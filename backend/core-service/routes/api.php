<?php

/**
 * API Routes
 *
 * This file defines the API routes for the application. The routes are grouped
 * and organized based on their functionality and associated controllers.
 *
 * Route Groups:
 * - `auth`: Handles authentication-related routes such as login, register, logout, etc.
 * - `student`: Manages student-related operations.
 * - Middleware-protected routes: Routes that require authentication (`auth:api` middleware).
 *
 * Middleware:
 * - `auth:api`: Ensures that the user is authenticated before accessing the route.
 *
 * Controllers:
 * Each route is associated with a specific controller method to handle the request.
 * Example: `AuthController`, `StudentController`, `UserController`, etc.
 *
 * Route Structure:
 * - `Route::prefix()`: Groups routes under a common prefix.
 * - `Route::apiResource()`: Automatically generates RESTful routes for a resource.
 * - Custom routes: Additional routes for specific functionalities.
 */

use App\Http\Controllers\ArticleController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\AssignmentSubmissionController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ClassroomController;
use App\Http\Controllers\ExamAttemptController;
use App\Http\Controllers\ExamController;
use App\Http\Controllers\ExamQuestionController;
use App\Http\Controllers\ExtracurricularController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\ParentController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\ScheduleController;
use App\Http\Controllers\SchedulePeriodController;
use App\Http\Controllers\AcademicYearController;
use App\Http\Controllers\CanteenAdminController;
use App\Http\Controllers\CanteenController;
use App\Http\Controllers\CashierController;
use App\Http\Controllers\ClassAttendanceController;
use App\Http\Controllers\ClassEnrollmentController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\DormitoryController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\ExamTokenController;
use App\Http\Controllers\StudentDailyActivityController;
use App\Http\Controllers\StudentDailyActivityReportController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\SubClassroomController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Export\TeacherTemplateController;
use App\Http\Controllers\Export\StudentTemplateController;
use App\Http\Controllers\Export\SubjectsTemplateController;
use App\Http\Controllers\Export\ParentTemplateController;
use App\Http\Controllers\ExtracurricularStudentController;
use App\Http\Controllers\FacilityController;
use App\Http\Controllers\FinancialCoreController;
use App\Http\Controllers\Export\FacilitiesTemplateController;
use App\Http\Controllers\FoundationController;
use App\Http\Controllers\GradeController;
use App\Http\Controllers\InformationController;
use App\Http\Controllers\LessonPlanController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\MidtransController;
use App\Http\Controllers\MobileInformationController;
use App\Http\Controllers\NfcCardController;
use App\Http\Controllers\PracticalTrainingController;
use App\Http\Controllers\PracticalTrainingDataController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProgramController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\PurchaseLogController;
use App\Http\Controllers\ReportCardController;
use App\Http\Controllers\RoomController;
use App\Http\Controllers\ScheduleActivityController;
use App\Http\Controllers\SchoolController;
use App\Http\Controllers\SchoolLevelController;
use App\Http\Controllers\StudentActivityReportController;
use App\Http\Controllers\SubjectEnrollmentController;
use App\Http\Controllers\TermController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\TuitionFeeController;
use App\Http\Controllers\TuitionInvoiceController;
use App\Http\Controllers\TuitionPaymentController;
use App\Models\ClassAttendance;

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/forgotPassword', [AuthController::class, 'sendResetLinkEmail']);
    Route::post('/resetPassword', [AuthController::class, 'resetPassword']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:api');
    Route::post('/refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
    Route::get('/profile', [AuthController::class, 'profile'])->middleware('auth:api');
    Route::get('/users', [AuthController::class, 'users'])->middleware('auth:api');
    Route::put('/update/{user}', [AuthController::class, 'update'])->middleware('auth:api');
    Route::put('/update', [AuthController::class, 'update'])->middleware('auth:api');
    Route::post('/changePassword', [AuthController::class, 'changePassword'])->middleware('auth:api');
    Route::post('/changePhotoProfile', [AuthController::class, 'changePhotoProfile'])->middleware('auth:api');
});

// Public student route
Route::post('/student', [StudentController::class, 'store']);

// Midtrans route
Route::group(['prefix' => 'midtrans'], function () {
    Route::post('/requestSnapToken', [MidtransController::class, 'requestSnapToken']);
    Route::post('/handleNotification', [MidtransController::class, 'handleNotification']);
});

// Protected routes (require authentication)
Route::middleware(['auth:api'])->group(function () {
    // Foundation routes
    Route::group(['prefix' => 'foundations'], function () {
        Route::get('/{foundation:id}/schoolCount', [FoundationController::class, 'schoolCount']);
        Route::get('/{foundation:id}/studentCount', [FoundationController::class, 'studentCount']);
        Route::get('/{foundation:id}/teacherCount', [FoundationController::class, 'teacherCount']);
        Route::get('/{foundation:id}/subClassroomCount', [FoundationController::class, 'subClassroomCount']);
        Route::get('/{foundation:id}/admins', [FoundationController::class, 'getAdmins']);
        Route::get('/{foundation:id}/schools', [FoundationController::class, 'getSchools']);
        Route::post('/{foundation:id}/addSchool', [FoundationController::class, 'addSchool']);
        Route::post('/{foundation:id}/assignAdmin', [FoundationController::class, 'assignAdmin']);
    });
    Route::apiResource('/foundations', FoundationController::class);

    // School routes
    Route::group(['prefix' => 'schools'], function () {
        Route::get('{school:id}/admins', [SchoolController::class, 'getAdmins']);
        Route::post('{school:id}/assignAdmin', [SchoolController::class, 'assignAdmin']);
        Route::put('{school}/update-by-admin', [SchoolController::class, 'updateByAdmin']);
    });
    Route::apiResource('/schools', SchoolController::class);

    Route::apiResource('/schoolLevels', SchoolLevelController::class);

    // User management routes
    Route::apiResource('/user', UserController::class);
    Route::put('/user/{user}/activate', [UserController::class, 'activate']);

    // Student and parent routes
    Route::group(['prefix' => 'student'], function () {
        Route::get('/count', [StudentController::class, 'count']);
        Route::get('/export', [StudentController::class, 'export']);
        Route::post('/import', [StudentController::class, 'import']);
        Route::get('/template', [StudentTemplateController::class, 'download']);
    });

    Route::apiResource('/student', StudentController::class)->except('store');

    Route::get('/parents', [ParentController::class, 'index']);
    Route::group(['prefix' => 'parents'], function () {
        Route::get('/template', ParentTemplateController::class);
        Route::post('/import', [ParentController::class, 'import']);
        Route::get('/getStudents', [ParentController::class, 'getStudents']);
        Route::post('/{userId}/students', [ParentController::class, 'addStudent']);
        Route::post('/link-student', [ParentController::class, 'linkStudent']);
    });

    // Student activity report routes
    Route::apiResource('/studentActivityReports', StudentActivityReportController::class);

    // Student daily activity routes
    Route::apiResource('/studentDailyActivity', StudentDailyActivityController::class);

    // Student daily activity report routes
    Route::group(['prefix' => '/studentDailyActivityReports'], function () {
        Route::get('/', [StudentDailyActivityReportController::class, 'index']);
        Route::put('/', [StudentDailyActivityReportController::class, 'update']);
    });

    // Teacher routes
    Route::group(['prefix' => 'teacher'], function () {
        Route::get('/count', [TeacherController::class, 'count']);
        Route::get('/export', [TeacherController::class, 'export']);
        Route::post('/import', [TeacherController::class, 'import']);
        Route::get('/template', [TeacherTemplateController::class, 'download']);
    });

    Route::apiResource('/teacher', TeacherController::class);

    // Article, category, and tag routes
    Route::apiResource('/article', ArticleController::class);
    Route::apiResource('/category', CategoryController::class);
    Route::apiResource('/tag', TagController::class);

    // Document routes
    Route::apiResource('/documents', DocumentController::class);

    // Classroom and sub-classroom routes
    Route::apiResource('/classroom', ClassroomController::class);
    Route::apiResource('/subClassroom', SubClassroomController::class)->except(['show']);
    Route::group(['prefix' => 'subClassroom'], function () {
        Route::get('/count', [SubClassroomController::class, 'count']);
        Route::get('/assignedSubClassroom', [SubClassroomController::class, 'getAssignedSubClassroom']);
        Route::post('/sync', [SubClassroomController::class, 'syncStudents']);

        Route::post('/{subClassroom:id}/assignStudents', [SubClassroomController::class, 'assignStudents']);
        Route::post('/{subClassroom:id}/assignTeacher', [SubClassroomController::class, 'assignTeacher']);
        Route::post('/{subClassroom:id}/assignSubjectTeacher', [SubClassroomController::class, 'assignSubjectTeacher']);
        Route::get('/{subClassroom:id}/students', [SubClassroomController::class, 'getStudents']);
        Route::get('/{subClassroom:id}/subjects', [SubClassroomController::class, 'getSubjects']);
        Route::get('/{subClassroom:id}/assignments', [SubClassroomController::class, 'getAssignments']);
    });

    // Subject routes
    Route::apiResource('/subject', SubjectController::class)->except('show');
    Route::group(['prefix' => 'subject'], function () {
        Route::get('/assignedSubjects', [SubjectController::class, 'getAssignedSubjects']);
        Route::get('/export', [SubjectController::class, 'export']);
        Route::post('/import', [SubjectController::class, 'import']);
        Route::get('/template', [SubjectsTemplateController::class, 'download']);
    });

    // Dormitory routes
    Route::apiResource('/dormitories', DormitoryController::class);
    Route::apiResource('/rooms', RoomController::class);

    // Assignment routes
    Route::apiResource('/assignments', AssignmentController::class);
    Route::group(['prefix' => 'assignments'], function () {
        Route::post('/{assignment:id}/submit', [AssignmentController::class, 'submit']);
        Route::get('/{assignment:id}/submissions', [AssignmentController::class, 'getSubmissions']);
    });

    // Assignment submission routes
    Route::apiResource('/assignmentSubmission', AssignmentSubmissionController::class);
    Route::group(['prefix' => 'assignmentSubmission'], function () {
        Route::post('/submitGrades', [AssignmentSubmissionController::class, 'submitGrades']);
    });

    // Attendance routes
    Route::group(['prefix' => 'attendance'], function () {
        Route::get('/exportPdf', [AttendanceController::class, 'exportPdf']);
        Route::get('/teacherAttendances', [AttendanceController::class, 'getTeacherAttendances']);

        Route::post('/batch', [AttendanceController::class, 'batchStore']);
        Route::get('/summary', [AttendanceController::class, 'getScheduleAttendanceSummary']);
        Route::get('/summary/students', [AttendanceController::class, 'attendanceSummaryForStudents']);
        Route::get('/summary/teachers', [AttendanceController::class, 'attendanceSummaryForTeachers']);
    });
    Route::apiResource('/attendance', AttendanceController::class);

    // Academic Year routes
    Route::apiResource('/academicYear', AcademicYearController::class);
    Route::apiResource('/terms', TermController::class);

    Route::prefix('/tuition')->group(function () {
        Route::apiResource('/fees', TuitionFeeController::class);
        Route::apiResource('/invoices', TuitionInvoiceController::class);

        Route::post('/payments/payWithMidtrans', [TuitionPaymentController::class, 'payWithMidtrans']);

        Route::apiResource('/payments', TuitionPaymentController::class);
        Route::post('/payments/{payment:id}/approve', [TuitionPaymentController::class, 'approve']);
        Route::post('/payments/{payment:id}/reject', [TuitionPaymentController::class, 'reject']);
    });

    // Financial Core routes - Central financial record system
    Route::prefix('/financial-core')->group(function () {
        Route::apiResource('/', FinancialCoreController::class)->parameters(['' => 'financialCore']);
    });

    // Schedule routes
    Route::apiResource('/schedules', ScheduleController::class);
    Route::get('/getCurrentSchedule', [ScheduleController::class, 'getCurrentSchedule']);
    Route::group(['prefix' => 'schedules'], function () {
        Route::get('/{schedule:id}/getStudentAttendances', [ScheduleController::class, 'getStudentAttendances']);
        Route::get('/{schedule:id}/getAttendanceStatus', [ScheduleController::class, 'getAttendanceStatus']);
    });

    // Schedule Period routes
    Route::apiResource('/schedulePeriod', SchedulePeriodController::class);
    Route::prefix('schedulePeriod')->group(function () {
        Route::post('increment', [SchedulePeriodController::class, 'increment']);
        Route::post('decrement', [SchedulePeriodController::class, 'decrement']);
    });

    // NFC Card routes
    Route::group(['prefix' => 'nfcCards'], function () {
        Route::get('/transactions', [NfcCardController::class, 'getAllTransactions']);
        Route::post('/transactions/{nfcCardTransaction:id}/approve', [NfcCardController::class, 'approveTransaction']);
        Route::post('/transactions/{nfcCardTransaction:id}/reject', [NfcCardController::class, 'rejectTransaction']);

        Route::get('/', [NfcCardController::class, 'index']);
        Route::post('/', [NfcCardController::class, 'store']);

        Route::get('/{nfcCard:uid}', [NfcCardController::class, 'show']);
        Route::get('/{nfcCard:uid}/user', [NfcCardController::class, 'getUserByUid']);
        Route::post('/{nfcCard:uid}/topUp', [NfcCardController::class, 'topUp']);
        Route::post('/{nfcCard:uid}/setDailyLimit', [NfcCardController::class, 'setDailyLimit']);
        Route::post('/{nfcCard:uid}/topUpWithMidtrans', [NfcCardController::class, 'topUpWithMidtrans']);
    });

    // Position and organization routes
    Route::apiResource('/position', PositionController::class);
    Route::apiResource('/organization', OrganizationController::class);

    // Extracurricular routes
    Route::apiResource('/extracurricular', ExtracurricularController::class)->except(['show']);
    Route::group(['prefix' => 'extracurricular'], function () {
        Route::get('/getAssigned', [ExtracurricularController::class, 'getAssigned']);
        Route::get('/{extracurricular:id}/students', [ExtracurricularController::class, 'getStudents']);

        Route::get('/{extracurricular:id}/getAttendances', [ExtracurricularController::class, 'getAttendances']);

        Route::post('/checkIn', [ExtracurricularController::class, 'checkIn']);
        Route::get('/checkIn/history', [ExtracurricularController::class, 'checkInHistory']);

        Route::get('/activities', [ExtracurricularController::class, 'getAllActivity']);
        Route::post('/submitActivity', [ExtracurricularController::class, 'submitActivity']);
        Route::put('/{activity:id}/approveActivity', [ExtracurricularController::class, 'approveActivity']);

        Route::post('/{extracurricular:id}/assign', [ExtracurricularController::class, 'assign']);
        Route::post('/{extracurricular:id}/unassign', [ExtracurricularController::class, 'unassign']);
        Route::post('/{extracurricular:id}/assignStudents', [ExtracurricularController::class, 'assignStudents']);
        Route::post('/{extracurricular:id}/unassignStudents', [ExtracurricularController::class, 'unassignStudents']);

        Route::get('/students',        [ExtracurricularStudentController::class, 'index']);
        Route::post('/students',        [ExtracurricularStudentController::class, 'store']);
        Route::put('/students/{id}',   [ExtracurricularStudentController::class, 'update']);
        Route::delete('/students/{id}',   [ExtracurricularStudentController::class, 'destroy']);
        Route::get('/classes/{subClassroomId}/students', [ExtracurricularStudentController::class, 'listStudentsByClass']);
    });

    Route::group(['prefix' => 'facilities'], function () {
        Route::get('/export', [FacilityController::class, 'export']);
        Route::post('/import', [FacilityController::class, 'import']);
        Route::get('/template', [FacilitiesTemplateController::class, 'download']);
    });
    Route::apiResource('/facilities', FacilityController::class);

    Route::group(['prefix' => 'canteens'], function () {
        Route::apiResource('/canteenAdmins', CanteenAdminController::class);
        Route::apiResource('/cashiers', CashierController::class);
        Route::apiResource('/transactions', TransactionController::class);
        Route::get('/transactions/{transaction:id}/items', [TransactionController::class, 'show']);
        Route::apiResource('/products', ProductController::class);
        Route::get('/purchases/logs', [PurchaseLogController::class, 'index']);
        Route::apiResource('/purchases', PurchaseController::class);
    });
    Route::apiResource('canteens', CanteenController::class);

    // Information routes
    Route::group(['prefix' => 'mobile'], function () {
        Route::get('/informations/{information:id}', [MobileInformationController::class, 'show']);
        Route::post('/informations/{information:id}/markAsRead', [MobileInformationController::class, 'markAsRead']);
    });

    Route::apiResource('informations', InformationController::class);

    // Schedule Actiivity
    Route::apiResource('/scheduleActivities', ScheduleActivityController::class);

    Route::group(['prefix' => 'events'], function () {
        Route::post('/{event:id}/restore', [EventController::class, 'restore']);
        Route::delete('/{event:id}/force', [EventController::class, 'forceDelete']);
    });

    Route::apiResource('/events', EventController::class);

    Route::group(['prefix' => 'lessonPlan'], function () {
        Route::post('/exportPdf/{lessonPlan:id}', [LessonPlanController::class, 'exportPdf']);
        Route::post('/learningMaterial/pdf', [LessonPlanController::class, 'exportLearningMaterial']);
    });
    Route::apiResource('lessonPlan', LessonPlanController::class);

    Route::get('/programs/exportPdf', [ProgramController::class, 'exportPdf']);

    Route::get('/programs/{program:id}/lessonPlan', [ProgramController::class, 'getLessonPlan']);
    Route::apiResource('programs', ProgramController::class);


    Route::apiResource('exams', ExamController::class);
    Route::group(['prefix' => 'exams'], function () {
        Route::post('/{exam:id}/publish', [ExamController::class, 'publish']);
        Route::post('/{exam:id}/unpublish', [ExamController::class, 'unpublish']);
        Route::post('/{exam:id}/questions-bulk', [ExamQuestionController::class, 'storeMultiple']);
        Route::post('/{exam:id}/start', [ExamController::class, 'start']);
        Route::post('/{exam:id}/submit', [ExamController::class, 'submit']);
        Route::post('/{exam:id}/grade', [ExamController::class, 'grade']);
        Route::get('/{exam:id}/results', [ExamController::class, 'showResult']);
        Route::get('/{exam:id}/count', [ExamController::class, 'countStudentExam']);

        Route::post('/{exam:id}/duplicate', [ExamController::class, 'duplicateExam']);
        Route::patch('/{exam:id}/assignTeacher', [ExamController::class, 'assignTeacher']);

        Route::post('/{exam:id}/student/{student:id}/reset', [ExamController::class, 'resetStudentExam']);
        Route::post('/{exam:id}/student/{student:id}/force-submit', [ExamController::class, 'forceSubmitStudentExam']);

        Route::get('/{exam}/students/{student}/attempts/{attempt}', [ExamController::class, 'getStudentAttempt']);

        Route::post('/{examAttemptId}/grade-essay', [ExamAttemptController::class, 'gradeEssay']);

        Route::post('/export/questions-card', [ExamController::class, 'download']);
        Route::post('/export/questions-grid', [ExamController::class, 'downloadGrid']);

        Route::get('/questions/template', [ExamQuestionController::class, 'downloadTemplateQuestion'])
            ->name('exams.questions.template');
        Route::post('/{exam}/questions/import', [ExamQuestionController::class, 'importQuestions'])
            ->name('exams.questions.import');

        Route::get('/questions/copy/sources', [ExamQuestionController::class, 'listSourceExams']);
        Route::get('/questions/copy/sources/{sourceExam}/questions', [ExamQuestionController::class, 'listQuestionsOfSource']);

        Route::post('/{exam}/questions/copyFrom', [ExamQuestionController::class, 'copyFromExam']);
    });

    Route::apiResource('exams.questions', ExamQuestionController::class)
        ->parameters([
            'questions' => 'examQuestion'
        ]);

    Route::apiResource('exam-sessions', ExamAttemptController::class)
        ->only(['show'])
        ->parameters([
            'exam-sessions' => 'examAttempt'
        ]);
    Route::group(['prefix' => 'exam-sessions'], function () {
        Route::get('/{examAttempt}/questions/{examAttemptAnswer}', [ExamAttemptController::class, 'showQuestion']);
        Route::post('/{examAttempt}/questions/{examAttemptAnswer}/answer', [ExamAttemptController::class, 'answer']);
    });

    Route::group(['prefix' => 'ExamToken'], function () {
        Route::post('/generate-token/{userId}', [ExamTokenController::class, 'generateExamTokenForStudent']);
        Route::post('/generate-token-all', [ExamTokenController::class, 'generateExamTokenForAllStudent']);
    });

    Route::apiResource('/grades', GradeController::class);

    Route::group(['prefix' => 'reportCards'], function () {
        Route::post('/{reportCardId}/grades/revision-score', [ReportCardController::class, 'revisionScore']);
        Route::put('/grades/assignments', [ReportCardController::class, 'assignmentsUpsertSmart']);
        Route::put('/{reportCard}/grades/assignments', [ReportCardController::class, 'assignmentsUpsert']);

        Route::post('/upsert-note', [ReportCardController::class, 'upsertNoteByKeys']);
        Route::post('/import-notes', [ReportCardController::class, 'importNotes']);
        Route::get('/notes-template', [ReportCardController::class, 'notesTemplate']);

        Route::get('/{term}/students/{student}/report-download', [ReportCardController::class, 'reportDownload'])
            ->name('reportCards.reportDownload');
        Route::get('/student/{student}/report-cover', [ReportCardController::class, 'reportCoverDownload'])
            ->name('reportCards.reportCoverDownload');

        Route::get('/terms/{term}/students/{student}/report-card/class-teacher-note', [ReportCardController::class, 'showClassTeacherNote']);
        Route::patch('/terms/{term}/students/{student}/report-card/class-teacher-note', [ReportCardController::class, 'updateClassTeacherNote']);
    });
    Route::apiResource('/reportCards', ReportCardController::class);

    Route::group(['prefix' => 'subjectEnrollment'], function () {
        Route::patch('/enrollments', [SubjectEnrollmentController::class, 'updateSingle']);
        Route::get('/enrollments/matrix', [SubjectEnrollmentController::class, 'matrix']);
        Route::put('/enrollments/bulk', [SubjectEnrollmentController::class, 'bulkUpsert']);
    });

    Route::group(['prefix' => 'classEnrollment'], function () {
        Route::get('/{subClassroom}/enrollments/summary', [ClassEnrollmentController::class, 'summary']);
        Route::put('/{subClassroom}/enrollments/sync', [ClassEnrollmentController::class, 'syncMatrix']);
        Route::post('/{subClassroom}/enrollments/defaults', [ClassEnrollmentController::class, 'setDefault']);
        Route::put('/{subClassroom}/subjects/{subClassroomSubject}/enroll-all', [ClassEnrollmentController::class, 'enrollAllForSubject']);
        Route::put('/{subClassroom}/students/{student}/enroll-all-subjects', [ClassEnrollmentController::class, 'enrollAllForStudent']);
    });

    Route::prefix('classAttendance')->group(function () {
        Route::post('/upsert', [ClassAttendanceController::class, 'upsert']);
        Route::post('/bulk-append', [ClassAttendanceController::class, 'bulkAppend']);
        Route::patch('/{id}/adjust', [ClassAttendanceController::class, 'adjust']);
        Route::get('/template', [ClassAttendanceController::class, 'downloadTemplate']);
        Route::post('/import', [ClassAttendanceController::class, 'import']);
    });
    Route::apiResource('/classAttendance', ClassAttendanceController::class);

    Route::prefix('practicalTrainingData')->group(function () {
        Route::get('import/template', [PracticalTrainingDataController::class, 'downloadTemplate']);
        Route::post('import', [PracticalTrainingDataController::class, 'import']);
    });
    Route::apiResource('practicalTrainingData', PracticalTrainingDataController::class)
        ->parameters(['practicalTrainingData' => 'practicalTrainingData']);

    Route::get('/practicalTraining/{practicalTraining}/downloadReport', [PracticalTrainingController::class, 'downloadReportPracticalTraining']);
    Route::apiResource('/practicalTraining', PracticalTrainingController::class);
});
