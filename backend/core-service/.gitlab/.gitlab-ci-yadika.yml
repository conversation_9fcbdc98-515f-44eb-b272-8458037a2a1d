stages:
  - build_yadika
  - deploy_yadika

build_yadika:
  stage: build_yadika
  only:
    - yadika
  image: docker:stable
  services:
    - name: docker:dind
      alias: thedockerhost
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
    APP_NAME: yadika
    PROJECT: tias
    CONTAINER_IMAGE: registry.ti-asia.com/$PROJECT/$APP_NAME
  tags:
    - gcprunner
  before_script:
    - docker login -u $HARBOR_USERNAME -p $HARBOR_PASSWORD $HARBOR_URL
  script:
    - docker rmi -f $CONTAINER_IMAGE:prod || true
    - docker build --no-cache -t $CONTAINER_IMAGE:$CI_COMMIT_SHA .
    - docker tag $CONTAINER_IMAGE:$CI_COMMIT_SHA $CONTAINER_IMAGE:prod
    - docker push $CONTAINER_IMAGE:prod

deploy_yadika:
  stage: deploy_yadika
  only:
    - yadika
  tags:
    - gcprunner
  before_script:
    - mkdir -p ~/.ssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY_PRODUCTION" | base64 -d > ~/.ssh/cicdkubetiacom
    - chmod 600 ~/.ssh/cicdkubetiacom
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/cicdkubetiacom
  script:
    - |
      ssh -i ~/.ssh/cicdkubetiacom -o StrictHostKeyChecking=no cicd@"$IP_PRODUCTION" << 'EOF'
      cd "$PATH_KUBECONFIG"
      kubectl --kubeconfig=kubeconfig.yaml rollout restart deployment tias-backend -n tias-yadika
  when: manual