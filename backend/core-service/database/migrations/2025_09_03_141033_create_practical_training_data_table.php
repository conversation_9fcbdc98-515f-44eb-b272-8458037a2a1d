<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('practical_training_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('practical_training_id')->nullable()->onDelete('set null');
            $table->string('learning_objective');
            $table->decimal('final_score', 5, 2);
            $table->string('description');
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('practical_training_data');
    }
};
