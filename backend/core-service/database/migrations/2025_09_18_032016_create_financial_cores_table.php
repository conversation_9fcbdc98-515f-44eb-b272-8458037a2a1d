<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_cores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('student_user_id')->nullable()->onDelete('cascade');
            $table->foreignId('parent_user_id')->nullable()->onDelete('cascade');
            $table->string('transaction_number')->unique();
            $table->string('reference_type')->comment('Type: tuition, canteen, cashless, others');
            $table->string('reference_id')->nullable()->comment('ID of the related transaction');
            $table->decimal('debit_amount', 15, 2)->default(0)->comment('Debit amount in rupiah');
            $table->decimal('credit_amount', 15, 2)->default(0)->comment('Credit amount in rupiah');
            $table->text('notes')->nullable()->comment('Additional notes or description');
            $table->string('status')->default('completed')->comment('Transaction status');
            $table->timestamp('transaction_date', 6)->useCurrent()->comment('Transaction date with microsecond precision');
            $table->time('transaction_time', 6)->nullable()->comment('Specific transaction time (HH:MM:SS.microseconds)');
            $table->string('transaction_timezone')->default('Asia/Jakarta')->comment('Transaction timezone');
            $table->timestamp('created_at', 6)->useCurrent()->comment('Record creation time with microsecond precision');
            $table->timestamp('updated_at', 6)->useCurrent()->useCurrentOnUpdate()->comment('Record update time with microsecond precision');
            $table->timestamp('deleted_at', 6)->nullable()->comment('Record deletion time with microsecond precision');
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
            
            $table->index(['school_id', 'student_user_id']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('transaction_date');
            $table->index(['transaction_date', 'transaction_time']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_cores');
    }
};
