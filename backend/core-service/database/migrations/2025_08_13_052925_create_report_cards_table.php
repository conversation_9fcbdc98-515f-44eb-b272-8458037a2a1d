<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('term_id')->nullable()->onDelete('set null');
            $table->foreignId('student_user_id')->nullable()->onDelete('set null');
            $table->foreignId('sub_classroom_subject_id')->nullable()->onDelete('set null');
            $table->decimal('final_score', 5, 2)->nullable();
            $table->decimal('revision_score', 5, 2)->nullable();
            $table->string('grade')->nullable();

            $table->string('mid_term_note')->nullable();
            $table->timestamp('mid_exported_at')->nullable();
            $table->decimal('mid_locked_score', 5, 2)->nullable();

            $table->string('final_term_note')->nullable();
            $table->timestamp('final_exported_at')->nullable();
            $table->decimal('final_locked_score', 5, 2)->nullable();

            $table->string('class_teacher_note')->nullable();

            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_cards');
    }
};
