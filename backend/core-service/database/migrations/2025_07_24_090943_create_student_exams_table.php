<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sub_classroom_subject_has_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('sub_classroom_subject_id')
                ->constrained('sub_classroom_subjects')
                ->onDelete('cascade');
            $table->foreignId('user_id')
                ->constrained('users')
                ->onDelete('cascade');
            $table->foreignId('term_id')
                ->constrained('terms')
                ->onDelete('cascade');

            $table->boolean('is_enrolled')->default(true);
            $table->timestamp('enrolled_at')->nullable();
            $table->timestamp('unenrolled_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');

            $table->unique(['term_id', 'sub_classroom_subject_id', 'user_id'], 'uniq_term_subject_user');
            $table->index(['term_id', 'user_id'], 'idx_term_user');
            $table->index(['term_id', 'sub_classroom_subject_id'], 'idx_term_subject');
        });

        Schema::create('exam_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_id')
                ->constrained('exams')
                ->onDelete('cascade');
            $table->foreignId('user_id')
                ->constrained('users')
                ->onDelete('cascade');
            $table->datetime('start_datetime');
            $table->datetime('submit_datetime')->nullable();
            $table->datetime('graded_at')->nullable();
            $table->decimal('score', 5, 2)->nullable();
            $table->integer('correct_count')->nullable();
            $table->enum('status', ['in_progress', 'completed', 'graded'])
                ->default('in_progress');
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });

        Schema::create('exam_attempt_answers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_attempt_id')
                ->constrained('exam_attempts')
                ->onDelete('cascade');
            $table->foreignId('exam_question_id')
                ->constrained('exam_questions')
                ->onDelete('cascade');
            $table->foreignId('selected_option_id')
                ->nullable()
                ->constrained('exam_question_has_answer_options')
                ->onDelete('cascade');
            $table->text('essay_answer')->nullable();
            $table->text('media_answer')->nullable();
            $table->decimal('points_awarded', 5, 2)->nullable();
            $table->integer('order_index');
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_attempt_answers');
        Schema::dropIfExists('exam_attempts');
        Schema::dropIfExists('sub_classroom_subject_has_students');
    }
};
