<?php

use App\Enums\QuestionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exams', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->string('title', 255);
            $table->text('description')->nullable();
            $table->string('exams_type')->nullable();
            $table->foreignId('sub_classroom_subject_id')->nullable();

            $table->foreignId('term_id')->nullable()->onDelete('cascade');

            $table->dateTime('start_datetime')->nullable();
            $table->dateTime('end_datetime')->nullable();
            $table->decimal('passing_score', 5, 2)->nullable();
            $table->boolean('is_published')->default(false);
            $table->boolean('is_shuffled')->default(false);

            $table->index(['term_id']);

            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });

        Schema::create('exam_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_id')->onDelete('cascade');
            $table->string('question_type')->nullable(); // QuestionType::class
            $table->text('content');
            $table->text('answer_key_essay')->nullable();
            $table->decimal('points', 5, 2)->default(1.00);
            $table->integer('order_index');
            $table->text('media_questions')->nullable();

            $table->text('kd_number')->nullable();               // Nomor Kompetensi Dasar
            $table->text('learning_outcome')->nullable();              // Isi Kompetensi dasar/ capaian pembelajaran/elemen
            $table->text('competency_indicator')->nullable();          // Indikator pencapaian kompetensi
            $table->string('level_kognitif')->nullable();                // level kogtif

            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });

        Schema::create('exam_question_has_answer_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_question_id')->constrained()->onDelete('cascade');
            $table->text('content');
            $table->boolean('is_correct')->default(false);
            $table->integer('order_index');
            $table->text('media_answer_options')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_question_has_answer_options');
        Schema::dropIfExists('exam_questions');
        Schema::dropIfExists('exams');
    }
};
