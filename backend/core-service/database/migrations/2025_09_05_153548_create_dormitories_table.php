<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dormitories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('guardian_user_id')->nullable()->onDelete('cascade');
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dormitories');
    }
};
