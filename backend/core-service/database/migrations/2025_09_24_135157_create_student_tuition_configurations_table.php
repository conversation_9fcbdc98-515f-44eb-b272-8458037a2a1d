<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_tuition_configurations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id');
            $table->unsignedBigInteger('academic_year_id');
            $table->unsignedBigInteger('school_id');
            $table->decimal('monthly_amount', 15, 2);
            $table->integer('installment_count')->default(12);
            $table->date('start_date');
            $table->date('due_date');
            $table->decimal('late_fee_amount', 15, 2)->default(0);
            $table->integer('late_fee_grace_days')->default(0);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->json('custom_installments')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['student_id', 'academic_year_id']);
            $table->index(['school_id', 'academic_year_id']);
            $table->index('is_active');

            // Foreign keys
            $table->foreign('student_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');

            // Unique constraint to prevent duplicate configurations per student per academic year
            $table->unique(['student_id', 'academic_year_id'], 'unique_student_academic_year');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_tuition_configurations');
    }
};
