<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('practical_trainings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('term_id')->nullable()->onDelete('set null');
            $table->foreignId('student_user_id')->nullable()->onDelete('set null');
            $table->string('program_of_study')->nullable(); // Program Keahlian
            $table->string('specialization')->nullable(); // Konsentrasi <PERSON>ahlian
            $table->string('place'); // Tempat PKL
            $table->string('period_start');
            $table->string('period_end');
            $table->string('instructor_name'); // Nama Instruktur
            $table->string('mentor_name'); // Nama Pembimbing

            $table->integer('sick')->default(0);
            $table->integer('leave')->default(0);
            $table->integer('present')->default(0);
            $table->integer('alpha')->default(0);

            $table->string('class_teacher_note');

            $table->unique(['term_id', 'student_user_id']);
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('practical_trainings');
    }
};
