<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('extracurricular_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('extracurricular_id')->onDelete('cascade');
            $table->foreignId('student_user_id')->onDelete('cascade');
            $table->foreignId('term_id')->nullable()->onDelete('cascade');
            $table->string('predicate', 2)->nullable(); // SB | B | C | K
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');

            $table->unique(['extracurricular_id', 'student_user_id', 'term_id'], 'uniq_exkul_student_term');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('extracurricular_students');
    }
};
