<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->nullable()->onDelete('cascade');
            $table->foreignId('academic_year_id')->nullable()->onDelete('cascade'); // untuk sementara ga pap
            $table->foreignId('term_id')->nullable()->onDelete('cascade');
            $table->foreignId('subject_id')->onDelete('cascade');
            $table->foreignId('sub_classroom_id')->onDelete('cascade');
            $table->string('title');
            $table->string('description')->nullable();
            $table->dateTimeTz('due_date');
            $table->string('file')->nullable();
            $table->string('status')->default('pending');
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignments');
    }
};
