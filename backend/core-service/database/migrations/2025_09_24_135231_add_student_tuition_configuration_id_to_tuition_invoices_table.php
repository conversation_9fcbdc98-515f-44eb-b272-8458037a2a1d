<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tuition_invoices', function (Blueprint $table) {
            $table->unsignedBigInteger('student_tuition_configuration_id')->nullable()->after('student_id');
            $table->index('student_tuition_configuration_id');
            $table->foreign('student_tuition_configuration_id')->references('id')->on('student_tuition_configurations')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tuition_invoices', function (Blueprint $table) {
            $table->dropForeign(['student_tuition_configuration_id']);
            $table->dropIndex(['student_tuition_configuration_id']);
            $table->dropColumn('student_tuition_configuration_id');
        });
    }
};
