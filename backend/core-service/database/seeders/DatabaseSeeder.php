<?php

namespace Database\Seeders;

use Database\Seeders\LessonPlanSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SchoolLevelSeeder::class,
            SchoolSeeder::class,
            RoleSeeder::class,
            AcademicYearSeeder::class,
            ArticleSeeder::class,
            ClassroomSeeder::class,
            SubClassroomSeeder::class,
            AssignmentSeeder::class,
            NotificationSeeder::class,
            SubjectSeeder::class,
            AssignmentSubmissionSeeder::class,
            TagSeeder::class,
            AttendanceSeeder::class,
            ExtracurricularSeeder::class,
            TeacherSeeder::class,
            UserSeeder::class,
            ParentSeeder::class,
            InformationSeeder::class,
            CategorySeeder::class,
            StudentSeeder::class,
            SchedulePeriodSeeder::class,
            StudentDailyActivitySeeder::class,
            TuitionFeeSeeder::class,
            FacilitySeeder::class,
            UserSchoolRoleSeeder::class,
            SubClassroomSubjectSeeder::class,
            ScheduleSeeder::class,
            CanteenSeeder::class,
            CanteenAdminSeeder::class,
            CashierSeeder::class,
            ProductSeeder::class,
            PurchaseSeeder::class,
            TransactionSeeder::class,
            PositionSeeder::class,
            FoundationSeeder::class,
            EventSeeder::class,
            ProgramSeeder::class,
            LessonPlanSeeder::class,
            SystemConfigSeeder::class,
            // GradeSeeder::class,
            DormitorySeeder::class,
            RoomSeeder::class,
        ]);
    }
}
