<?php

namespace Database\Seeders;

use App\Models\School;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ParentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if parents already exist to avoid duplicate seeding
        $existingParentsCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'parent');
        })->count();

        if ($existingParentsCount > 0) {
            $this->command->info("Parents already exist ({$existingParentsCount} found). Skipping parent seeding...");
            return;
        }

        $this->command->info('Creating predefined parents...');

        $data = [
            [
                'name' => 'Nurul <PERSON> Tanjung',
                'phone' => '081234567897',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Sugeng Hary Purnomo',
                'phone' => '081234567898',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
        ];

        $this->createPredefinedParents($data);
        $this->createRandomParents();
    }

    /**
     * Create predefined parents with specific data
     */
    private function createPredefinedParents(array $parentsData): void
    {
        foreach ($parentsData as $parentData) {
            // Check if parent already exists by email or phone
            $existingParent = User::where('email', $parentData['email'])
                ->orWhere('phone', $parentData['phone'])
                ->first();

            if ($existingParent) {
                $this->command->info("Parent {$parentData['name']} ({$parentData['email']}) already exists. Skipping...");
                continue;
            }

            try {
                $parent = User::create([
                    'name' => $parentData['name'],
                    'phone' => $parentData['phone'],
                    'email' => $parentData['email'],
                    'is_active' => $parentData['is_active'],
                ]);

                $parent->assignSchoolRole(School::find(4), 'parent');
                $this->command->info("Created parent: {$parentData['name']}");
            } catch (\Illuminate\Database\QueryException $e) {
                if (str_contains($e->getMessage(), 'unique constraint') || str_contains($e->getMessage(), 'Duplicate entry')) {
                    $this->command->warn("Duplicate detected for parent {$parentData['name']}, skipping...");
                    continue;
                }
                throw $e;
            }
        }
    }

    /**
     * Create random parents using factory
     */
    private function createRandomParents(): void
    {
        $targetCount = 100;
        $existingRandomParentsCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'parent');
        })->count();

        // Subtract predefined parents (2) from existing count
        $predefinedCount = 2;
        $randomParentsToCreate = max(0, $targetCount - max(0, $existingRandomParentsCount - $predefinedCount));

        if ($randomParentsToCreate === 0) {
            $this->command->info("Already have enough random parents. Skipping random parent creation...");
            return;
        }

        $this->command->info("Creating {$randomParentsToCreate} random parents...");

        $createdCount = 0;
        $attempts = 0;
        $maxAttempts = $randomParentsToCreate * 3; // Allow some retries

        while ($createdCount < $randomParentsToCreate && $attempts < $maxAttempts) {
            $attempts++;

            try {
                $parent = User::factory()->create();
                $parent->assignSchoolRole(School::find(4), 'parent');
                $createdCount++;

                if ($createdCount % 10 === 0) {
                    $this->command->info("Created {$createdCount}/{$randomParentsToCreate} random parents...");
                }
            } catch (\Illuminate\Database\QueryException $e) {
                if (str_contains($e->getMessage(), 'unique constraint') || str_contains($e->getMessage(), 'Duplicate entry')) {
                    $this->command->warn("Duplicate detected, retrying... (attempt {$attempts})");
                    continue;
                }
                throw $e;
            }
        }

        if ($createdCount < $randomParentsToCreate) {
            $this->command->warn("Only created {$createdCount} out of {$randomParentsToCreate} random parents due to unique constraints");
        } else {
            $this->command->info("Successfully created {$createdCount} random parents");
        }
    }
}
