<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubClassroomSubject;
use App\Models\Schedule;
use App\Models\School;

class ScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school = School::find(4);
        $academicYear = $school->academicYears->first();
        $schedulePeriods = $school->schedulePeriods;
        $subjects = $school->subjects;
        $teachers = $school->teachers;
        $subClassrooms = $school->subClassrooms;

        foreach ($subClassrooms as $subClassroom) {
            // Loop through all days (1-7 for Monday-Sunday)
            for ($day = 1; $day <= 7; $day++) {
                // Get available periods for this day (shuffle for variety)
                $availablePeriods = $schedulePeriods->shuffle();

                // Get subjects for this subclassroom (randomize order for variety)
                $subClassroomSubjects = $subjects->shuffle()->take(min($subjects->count(), $availablePeriods->count()));

                foreach ($subClassroomSubjects as $index => $subject) {
                    // Skip if we run out of available periods for this day
                    if ($index >= $availablePeriods->count()) {
                        break;
                    }

                    $teacher = $teachers->random();
                    $period = $availablePeriods[$index];

                    // Create or get SubClassroomSubject
                    $scs = SubClassroomSubject::firstOrCreate([
                        'sub_classroom_id' => $subClassroom->id,
                        'subject_id' => $subject->id,
                    ], [
                        'teacher_user_id' => $teacher->id,
                    ]);

                    // Check if schedule already exists for this combination
                    $existingSchedule = Schedule::where([
                        'academic_year_id' => $academicYear->id,
                        'day' => $day,
                        'schedule_period_id' => $period->id,
                        'sub_classroom_subject_id' => $scs->id
                    ])->first();

                    // Create schedule if it doesn't exist
                    if (!$existingSchedule) {
                        Schedule::create([
                            'school_id' => $school->id,
                            'academic_year_id' => $academicYear->id,
                            'day' => $day,
                            'schedule_period_id' => $period->id,
                            'sub_classroom_subject_id' => $scs->id,
                        ]);
                    }
                }
            }
        }
    }
}
