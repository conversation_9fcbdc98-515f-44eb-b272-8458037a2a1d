<?php

namespace Database\Seeders;

use App\Models\SubClassroom;
use App\Models\User;
use Illuminate\Database\Seeder;

class TeacherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if teachers already exist to avoid duplicate seeding
        $existingTeachersCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'teacher');
        })->count();

        if ($existingTeachersCount > 0) {
            $this->command->info("Teachers already exist ({$existingTeachersCount} found). Skipping teacher seeding...");
            return;
        }

        $this->command->info('Creating predefined teachers...');

        $data = [
            [
                'name' => '<PERSON>',
                'registration_number' => '01.234.567.890',
                'email' => '<EMAIL>',
                'phone' => '081234567894',
                'is_active' => true,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'registration_number' => '01.234.567.891',
                'email' => '<EMAIL>',
                'phone' => '081234567895',
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'registration_number' => '01.234.567.892',
                'email' => '<EMAIL>',
                'phone' => '081234567896',
                'is_active' => false,
            ],
        ];

        $this->createPredefinedTeachers($data);

        // Create teacher assigned to each sub-classroom
        // $this->createSubClassroomTeachers();

        // Create random teachers
        $this->createRandomTeachers();
    }

    /**
     * Create predefined teachers with specific data
     */
    private function createPredefinedTeachers(array $teachersData): void
    {
        foreach ($teachersData as $teacherData) {
            // Check if teacher already exists by email or registration number
            $existingTeacher = User::where('email', $teacherData['email'])
                ->orWhere('registration_number', $teacherData['registration_number'])
                ->first();

            if ($existingTeacher) {
                $this->command->info("Teacher {$teacherData['name']} ({$teacherData['email']}) already exists. Skipping...");
                continue;
            }

            try {
                $teacher = User::create([
                    'name' => $teacherData['name'],
                    'phone' => $teacherData['phone'],
                    'email' => $teacherData['email'],
                    'is_active' => $teacherData['is_active'],
                    'registration_number' => $teacherData['registration_number'],
                ]);

                $teacher->assignRole('teacher');
                $this->command->info("Created teacher: {$teacherData['name']}");
            } catch (\Illuminate\Database\QueryException $e) {
                if (str_contains($e->getMessage(), 'unique constraint') || str_contains($e->getMessage(), 'Duplicate entry')) {
                    $this->command->warn("Duplicate detected for teacher {$teacherData['name']}, skipping...");
                    continue;
                }
                throw $e;
            }
        }
    }

    /**
     * Create teachers assigned to each sub-classroom
     */
    private function createSubClassroomTeachers(): void
    {
        $subClassrooms = SubClassroom::all();

        if ($subClassrooms->isEmpty()) {
            $this->command->warn('No sub-classrooms found. Skipping sub-classroom teacher creation...');
            return;
        }

        $this->command->info('Creating teachers for each sub-classroom...');

        foreach ($subClassrooms as $subClassroom) {
            // Check if sub-classroom already has a teacher
            $existingTeacher = User::where('sub_classroom_id', $subClassroom->id)
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'teacher');
                })->first();

            if ($existingTeacher) {
                $this->command->info("Sub-classroom {$subClassroom->fullName} already has a teacher ({$existingTeacher->name}). Skipping...");
                continue;
            }

            $attempts = 0;
            $maxAttempts = 5;
            $created = false;

            while (!$created && $attempts < $maxAttempts) {
                $attempts++;

                try {
                    $teacher = User::factory()
                        ->teacher(['sub_classroom_id' => $subClassroom->id])
                        ->create();

                    $this->command->info("Created teacher for sub-classroom {$subClassroom->fullName}: {$teacher->name}");
                    $created = true;
                } catch (\Illuminate\Database\QueryException $e) {
                    if (str_contains($e->getMessage(), 'unique constraint') || str_contains($e->getMessage(), 'Duplicate entry')) {
                        $this->command->warn("Duplicate detected for sub-classroom {$subClassroom->fullName}, retrying... (attempt {$attempts})");
                        continue;
                    }
                    throw $e;
                }
            }

            if (!$created) {
                $this->command->error("Failed to create teacher for sub-classroom {$subClassroom->fullName} after {$maxAttempts} attempts");
            }
        }
    }

    /**
     * Create random teachers
     */
    private function createRandomTeachers(): void
    {
        $targetCount = 20;
        $existingRandomTeachersCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'teacher');
        })->whereNull('sub_classroom_id')->count();

        $teachersToCreate = max(0, $targetCount - $existingRandomTeachersCount);

        if ($teachersToCreate === 0) {
            $this->command->info("Already have {$existingRandomTeachersCount} random teachers. Skipping random teacher creation...");
            return;
        }

        $this->command->info("Creating {$teachersToCreate} random teachers...");

        $createdCount = 0;
        $attempts = 0;
        $maxAttempts = $teachersToCreate * 3;

        while ($createdCount < $teachersToCreate && $attempts < $maxAttempts) {
            $attempts++;

            try {
                $teacher = User::factory()->teacher()->create();
                $createdCount++;

                if ($createdCount % 5 === 0) {
                    $this->command->info("Created {$createdCount}/{$teachersToCreate} random teachers...");
                }
            } catch (\Illuminate\Database\QueryException $e) {
                if (str_contains($e->getMessage(), 'unique constraint') || str_contains($e->getMessage(), 'Duplicate entry')) {
                    $this->command->warn("Duplicate detected, retrying... (attempt {$attempts})");
                    continue;
                }
                throw $e;
            }
        }

        if ($createdCount < $teachersToCreate) {
            $this->command->warn("Only created {$createdCount} out of {$teachersToCreate} random teachers due to unique constraints");
        } else {
            $this->command->info("Successfully created {$createdCount} random teachers");
        }
    }
}
