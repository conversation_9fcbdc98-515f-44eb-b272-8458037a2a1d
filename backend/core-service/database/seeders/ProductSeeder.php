<?php

namespace Database\Seeders;

use App\Models\Canteen;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Cleaning up existing products and images...');

        $existingProductImages = Product::pluck('image')->toArray();
        Product::query()->delete();
        foreach ($existingProductImages as $imagePath) {
            if (Storage::exists($imagePath)) {
                Storage::delete($imagePath);
            }
        }
        Storage::deleteDirectory('photoProduct');
        Storage::makeDirectory('photoProduct');
        $this->command->info('Existing products and images cleaned.');
        $canteenIds = Canteen::pluck('id');
        if ($canteenIds->isEmpty()) {
            $this->command->warn('Tidak ada data kantin. Seeder product dilewati.');
            return;
        }

        $products = [
            [
                'name' => 'Nasi Goreng',
                'image' => '',
                'price' => 15000,
                'stock' => 30,
            ],
            [
                'name' => 'Mie Ayam Bakso',
                'image' => '',
                'price' => 20000,
                'stock' => 30,
            ],
            [
                'name' => 'Es Teh Manis',
                'image' => '',
                'price' => 5000,
                'stock' => 50,
            ],
            [
                'name' => 'Roti Coklat',
                'image' => '',
                'price' => 7000,
                'stock' => 40,
            ],
        ];

        foreach ($canteenIds as $canteenId) {
            foreach ($products as $product) {
                $imageUrl = $product['image'];

                try {
                    $imageContents = Http::get($imageUrl)->body();
                    $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                    $filename = 'product_' . Str::random(10) . '.' . $extension;
                    Storage::put("photoProduct/{$filename}", $imageContents);

                    Product::create([
                        'canteen_id' => $canteenId,
                        'name'       => $product['name'],
                        'image'      => "photoProduct/{$filename}" ?? null,
                        'price'      => $product['price'],
                        'stock'      => $product['stock'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                } catch (\Exception $e) {
                    $this->command->error("Failed to download image or create product for {$product['name']}: " . $e->getMessage());
                }
            }
        }
        $this->command->info('Product seeding completed.');
    }
}
