<?php

namespace Database\Seeders;

use App\Models\Facility;
use Illuminate\Database\Seeder;

class FacilitySeeder extends Seeder
{
    public function run(): void
    {
        Facility::truncate();
        $school_id = 4;
        $data = [
            [
                'school_id' => $school_id,
                'name' => 'Sabun Pembersin Lantai',
                'stock' => 2,
                'unit' => 'Buah',
                'good_condition' => 2,
                'minor_damage' => 0,
                'major_damage' => 0,
            ],
            [
                'school_id' => $school_id,
                'name' => 'Sabun Cuci Tang<PERSON>',
                'stock' => 2,
                'unit' => 'Buah',
                'good_condition' => 2,
                'minor_damage' => 0,
                'major_damage' => 0,
            ],
            [
                'school_id' => $school_id,
                'name' => 'Alat Tulis Kantor (Atk)',
                'stock' => 20,
                'unit' => 'Lusin',
                'good_condition' => 20,
                'minor_damage' => 0,
                'major_damage' => 0,
            ],
            [
                'school_id' => $school_id,
                'name' => 'Kertas A4',
                'stock' => 2,
                'unit' => 'Rim',
                'good_condition' => 2,
                'minor_damage' => 0,
                'major_damage' => 0,
            ],
            [
                'school_id' => $school_id,
                'name' => 'Spidol',
                'stock' => 2,
                'unit' => 'Lusin',
                'good_condition' => 2,
                'minor_damage' => 0,
                'major_damage' => 0,
            ],
        ];

        Facility::insert($data);
    }
}
