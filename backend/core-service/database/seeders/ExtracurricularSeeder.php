<?php

namespace Database\Seeders;

use App\Models\Extracurricular;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExtracurricularSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
        ];

        foreach ($data as $d) {
            Extracurricular::create(['school_id' => 4, 'name' => $d]);
        }
    }
}
