<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use App\Models\Term;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AcademicYearSeeder extends Seeder
{
    /**
     * Buat tahun ajaran + semester (terms) secara otomatis.
     * - Idempotent: pakai updateOrCreate / pengecekan existing
     * - is_current di-set hanya untuk tahun aktif terbaru
     */
    public function run(): void
    {
        DB::transaction(function () {
            $schoolId = 4;

            $this->createYearWithTerms(
                schoolId: $schoolId,
                name: '2024/2025',
                start: '2024-07-01',
                end: '2025-06-30',
                isCurrent: false
            );

            $this->createYearWithTerms(
                schoolId: $schoolId,
                name: '2025/2026',
                start: '2025-07-01',
                end: '2026-06-30',
                isCurrent: true
            );

            $this->ensureSingleCurrentYear($schoolId);
        });
    }

    /**
     * Helper untuk membuat tahun ajaran + dua semester (dibagi 2 range).
     */
    private function createYearWithTerms(?int $schoolId, string $name, string $start, string $end, bool $isCurrent): void
    {
        $year = AcademicYear::query()
            ->where('name', $name)
            ->when($schoolId, fn($q) => $q->where('school_id', $schoolId))
            ->first();

        if (!$year) {
            $year = AcademicYear::create([
                'school_id'  => $schoolId,
                'name'       => $name,
                'start_date' => $start,
                'end_date'   => $end,
                'is_current' => $isCurrent,
            ]);
        } else {
            $year->update([
                'start_date' => $start,
                'end_date'   => $end,
                'is_current' => $isCurrent,
            ]);
        }

        $hasTerms = Term::where('academic_year_id', $year->id)->exists();
        if (!$hasTerms) {
            $startDate = Carbon::parse($start);
            $endDate   = Carbon::parse($end);
            $days      = $startDate->diffInDays($endDate);
            $mid       = $startDate->copy()->addDays((int) floor($days / 2));

            Term::create([
                'academic_year_id' => $year->id,
                'name'       => 'Semester I',
                'order'      => 1,
                'start_date' => $startDate->format('Y-m-d'),
                'end_date'   => $mid->format('Y-m-d'),
            ]);

            Term::create([
                'academic_year_id' => $year->id,
                'name'       => 'Semester II',
                'order'      => 2,
                'start_date' => $mid->copy()->addDay()->format('Y-m-d'),
                'end_date'   => $endDate->format('Y-m-d'),
            ]);
        }
    }

    /**
     * Pastikan hanya satu tahun ajaran aktif per sekolah (opsional).
     */
    private function ensureSingleCurrentYear(?int $schoolId): void
    {
        $current = AcademicYear::query()
            ->when($schoolId, fn($q) => $q->where('school_id', $schoolId))
            ->where('is_current', true)
            ->orderByDesc('end_date')
            ->get();

        if ($current->count() <= 1) {
            return;
        }

        $keep = $current->first();
        AcademicYear::query()
            ->when($schoolId, fn($q) => $q->where('school_id', $schoolId))
            ->where('id', '!=', $keep->id)
            ->update(['is_current' => false]);
    }
}
