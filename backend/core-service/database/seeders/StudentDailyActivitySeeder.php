<?php

namespace Database\Seeders;

use App\Models\StudentDailyActivity;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StudentDailyActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            'Sholat Shubuh',
            'Sholat Dzuhur',
            'Sholat Ashar',
            'Sholat Maghrib',
            'Sholat Isya',
            'Sholat Dhuha',
            'Sholat Tahajud',
            'Sholat Rawatib',
            'Membantu Orang Tua',
            'Tilawah/Yanbu\'a di Rumah',
            'Shaum'
        ];

        foreach ($data as $activity) {
            StudentDailyActivity::create([
                'school_id' => 4,
                'name' => $activity,
            ]);
        }
    }
}
