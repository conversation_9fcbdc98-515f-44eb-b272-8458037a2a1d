<?php

namespace Database\Seeders;

use App\Enums\GradeType;
use App\Models\Grade;
use App\Models\ReportCard;
use App\Models\SubClassroomSubject;
use App\Models\User;
use Illuminate\Database\Seeder;

class GradeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $students = User::query()->hasRole('student')->get();
        foreach ($students as $student) {
            $subClassroomSubjects = SubClassroomSubject::where('sub_classroom_id', $student->sub_classroom_id)->get();
            foreach ($subClassroomSubjects as $subClassroomSubject) {
                // Create report card
                $reportCards = ReportCard::create([
                    'school_id' => 4,
                    'term_id' => 1,
                    'student_user_id' => $student->id,
                    'sub_classroom_subject_id' => $subClassroomSubject->id,
                    'final_score' => rand(78, 100),
                    'grade' => 'A',
                ]);

                // Create assignment grade 6 times
                for ($i = 0; $i < 6; $i++) {
                    Grade::create([
                        'school_id' => 4,
                        'term_id' => 1,
                        'report_card_id' => $reportCards->id,
                        'student_user_id' => $student->id,
                        'type' => GradeType::ASSIGNMENT,
                        'score' => rand(78, 100),
                    ]);
                }

                // Create mid-exam grade
                Grade::create([
                    'school_id' => 4,
                    'term_id' => 1,
                    'report_card_id' => $reportCards->id,
                    'student_user_id' => $student->id,
                    'type' => GradeType::MID_EXAM,
                    'score' => rand(78, 100),
                ]);

                // Create final-exam grade
                Grade::create([
                    'school_id' => 4,
                    'term_id' => 1,
                    'report_card_id' => $reportCards->id,
                    'student_user_id' => $student->id,
                    'type' => GradeType::FINAL_EXAM,
                    'score' => rand(78, 100),
                ]);
            }
        }
    }
}
