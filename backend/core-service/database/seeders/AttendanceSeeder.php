<?php

namespace Database\Seeders;

use App\Models\Attendance;
use App\Models\Schedule;
use App\Models\SubClassroomSubject;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AttendanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statusOptions = ['sick', 'excused_absent', 'no_status'];

        $user = User::firstWhere('email', '<EMAIL>');
        $schedules = Schedule::where('school_id', 4)->get();

        foreach ($schedules as $schedule) {
            $isPresent = rand(0, 1) === 1;
            Attendance::create([
                'school_id' => 4,
                'user_id' => $user->id,
                'schedule_id' => $schedule->id,
                'checked_in_at' => now(),
                'is_present' => $isPresent,
                'notes' => !$isPresent ? $statusOptions[array_rand($statusOptions)] : null,
            ]);
        }
    }
}
