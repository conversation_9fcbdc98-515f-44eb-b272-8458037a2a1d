<?php

namespace Database\Seeders;

use App\Models\SchedulePeriod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SchedulePeriodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        SchedulePeriod::insert([
            ['school_id' => 4, 'period' => 1, 'start_time' => '07:00:00', 'end_time' => '08:30:00'],
            ['school_id' => 4, 'period' => 2, 'start_time' => '08:30:00', 'end_time' => '10:00:00'],
            ['school_id' => 4, 'period' => 3, 'start_time' => '10:00:00', 'end_time' => '11:30:00'],
            ['school_id' => 4, 'period' => 4, 'start_time' => '11:30:00', 'end_time' => '13:00:00'],
            ['school_id' => 4, 'period' => 5, 'start_time' => '13:00:00', 'end_time' => '14:30:00'],
            ['school_id' => 4, 'period' => 6, 'start_time' => '14:30:00', 'end_time' => '16:00:00'],
        ]);
    }
}
