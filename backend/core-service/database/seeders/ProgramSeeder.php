<?php

namespace Database\Seeders;

use App\Enums\ProgramSourceType;
use App\Enums\ProgramType;
use App\Enums\SemesterType;
use App\Models\Program;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;

class ProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::transaction(function () {
            DB::table('programs')->truncate();

            Carbon::setLocale('id');

            $programAnnualData = [
                [
                    'topic' => 'A. Per<PERSON>laan Tahun Pelajaran 2024 / 2025',
                    'start_date' => '2024-07-15',
                    'end_date' => '2024-07-15',
                    'type' => ProgramType::ACADEMIC_EVENT,
                    'semester' => null,
                ],
                [
                    'topic' => '16 Kegiatan PPDB dan Masa <PERSON>’aruf <PERSON>wa <PERSON> (Matsama)',
                    'start_date' => '2024-07-15',
                    'end_date' => '2024-07-19',
                    'type' => ProgramType::ACADEMIC_EVENT,
                    'semester' => null,
                ],
                [
                    'topic' => 'Sekolah Asyik',
                    'start_date' => '2024-07-15',
                    'end_date' => '2024-08-09',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Indonesia ku',
                    'start_date' => '2024-08-01',
                    'end_date' => '2024-08-28',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Aku Ciptaan Allah',
                    'start_date' => '2024-09-01',
                    'end_date' => '2024-09-28',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Apa Saja Disekitarku',
                    'start_date' => '2024-10-01',
                    'end_date' => '2024-10-21',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Alam Semesta',
                    'start_date' => '2024-10-22',
                    'end_date' => '2024-11-25',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Kebutuhan ku',
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-01-21',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Pekerjaan',
                    'start_date' => '2025-01-22',
                    'end_date' => '2025-02-10',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Tanaman',
                    'start_date' => '2025-02-11',
                    'end_date' => '2025-02-25',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'P5',
                    'start_date' => '2025-02-26',
                    'end_date' => '2025-03-04',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Alat Transportasi',
                    'start_date' => '2025-04-01',
                    'end_date' => '2025-04-14',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Binatang',
                    'start_date' => '2025-04-15',
                    'end_date' => '2025-05-14',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Rekreasi',
                    'start_date' => '2025-05-15',
                    'end_date' => '2025-05-21',
                    'type' => ProgramType::LEARNING_TOPIC,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Tahun Baru Hijriyah',
                    'start_date' => '2024-07-07',
                    'end_date' => '2024-07-07',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Hari Kemerdekaan',
                    'start_date' => '2024-08-17',
                    'end_date' => '2024-08-17',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Maulid Nabi Muhammad SAW',
                    'start_date' => '2024-09-16',
                    'end_date' => '2024-09-16',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Libur Natal',
                    'start_date' => '2025-12-25',
                    'end_date' => '2025-12-25',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER1,
                ],
                [
                    'topic' => 'Tahun Baru Masehi',
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-01-01',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Peringatan Isra Mi’raj',
                    'start_date' => '2025-01-27',
                    'end_date' => '2025-01-27',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Tahun Baru Imlek',
                    'start_date' => '2025-01-29',
                    'end_date' => '2025-01-29',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Libur Hari Raya Nyepi',
                    'start_date' => '2025-03-29',
                    'end_date' => '2025-03-29',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Raya Idul Fitri',
                    'start_date' => '2025-03-02',
                    'end_date' => '2025-03-07',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Buruh',
                    'start_date' => '2025-05-01',
                    'end_date' => '2025-05-01',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Waisak',
                    'start_date' => '2025-05-12',
                    'end_date' => '2025-05-12',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Kenaikan Isa Al Masih',
                    'start_date' => '2025-05-29',
                    'end_date' => '2025-05-29',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Lahir Pancasila',
                    'start_date' => '2025-06-01',
                    'end_date' => '2025-06-01',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
                [
                    'topic' => 'Hari Raya Idul Adha',
                    'start_date' => '2025-06-07',
                    'end_date' => '2025-06-07',
                    'type' => ProgramType::HOLIDAY,
                    'semester' => SemesterType::SEMESTER2,
                ],
            ];

            $semesterWeekCounters = [
                SemesterType::SEMESTER1->value => 1,
                SemesterType::SEMESTER2->value => 1,
            ];


            foreach ($programAnnualData as $data) {
                $teacherId = 1;
                $schoolId = 1;
                $academicYearId = 2;

                $start = Carbon::parse($data['start_date'])->startOfWeek(Carbon::MONDAY);
                $end = Carbon::parse($data['end_date'])->endOfWeek(Carbon::FRIDAY);
                $durationWeeks = $start->diffInWeeks($end) + 1;

                // Buat program tahunan
                $created = Program::create([
                    'school_id'        => $schoolId,
                    'academic_year_id' => $academicYearId,
                    'teacher_id'       => $teacherId,
                    'type'             => $data['type'],
                    'semester'         => $data['semester'],
                    'topic'            => $data['topic'],
                    'start_date'       => $start->format('Y-m-d'),
                    'end_date'         => $end->format('Y-m-d'),
                    'duration_weeks'   => $durationWeeks,
                    'source_type'      => ProgramSourceType::ANNUAL,
                    'created_at'       => now(),
                    'updated_at'       => now(),
                ]);

                // Buat program mingguan otomatis
                if ($created->type === ProgramType::LEARNING_TOPIC) {
                    $loopDate = $start->copy();

                    $semesterKey = is_object($created->semester) ? $created->semester->value : $created->semester;
                    $weekIndex = $semesterWeekCounters[$semesterKey] ?? 1;

                    while ($loopDate->lte($end)) {
                        $weekStart = $loopDate->copy();
                        $weekEnd = $loopDate->copy()->addDays(4);

                        if ($weekEnd->gt($end)) {
                            $weekEnd = $end->copy();
                        }

                        $monthName = $weekStart->translatedFormat('F');
                        $monthLabel = "{$monthName} ({$weekStart->format('j')} – {$weekEnd->format('j M')})";

                        Program::create([
                            'school_id'        => $schoolId,
                            'academic_year_id' => $academicYearId,
                            'teacher_id'       => $teacherId,
                            'parent_id'        => $created->id,
                            'type'             => $created->type,
                            'semester'         => $created->semester,
                            'topic'            => "Minggu ke-{$weekIndex}",
                            'week'             => $weekIndex,
                            'month'            => $monthLabel,
                            'start_date'       => $weekStart->format('Y-m-d'),
                            'end_date'         => $weekEnd->format('Y-m-d'),
                            'source_type'      => ProgramSourceType::MONTHLY,
                            'created_at'       => now(),
                            'updated_at'       => now(),
                        ]);

                        $loopDate->addWeek();
                        $weekIndex++;
                    }

                    // Simpan week terakhir ke map semester
                    $semesterWeekCounters[$semesterKey] = $weekIndex;
                }
            }
        });
    }
}
