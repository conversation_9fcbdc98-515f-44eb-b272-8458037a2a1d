<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - {{ $nama_sekolah ?? 'SMK Yadika 13' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: #fff;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .header h1 {
            font-size: 16px;
            margin-bottom: 8px;
            text-transform: uppercase;
        }

        .subtitle {
            font-size: 12px;
            font-weight: bold;
            margin-top: 6px;
        }

        /* Student info */
        .student-info {
            margin-bottom: 20px;
        }

        .student-info table {
            width: 100%;
            font-size: 12px;
        }

        .student-info td {
            padding: 2px 0;
            vertical-align: top;
        }

        .student-info td:first-child {
            width: 150px;
        }

        .student-info td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        .student-info td:nth-child(3) {
            width: 230px;
        }

        .student-info td:nth-child(4) {
            width: 80px;
        }

        .student-info td:nth-child(5) {
            width: 10px;
            text-align: center;
        }

        /* Grades */
        .grades-table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0 15px;
            font-size: 11px;
        }

        .grades-table th,
        .grades-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            vertical-align: top;
        }

        .grades-table th {
            background: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .grades-table td.center {
            text-align: center;
        }

        .grades-table .no-col {
            width: 28px;
        }

        .grades-table .subject-col {
            width: 200px;
        }

        .grades-table .grade-col {
            width: 60px;
        }

        /* Ekstrakurikuler */
        .extracurricular {
            margin: 20px 0;
        }

        .extracurricular table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .extracurricular th,
        .extracurricular td {
            border: 1px solid #000;
            padding: 6px 8px;
        }

        .extracurricular th {
            background: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .extracurricular .no-col {
            width: 28px;
            text-align: center;
        }

        .extracurricular .activity-col {
            width: 150px;
        }

        .extracurricular .grade-col {
            width: 60px;
            text-align: center;
        }

        .extracurricular .desc-col {
            width: 100px;
            text-align: center;
        }

        .extracurricular .center {
            text-align: center;
        }

        /* Attendance */
        .attendance {
            margin: 15px 0;
            font-size: 12px;
        }

        .attendance table {
            width: 50%;
            max-width: 300px;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .attendance td {
            border: 1px solid #000;
            padding: 6px 8px;
        }

        .attendance td:first-child {
            width: 100px;
        }

        .attendance td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        /* Hapus border kanan untuk kolom titik dua */
        .attendance td.colon {
            border-right: none;
        }

        .attendance td.day {
            border-left: none;
        }

        .attendance td:first-child {
            width: 120px;
        }

        .attendance td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        /* Notes */
        .notes {
            margin: 10px 0;
            padding: 10px;
            font-size: 12px;
            border: 1px solid #000;

        }

        .class_teacher_note {
            font-size: 12px;
            font-weight: bold;
        }

        .notes p {
            text-align: justify;
            line-height: 1.5;
        }

        /* Signatures */
        .signatures {
            margin-top: 30px;
            font-size: 11px;
        }

        .signatures table {
            width: 100%;
            border-collapse: collapse;
        }

        .signatures td {
            vertical-align: top;
            padding: 0 10px;
        }

        .sig-left {
            text-align: left;
        }

        .sig-right {
            text-align: right;
        }

        .sig-center {
            text-align: center;
            padding-top: 12px;
        }

        .signature-box {
            height: 70px;
            margin: 10px 0;
        }

        .signature-name {
            font-weight: bold;
            margin-top: 2px;
        }

        .dotted-line {
            letter-spacing: 1px;
        }

        .sig-date {
            font-weight: bold;
            margin-bottom: 6px;
        }

        .notes h3 {
            font-size: 12px;
            margin-bottom: 4px;
        }

        .notes p {
            font-size: 12px;
            margin: 0 0 6px 0;
        }

        .graduation {
            font-size: 12px;
        }

        .graduation .label {
            font-weight: bold;
            margin-right: 6px;
        }

        .strike {
            text-decoration: line-through;
        }

        .mt-2 {
            margin-top: 8px;
        }

        @media print {
            .container {
                margin: 0;
                padding: 10mm;
            }

            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Laporan Hasil Belajar</h1>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <table>
                <colgroup>
                    <col class="label">
                    <col class="sep">
                    <col class="val">
                    <col class="label">
                    <col class="sep">
                    <col class="val">
                </colgroup>
                <tr>
                    <td class="label">Nama</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nama ?? '—' }}</td>

                    <td class="label">Kelas</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $kelas ?? '—' }}</td>
                </tr>
                <tr>
                    <td class="label">NIS / NISN</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nis ?? '—' }} / {{ $nisn ?? '—' }}</td>

                    <td class="label">Fase</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $fase ?? '—' }}</td>
                </tr>
                <tr>
                    <td class="label">Nama Sekolah</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nama_sekolah ?? '—' }}</td>

                    <td class="label">Semester</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $semester ?? '—' }}</td>
                </tr>
                <tr>
                    <td class="label">Alamat</td>
                    <td class="sep">:</td>
                    <td class="val full" colspan="1">{{ $alamat ?? '—' }}</td>

                    <td class="label">Tahun Ajaran</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $tahun_ajaran ?? '—' }}</td>
                </tr>
            </table>
        </div>

        <!-- Grades Table -->
        <table class="grades-table">
            <thead>
                <tr>
                    <th class="no-col">No</th>
                    <th class="subject-col">Mata Pelajaran</th>
                    <th class="grade-col">Nilai Akhir</th>
                    <th class="achievement-col">Capaian Kompetensi</th>
                </tr>
            </thead>
            <tbody>
                @if (isset($mata_pelajaran) && is_array($mata_pelajaran) && count($mata_pelajaran))
                    @foreach ($mata_pelajaran as $index => $mapel)
                        <tr>
                            <td class="center">{{ $index + 1 }}</td>
                            <td>{{ $mapel['nama'] }}</td>
                            <td class="center">{{ $mapel['nilai'] }}</td>
                            <td>{{ $mapel['capaian'] }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td class="center" colspan="4">Belum ada data nilai.</td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- Extracurricular Activities -->
        <div class="extracurricular">
            <table>
                <thead>
                    <tr>
                        <th class="no-col">No</th>
                        <th class="activity-col">Kegiatan Ekstrakurikuler</th>
                        <th class="grade-col">Predikat</th>
                        <th class="desc-col">Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    @if (isset($ekstrakurikuler) && is_array($ekstrakurikuler) && count($ekstrakurikuler))
                        @foreach ($ekstrakurikuler as $index => $ekskul)
                            <tr>
                                <td class="center">{{ $index + 1 }}</td>
                                <td>{{ $ekskul['nama'] }}</td>
                                <td class="center">{{ $ekskul['predikat'] }}</td>
                                <td>{{ $ekskul['keterangan'] }}</td>
                            </tr>
                        @endforeach
                    @else
                        <tr>
                            <td class="center" colspan="4">Tidak ada data ekstrakurikuler.</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Attendance -->
        <div class="attendance">
            <table>
                <tr>
                    <td>Sakit</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $sakit }} hari</td>
                </tr>
                <tr>
                    <td>Izin</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $izin }} hari</td>
                </tr>
                <tr>
                    <td>Tanpa Keterangan</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $alpa }} hari</td>
                </tr>
            </table>
        </div>

        <!-- Teacher Notes -->
        <h3 class="class_teacher_note">CATATAN WALI KELAS</h3>
        <div class="notes">
            <p>{{ $class_teacher_note ?? '—' }}</p>

            @if (!empty($is_final) && isset($graduation_status) && in_array($graduation_status, ['pass', 'fail'], true))
                @php
                    $pass = $graduation_status === 'pass';
                    $fail = $graduation_status === 'fail';
                @endphp
                <div class="graduation mt-2">
                    <span class="label">Keterangan Kelulusan:</span>
                    <span class="{{ $pass ? '' : 'strike' }}">Lulus</span>
                    <span> / </span>
                    <span class="{{ $fail ? '' : 'strike' }}">Tidak Lulus</span>
                </div>
            @endif
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <table>
                <tr>
                    <!-- KIRI ATAS: ORTU/WALI -->
                    <td class="sig-left" style="width:33%">
                        <div>Mengetahui</div>
                        <div>Orang Tua/Wali,</div>
                        <div class="signature-box"></div>
                        <div class="dotted-line">..........................</div>
                    </td>

                    <!-- TENGAH ATAS: KOSONG (spacer) -->
                    <td style="width:34%"></td>

                    <!-- KANAN ATAS: WALI KELAS + TANGGAL DI POJOK KANAN -->
                    <td class="sig-right" style="width:33%">
                        <div class="sig-date">{{ $kota ?? 'Kab. Bekasi' }}, {{ $tanggal ?? now()->format('d F Y') }}
                        </div>
                        <div>Wali Kelas,</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ $wali_kelas ?? 'Putri Nurkhasanah, A. Md.' }}</div>
                    </td>
                </tr>

                <tr>
                    <!-- baris kedua: kepala sekolah DI TENGAH BAWAH -->
                    <td></td>
                    <td class="sig-center">
                        <div>Mengetahui</div>
                        <div>Kepala Sekolah</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ 'Novita Yusnaini, S.S., M.Pd.' }}</div>
                        <div class="school-code">{{ 'NUKS. 19023L0550222242079707' }}</div>
                    </td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>
