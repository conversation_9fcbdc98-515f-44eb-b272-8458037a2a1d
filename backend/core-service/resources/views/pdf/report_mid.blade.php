<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - {{ $nama_sekolah }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .header h1 {
            font-size: 16px;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .header .subtitle {
            font-size: 12px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .student-info {
            margin-bottom: 20px;
        }

        .student-info table {
            width: 100%;
            font-size: 12px;
        }

        .student-info td {
            padding: 2px 0;
            vertical-align: top;
        }

        .student-info td:first-child {
            width: 120px;
        }

        .student-info td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        .student-info td:nth-child(3) {
            width: 200px;
        }

        .student-info td:nth-child(4) {
            width: 80px;
        }

        .student-info td:nth-child(5) {
            width: 10px;
            text-align: center;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin: 20px 0 10px 0;
        }

        .grades-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .grades-table th,
        .grades-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            vertical-align: top;
            text-align: left;
        }

        .grades-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .grades-table .no-col {
            width: 30px;
            text-align: center;
        }

        .grades-table .subject-col {
            width: 200px;
        }

        .grades-table .grade-col {
            width: 50px;
            text-align: center;
        }

        .grades-table .achievement-col {
            width: auto;
        }

        .grades-table td.center {
            text-align: center;
        }

        .extracurricular-notes {
            margin: 15px 0;
            font-size: 12px;
        }

        .extracurricular-notes p {
            margin-bottom: 5px;
        }

        .extracurricular-box {
            border: 1px solid #000;
            padding: 10px;
            background-color: #fff;
        }

        .attendance {
            margin: 15px 0;
            font-size: 12px;
        }

        .attendance table {
            width: 50%;
            max-width: 300px;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .attendance td {
            border: 1px solid #000;
            padding: 6px 8px;
        }

        .attendance td:first-child {
            width: 100px;
        }

        .attendance td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        /* Hapus border kanan untuk kolom titik dua */
        .attendance td.colon {
            border-right: none;
        }

        .attendance td.day {
            border-left: none;
        }

        .attendance td:first-child {
            width: 120px;
        }

        .attendance td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        /* Signatures */
        .signatures {
            margin-top: 30px;
            font-size: 11px;
        }

        .signatures table {
            width: 100%;
            border-collapse: collapse;
        }

        .signatures td {
            vertical-align: top;
            padding: 0 10px;
        }

        .sig-left {
            text-align: left;
        }

        .sig-right {
            text-align: right;
        }

        .sig-center {
            text-align: center;
            padding-top: 12px;
        }

        .signature-box {
            height: 70px;
            margin: 10px 0;
        }

        .signature-name {
            font-weight: bold;
            margin-top: 2px;
        }

        .dotted-line {
            letter-spacing: 1px;
        }

        .sig-date {
            font-weight: bold;
            margin-bottom: 6px;
        }

        @media print {
            .container {
                margin: 0;
                padding: 10mm;
            }

            body {
                -webkit-print-color-adjust: exact;
            }
        }

        .student-info .full {
            padding-top: 2px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Laporan Kemajuan Belajar Siswa</h1>
            <div class="subtitle">{{ $periode_header }}</div>
        </div>

        <div class="student-info">
            <table>
                <colgroup>
                    <col class="label">
                    <col class="sep">
                    <col class="val">
                    <col class="label">
                    <col class="sep">
                    <col class="val">
                </colgroup>

                <tr>
                    <td class="label">Nama</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nama }}</td>

                    <td class="label">Kelas</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $kelas }}</td>
                </tr>

                <tr>
                    <td class="label">NISN</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nisn }}</td>

                    <td class="label">Semester</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $semester }}</td>
                </tr>

                <tr>
                    <td class="label">Nama Sekolah</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $nama_sekolah }}</td>

                    <td class="label">Tahun Ajaran</td>
                    <td class="sep">:</td>
                    <td class="val">{{ $tahun_ajaran }}</td>
                </tr>

                <!-- alamat dibuat full-width biar rapi -->
                <tr>
                    <td class="label">Alamat</td>
                    <td class="sep">:</td>
                    <td class="val full" colspan="1">{{ $alamat }}</td>
                </tr>
            </table>
        </div>

        <!-- Section 1: Penilaian Intrakurikuler -->
        <div class="section-title">1. Penilaian Intrakurikuler</div>

        <!-- Grades Table -->
        <table class="grades-table">
            <thead>
                <tr>
                    <th class="no-col">No</th>
                    <th class="subject-col">Mata Pelajaran</th>
                    <th class="grade-col">Nilai</th>
                    <th class="achievement-col">Capaian Kompetensi</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($mata_pelajaran as $index => $mapel)
                    <tr>
                        <td class="center">{{ $index + 1 }}</td>
                        <td>{{ $mapel['nama'] }}</td>
                        <td class="center">{{ $mapel['nilai'] }}</td>
                        <td>{{ $mapel['capaian'] }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Section 2: Catatan Penilaian Ekstrakurikuler -->
        <div class="section-title">2. Catatan Penilaian Ekstrakurikuler</div>
        <div class="extracurricular-notes extracurricular-box">
            @php
                $ekskuls = isset($ekskuls) && is_array($ekskuls) ? $ekskuls : [];
                $ekskuls = array_values(array_unique(array_map(fn($v) => trim(strip_tags($v ?? '')), $ekskuls)));
            @endphp

            @forelse ($ekskuls as $i => $ex)
                <p>Siswa mengikuti ekstrakurikuler {{ $ex }}</p>
            @empty
                <p>Siswa belum mengikuti ekstrakurikuler</p>
            @endforelse
        </div>

        <!-- Section 3: Catatan Kehadiran -->
        <div class="section-title">3. Catatan Kehadiran</div>
        <div class="attendance">
            <table>
                <tr>
                    <td>Sakit</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $sakit }} hari</td>
                </tr>
                <tr>
                    <td>Izin</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $izin }} hari</td>
                </tr>
                <tr>
                    <td>Tanpa Keterangan</td>
                    <td class="colon">:</td>
                    <td class="day">{{ $alpa }} hari</td>
                </tr>
            </table>
        </div>


        <!-- Signatures -->
        <div class="signatures">
            <table>
                <tr>
                    <!-- KIRI ATAS: ORTU/WALI -->
                    <td class="sig-left" style="width:33%">
                        <div>Mengetahui</div>
                        <div>Orang Tua/Wali,</div>
                        <div class="signature-box"></div>
                        <div class="dotted-line">..........................</div>
                    </td>

                    <!-- TENGAH ATAS: KOSONG (spacer) -->
                    <td style="width:34%"></td>

                    <!-- KANAN ATAS: WALI KELAS + TANGGAL DI POJOK KANAN -->
                    <td class="sig-right" style="width:33%">
                        <div class="sig-date">{{ $kota ?? 'Kab. Bekasi' }}, {{ $tanggal ?? now()->format('d F Y') }}
                        </div>
                        <div>Wali Kelas,</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ $wali_kelas ?? 'Putri Nurkhasanah, A. Md.' }}</div>
                    </td>
                </tr>

                <tr>
                    <!-- baris kedua: kepala sekolah DI TENGAH BAWAH -->
                    <td></td>
                    <td class="sig-center">
                        <div>Mengetahui</div>
                        <div>Kepala Sekolah</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ 'Novita Yusnaini, S.S., M.Pd.' }}</div>
                        <div class="school-code">{{ 'NUKS. 19023L0550222242079707' }}</div>
                    </td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>
