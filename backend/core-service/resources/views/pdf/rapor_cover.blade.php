<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rapor <PERSON> - {{ $nama_sekolah ?? 'SMK Yadika 13' }}</title>
    <style>
        /* ====== RESET ====== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Paksa area cetak pas A4 dan biar padding .container jadi margin halaman */
        @page {
            size: A4;
            margin: 0;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        /* Satu halaman = satu .container */
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            /* “margin” halaman */
            background: #fff;
            /* tinggi A4 (297) - padding atas+bawah (30) - toleransi 1mm */
            min-height: calc(297mm - 30mm - 1mm);
            page-break-inside: avoid;
        }

        .page1-header {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .school-info-table {
            width: 100%;
            font-size: 12px;
            margin-bottom: 30px;
        }

        .school-info-table td {
            padding: 3px 0;
            vertical-align: top;
        }

        .school-info-table td:first-child {
            width: 140px;
        }

        .school-info-table td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        .main-cover {
            text-align: center;
            margin-top: 80px;
        }

        .main-cover h1 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .main-cover h2 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .main-cover h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 60px;
        }

        .student-info-cover {
            margin: 60px 0;
        }

        .student-info-cover .label {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .student-info-cover .value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .school-address-cover {
            font-size: 14px;
            font-weight: bold;
            margin-top: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* flex item di tengah */
            gap: 5px;
            text-align: center;
            /* teks rata tengah */
        }

        .school-address-cover div {
            max-width: 200px;
            /* batasi lebar alamat */
            word-wrap: break-word;
            /* biar kata panjang otomatis turun */
            margin: 0 auto;
            /* biar blok tetap di tengah */
        }

        img.logo {
            width: 200px;
            margin-bottom: 20px;
        }

        .identity-header {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 30px;
            text-transform: uppercase;
        }

        .identity-table {
            width: 100%;
            font-size: 12px;
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .identity-table td {
            padding: 4px 0;
            vertical-align: top;
        }

        .identity-table td:first-child {
            width: 30px;
        }

        .identity-table td:nth-child(2) {
            width: 220px;
        }

        .identity-table td:nth-child(3) {
            width: 10px;
            text-align: center;
        }

        .identity-signature {
            text-align: right;
            font-size: 12px;
            margin-top: 60px;
        }

        .identity-signature .location-date {
            margin-bottom: 10px;
        }

        .identity-signature .title {
            margin-bottom: 60px;
        }

        .identity-signature .name {
            text-decoration: underline;
            margin-bottom: 5px;
        }

        .identity-signature .code {
            font-size: 10px;
        }

        .transfer-header {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .transfer-student-name {
            font-size: 12px;
            margin-bottom: 20px;
        }

        .transfer-type {
            border-style: solid;
            border-width: 1px 1px 0 1px;
            border-color: #000;
            background: #f0f0f0;

            text-align: center;
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
        }


        .transfer-out-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            page-break-inside: avoid;
        }

        .transfer-out-table th,
        .transfer-out-table td {
            border: 1px solid #000;
            padding: 6px;
            vertical-align: middle;
        }

        /* header tabel lebih “tegas” seperti contoh kanan */
        .transfer-out-table th {
            background: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 11px;
            text-transform: uppercase;
            /* UPPERCASE */
            line-height: 1.35;
            /* spasi nyaman untuk 2–3 baris */
            padding: 8px 6px;
            /* padding konsisten */
        }

        /* kolom tanda tangan sedikit lebih lebar biar muat 3 baris judul */
        .transfer-out-table .signature-col {
            width: 220px;
        }

        /* rapikan baris konten tanda tangan */
        .signature-cell {
            padding: 6px;
            font-size: 11px;
            text-align: center;
        }

        .signature-cell .gap-40 {
            height: 40px;
        }

        .signature-cell .gap-30 {
            height: 30px;
        }

        .signature-cell .u {
            text-decoration: underline;
        }


        .transfer-out-table .date-col {
            width: 80px;
        }

        .transfer-out-table .class-col {
            width: 80px;
        }

        .transfer-out-table .reason-col {
            width: 200px;
        }

        .transfer-out-table td {
            height: 80px;
        }

        /* pemisah antar halaman — pakai hanya di antara container */
        .page-break {
            page-break-after: always;
        }

        /* ====== Tabel MASUK (Halaman 5) persis seperti gambar ====== */
        .transfer-in-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .transfer-in-table th,
        .transfer-in-table td {
            border: 1.2px solid #000;
            padding: 8px 10px;
            vertical-align: middle;
        }

        .transfer-in-table thead th {
            background: #f0f0f0;
            font-weight: bold;
            text-transform: uppercase;
            text-align: center;
            line-height: 1.35;
        }

        .transfer-in-table .no-col {
            width: 50px;
            text-align: center;
        }

        .transfer-in-table .label-col {
            width: 180px;
        }

        .transfer-in-table td:nth-child(3) {
            width: 200px;
        }

        .transfer-in-table .signature-col {
            width: 200px;
        }

        .transfer-in-table .signature-col {
            width: 230px;
        }

        /* konten */
        .center {
            text-align: center;
        }

        .u {
            text-decoration: underline;
        }

        .sig-cell {
            font-size: 11px;
            text-align: left;
        }

        .sig-cell .sp-40 {
            height: 40px;
        }

        .dotted {
            display: block;
            border-bottom: 1px dotted #000;
            height: 14px;
            width: 100%;
            margin: 6px 0;
        }

        .stack>.dotted {
            margin: 20px 0;
        }


        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }

            .container {
                padding: 15mm;
            }

            /* konsisten saat print */
            .page-break {
                page-break-after: always;
            }
        }
    </style>
</head>

<body>

    <!-- HALAMAN 1 -->
    <div class="container">
        <div class="page1-header">RAPOR</div>
        <div class="school-info-table">
            <table>
                <tr>
                    <td>Nama Sekolah</td>
                    <td>:</td>
                    <td>{{ $nama_sekolah ?? 'SMK YADIKA 13' }}</td>
                </tr>
                <tr>
                    <td>NPSN</td>
                    <td>:</td>
                    <td>{{ $npsn ?? '20268791' }}</td>
                </tr>
                <tr>
                    <td>TNIS/NSS/NDS</td>
                    <td>:</td>
                    <td>{{ $tnis ?? '402022205006' }}</td>
                </tr>
                <tr>
                    <td>Alamat Sekolah</td>
                    <td>:</td>
                    <td>{{ $alamat_sekolah ?? 'JL. RAYA VILLA BEKASI INDAH, JEJALENJAYA, KEC. TAMBUN UTARA, BEKASI, JAWA BARAT.' }}
                    </td>
                </tr>
                <tr>
                    <td>Kelurahan / Desa</td>
                    <td>:</td>
                    <td>{{ $kelurahan ?? 'JEJALENJAYA' }}</td>
                </tr>
                <tr>
                    <td>Kecamatan</td>
                    <td>:</td>
                    <td>{{ $kecamatan ?? 'TAMBUN UTARA' }}</td>
                </tr>
                <tr>
                    <td>Kota / Kabupaten</td>
                    <td>:</td>
                    <td>{{ $kota_kab ?? 'KAB.BEKASI' }}</td>
                </tr>
                <tr>
                    <td>Provinsi</td>
                    <td>:</td>
                    <td>{{ $provinsi ?? 'JAWA BARAT' }}</td>
                </tr>
                <tr>
                    <td>Website</td>
                    <td>:</td>
                    <td>{{ $website ?? 'HTTPS://SMKYADIKA13.SMARTESCHOOL.ID' }}</td>
                </tr>
                <tr>
                    <td>E-mail</td>
                    <td>:</td>
                    <td>{{ $email ?? '<EMAIL>' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="page-break"></div>

    <!-- HALAMAN 2 -->
    <div class="container">
        <div class="main-cover">
            <h1>RAPOR</h1>
            <h2>SEKOLAH MENENGAH KEJURUAN</h2>
            <h3>(SMK)</h3>

            @if (!empty($logo_url))
                <img class="logo" src="{{ $logo_url }}" alt="Logo Sekolah" />
            @endif

            <div class="student-info-cover">
                <div class="label">Nama Peserta Didik</div>
                <div class="value">{{ isset($nama) ? strtoupper($nama) : 'ARTIKA PUTRI KUSUMA WARDANI' }}</div>
                <div class="label">NISN / NIS</div>
                <div class="value">{{ $nisn ?? '0097192115' }} / {{ $nis ?? '242510001' }}</div>
            </div>

            <div class="school-address-cover">
                <div>{{ $nama_sekolah ?? 'SMK YADIKA 13' }}</div>
                <div>{{ $alamat_cover ?? 'JL. VILLA I, KP. KEBON, DS. JEJALEN JAYA' }}</div>
            </div>
        </div>
    </div>

    <div class="page-break"></div>

    <!-- HALAMAN 3 -->
    <div class="container">
        <div class="identity-header">
            <h2>Identitas Peserta Didik</h2>
        </div>

        <div class="identity-table">
            <table>
                <tr>
                    <td>1.</td>
                    <td>Nama</td>
                    <td>:</td>
                    <td>{{ $nama ?? 'Artika Putri Kusuma Wardani' }}</td>
                </tr>
                <tr>
                    <td>2.</td>
                    <td>NISN / NIS</td>
                    <td>:</td>
                    <td>{{ $nisn ?? '0097192115' }} / {{ $nis ?? '242510001' }}</td>
                </tr>
                <tr>
                    <td>3.</td>
                    <td>Tempat, Tanggal Lahir</td>
                    <td>:</td>
                    <td>{{ $tempat_lahir ?? 'Bekasi' }}, {{ $tanggal_lahir ?? '14 Mei 2009' }}</td>
                </tr>
                <tr>
                    <td>4.</td>
                    <td>Jenis Kelamin</td>
                    <td>:</td>
                    <td>{{ $jenis_kelamin ?? 'Perempuan' }}</td>
                </tr>
                <tr>
                    <td>5.</td>
                    <td>Agama</td>
                    <td>:</td>
                    <td>{{ $agama ?? 'Islam' }}</td>
                </tr>
                <tr>
                    <td>6.</td>
                    <td>Status dalam Keluarga</td>
                    <td>:</td>
                    <td>{{ $status_keluarga ?? 'Anak Kandung' }}</td>
                </tr>
                <tr>
                    <td>7.</td>
                    <td>Anak ke</td>
                    <td>:</td>
                    <td>{{ $anak_ke ?? '3' }}</td>
                </tr>
                <tr>
                    <td>8.</td>
                    <td>Alamat Peserta Didik</td>
                    <td>:</td>
                    <td>{{ $alamat_siswa ?? 'Perum Maldives Garden No.A3, RT.002/035 Sumber Jaya Tambun Selatana Bekasi' }}
                    </td>
                </tr>
                <tr>
                    <td>9.</td>
                    <td>Nomor Telepon</td>
                    <td>:</td>
                    <td>{{ $no_telepon ?? '081574068705' }}</td>
                </tr>
                <tr>
                    <td>10.</td>
                    <td>Sekolah Asal</td>
                    <td>:</td>
                    <td>{{ $sekolah_asal ?? 'SMPN 6 Tambun Selatan' }}</td>
                </tr>

                <tr>
                    <td>11.</td>
                    <td colspan="3"><strong>Diterima di sekolah ini</strong></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">Di kelas</td>
                    <td>:</td>
                    <td>{{ $kelas_diterima ?? 'X AKL' }} - {{ $fase ?? 'Fase E' }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">Pada tanggal</td>
                    <td>:</td>
                    <td>{{ $tanggal_diterima ?? '15 Juli 2024' }}</td>
                </tr>

                <tr>
                    <td>12.</td>
                    <td colspan="3"><strong>Nama Orang Tua</strong></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">a. Ayah</td>
                    <td>:</td>
                    <td>{{ $nama_ayah ?? 'Sugeng' }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">b. Ibu</td>
                    <td>:</td>
                    <td>{{ $nama_ibu ?? 'Siti Soibah' }}</td>
                </tr>

                <tr>
                    <td>13.</td>
                    <td>Alamat Orang Tua</td>
                    <td>:</td>
                    <td>{{ $alamat_ortu ?? 'Perum Maldives Garden No.A3, RT.002/035 Sumber Jaya Tambun Selatana Bekasi' }}
                    </td>
                </tr>
                <tr>
                    <td>14.</td>
                    <td>Nomor Telepon Orang Tua</td>
                    <td>:</td>
                    <td>{{ $no_telepon_ortu ?? '081574068705' }}</td>
                </tr>

                <tr>
                    <td>15.</td>
                    <td colspan="3"><strong>Pekerjaan Orang Tua</strong></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">a. Ayah</td>
                    <td>:</td>
                    <td>{{ $pekerjaan_ayah ?? 'Wiraswasta' }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-left:20px;">b. Ibu</td>
                    <td>:</td>
                    <td>{{ $pekerjaan_ibu ?? 'Ibu Rumah Tangga' }}</td>
                </tr>

                <tr>
                    <td>16.</td>
                    <td>Nama Wali Peserta Didik</td>
                    <td>:</td>
                    <td>{{ $nama_wali ?? '-' }}</td>
                </tr>
                <tr>
                    <td>17.</td>
                    <td>Alamat Wali Peserta Didik</td>
                    <td>:</td>
                    <td>{{ $alamat_wali ?? '-' }}</td>
                </tr>
                <tr>
                    <td>18.</td>
                    <td>Nomor Telpon Wali Peserta Didik</td>
                    <td>:</td>
                    <td>{{ $no_telepon_wali ?? '-' }}</td>
                </tr>
                <tr>
                    <td>19.</td>
                    <td>Pekerjaan Wali Peserta Didik</td>
                    <td>:</td>
                    <td>{{ $pekerjaan_wali ?? '-' }}</td>
                </tr>
            </table>
        </div>

        <div class="identity-signature">
            <div class="location-date">{{ $kota_identitas ?? 'Kab. Bekasi' }},
                {{ $tanggal_identitas ?? '17 Juli 2023' }}</div>
            <div class="title">Kepala Sekolah</div>
            <div class="name">{{ 'Novita Yusnaini, S.S.,M.Pd.' }}</div>
            <div class="code">{{ 'NUKS. 19023L0550222242079707' }}</div>
        </div>
    </div>

    <div class="page-break"></div>

    <!-- HALAMAN 4 -->
    <div class="container">
        <div class="transfer-header">
            <h3>KETERANGAN PINDAH SEKOLAH</h3>
        </div>

        <div class="transfer-student-name">
            Nama Peserta Didik : {{ '....................................................' }}
        </div>

        <div class="transfer-type">KELUAR</div>

        <table class="transfer-out-table">
            <thead>
                <tr>
                    <th class="date-col">Tanggal</th>
                    <th class="class-col">Kelas yang<br>ditinggalkan</th>
                    <th class="reason-col">Sebab-sebab Keluar atau<br>Atas Permintaan (Tertulis)</th>
                    <th class="signature-col">Tanda Tangan Kepala Sekolah,<br>Stempel Sekolah,<br>dan Tanda Tangan Orang
                        Tua/Wali</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="signature-cell">
                        <div>.....................,....................</div>
                        <div style="margin:5px 0;">Kepala Sekolah,</div>
                        <div class="gap-40"></div>
                        <div class="u">..........................................</div>
                        <div>NIP.</div>
                        <div style="margin:12px 0;">Orang Tua/Wali,</div>
                        <div class="gap-30"></div>
                        <div class="u">..........................................</div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="page-break"></div>

    <!-- HALAMAN 5 -->
    <div class="container">
        <div class="transfer-header">
            <h3>KETERANGAN PINDAH SEKOLAH</h3>
        </div>

        <!-- baris nama peserta didik seperti gambar -->
        <div class="transfer-student-name">
            Nama Peserta Didik &nbsp;: &nbsp;
            {{ '...............................................................' }}
        </div>

        <table class="transfer-in-table">
            <thead>
                <tr>
                    <th class="no-col">NO</th>
                    <th colspan="3">MASUK</th>
                </tr>
            </thead>

            <tbody>
                <!-- 1 -->
                <tr>
                    <td class="center"><strong>1.</strong></td>
                    <td class="label-col">Nama Siswa</td>
                    <td>
                        <div class="stack">
                            <div class="dotted"></div>
                        </div>
                    </td>
                    <!-- panel tanda tangan (rowspan 5) -->
                    <td class="sig-cell" rowspan="5">
                        <div>................................................</div>
                        <div style="margin:6px 0;">Kepala Sekolah,</div>
                        <div class="sp-40"></div>
                        <div class="u">................................................</div>
                        <div>NIP.</div>
                    </td>
                </tr>

                <!-- 2 -->
                <tr>
                    <td class="center"><strong>2.</strong></td>
                    <td class="label-col">Nomor Induk</td>
                    <td>
                        <div class="dotted"></div>
                    </td>
                </tr>

                <!-- 3 -->
                <tr>
                    <td class="center"><strong>3.</strong></td>
                    <td class="label-col">Nama Sekolah</td>
                    <td>
                        <div class="dotted"></div>
                    </td>
                </tr>

                <!-- 4 -->
                <tr>
                    <td class="center"><strong>4.</strong></td>
                    <td class="label-col">
                        Masuk di Sekolah ini:
                        <div style="padding-left:18px;">a. Tanggal</div>
                        <div style="padding-left:18px;">b. Di Kelas</div>
                    </td>
                    <td>
                        <div class="stack">
                            <div class="dotted"></div> <!-- (opsional baris umum) -->
                            <div class="dotted"></div> <!-- a. Tanggal -->
                            <div class="dotted"></div> <!-- b. Di Kelas -->
                        </div>
                    </td>
                </tr>

                <!-- 5 -->
                <tr>
                    <td class="center"><strong>5.</strong></td>
                    <td class="label-col">Tahun Pelajaran</td>
                    <td>
                        <div class="dotted"></div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>
