<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
        }

        .header {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .student-info {
            margin-bottom: 20px;
        }

        .student-info table {
            width: 100%;
            font-size: 12px;
        }

        .student-info td {
            padding: 2px 0;
            vertical-align: top;
        }

        .student-info td:first-child {
            width: 120px;
        }

        .student-info td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        .student-info td:nth-child(3) {
            width: 200px;
        }

        .student-info td:nth-child(4) {
            width: 120px;
        }

        .student-info td:nth-child(5) {
            width: 10px;
            text-align: center;
        }

        .student-info td:nth-child(6) {
            width: auto;
        }

        .grades-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .grades-table th,
        .grades-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            vertical-align: top;
            text-align: left;
        }

        .grades-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .grades-table .no-col {
            width: 30px;
            text-align: center;
        }

        .grades-table .tujuan-col {
            width: 200px;
        }

        .grades-table .nilai-col {
            width: 50px;
            text-align: center;
        }

        .grades-table .deskripsi-col {
            width: auto;
        }

        .grades-table td.center {
            text-align: center;
        }

        .attendance {
            margin: 15px 0;
            font-size: 12px;
        }

        .attendance h3 {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .attendance table {
            width: 300px;
        }

        .attendance td {
            padding: 2px 0;
        }

        .attendance td:first-child {
            width: 120px;
        }

        .attendance td:nth-child(2) {
            width: 10px;
            text-align: center;
        }

        .notes {
            margin: 20px 0;
            font-size: 12px;
        }

        .notes h3 {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .notes p {
            text-align: justify;
            line-height: 1.5;
        }

        .signatures {
            margin-top: 30px;
            font-size: 11px;
        }

        .signatures table {
            width: 100%;
        }

        .signatures td {
            width: 33.33%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signatures .signature-box {
            height: 60px;
            margin: 10px 0;
        }

        .signatures .signature-name {
            text-decoration: underline;
            margin-top: 5px;
        }

        .school-code {
            text-align: right;
            font-size: 10px;
            margin-top: 10px;
        }

        @media print {
            .container {
                margin: 0;
                padding: 10mm;
            }

            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Laporan Hasil Belajar</h1>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <table>
                <tr>
                    <td>Nama</td>
                    <td>:</td>
                    <td>{{ $nama ?? 'Agnes Florensia Gengu' }}</td>
                    <td>Program Keahlian</td>
                    <td>:</td>
                    <td>{{ $program_keahlian ?? 'Akuntansi dan Keuangan Lembaga' }}</td>
                </tr>
                <tr>
                    <td>NIS / NISN</td>
                    <td>:</td>
                    <td>{{ $nis ?? '222310001' }} / {{ $nisn ?? '0068011780' }}</td>
                    <td>Konsentrasi Keahlian</td>
                    <td>:</td>
                    <td>{{ $konsentrasi_keahlian ?? 'Akuntansi' }}</td>
                </tr>
                <tr>
                    <td>Kelas</td>
                    <td>:</td>
                    <td>{{ $kelas ?? 'XII AKL' }}</td>
                    <td>Tempat PKL</td>
                    <td>:</td>
                    <td>{{ $tempat_pkl ?? 'PT AMARTA KARYA (PERSERO)' }}</td>
                </tr>
                <tr>
                    <td>Fase</td>
                    <td>:</td>
                    <td>{{ $fase ?? 'F' }}</td>
                    <td>Periode PKL</td>
                    <td>:</td>
                    <td>{{ $periode_pkl ?? '19 November 2024 - 27 Maret 2025' }}</td>
                </tr>
                <tr>
                    <td>Semester</td>
                    <td>:</td>
                    <td>{{ $semester ?? 'Genap' }}</td>
                    <td>Nama Instruktur</td>
                    <td>:</td>
                    <td>{{ $nama_instruktur ?? 'M. Ainul Yaqin' }}</td>
                </tr>
                <tr>
                    <td>Tahun Ajaran</td>
                    <td>:</td>
                    <td>{{ $tahun_ajaran ?? '2024/2025' }}</td>
                    <td>Nama Pembimbing</td>
                    <td>:</td>
                    <td>{{ $nama_pembimbing ?? 'Putri Nurkhasanah, A.Md.' }}</td>
                </tr>
            </table>
        </div>

        <!-- PKL Assessment Table -->
        <table class="grades-table">
            <thead>
                <tr>
                    <th class="no-col">No</th>
                    <th class="tujuan-col">Tujuan Pembelajaran</th>
                    <th class="nilai-col">Nilai Akhir</th>
                    <th class="deskripsi-col">Deskripsi</th>
                </tr>
            </thead>
            <tbody>
                @if (isset($tujuan_pembelajaran) && is_array($tujuan_pembelajaran))
                    @foreach ($tujuan_pembelajaran as $index => $tujuan)
                        <tr>
                            <td class="center">{{ $index + 1 }}</td>
                            <td>{{ $tujuan['nama'] }}</td>
                            <td class="center">{{ $tujuan['nilai'] }}</td>
                            <td>{{ $tujuan['deskripsi'] }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td class="center">1</td>
                        <td>Menerapkan Soft Skill yang dibutuhkan dalam dunia kerja</td>
                        <td class="center">87</td>
                        <td>Menunjukkan penguasaan yang baik dalam menerapkan etika berkomunikasi, integritas yang
                            tinggi (jujur, disiplin, komitmen dan bertanggungjawab), etos kerja yang baik serta
                            kemandirian dalam bekerja.</td>
                    </tr>
                    <tr>
                        <td class="center">2</td>
                        <td>Menerapkan norma, POS dan K3LH yang ada pada dunia kerja</td>
                        <td class="center">86</td>
                        <td>Menunjukkan penguasaan yang baik dalam memastikan semua kegiatan kerja mengikuti aturan yang
                            telah ditetapkan untuk menjamin keselamatan dan kesehatan di dalam lingkungan kerja.</td>
                    </tr>
                    <tr>
                        <td class="center">3</td>
                        <td>Menerapkan kompetensi teknis yang sudah dipelajari di sekolah dan/ atau baru dipelajari pada
                            dunia kerja</td>
                        <td class="center">88</td>
                        <td>Menunjukkan penguasaan yang baik dalam melakukan entri data surat masuk, melakukan
                            pencetakan dan pemindaian dokumen, mempersiapkan dokumen pengiriman untuk supplier atau
                            customer, melakukan inventarisasi peralatan perusahaan.</td>
                    </tr>
                    <tr>
                        <td class="center">4</td>
                        <td>Memahami alur bisnis dunia kerja tempat PKL dan wawasan wirausaha</td>
                        <td class="center">87</td>
                        <td>Menunjukkan penguasaan yang baik dalam memahami alur bisnis dan proses kerja di tempat
                            Praktik Kerja Lapangan (PKL), mampu mengidentifikasi peluang usaha, serta menerapkan prinsip
                            dasar kewirausahaan dalam lingkungan kerja nyata.</td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- Attendance Section -->
        <div class="attendance">
            <h3>KEHADIRAN</h3>
            <table>
                <tr>
                    <td>Sakit</td>
                    <td>:</td>
                    <td>{{ $sakit ?? '-' }} Hari</td>
                </tr>
                <tr>
                    <td>Ijin</td>
                    <td>:</td>
                    <td>{{ $ijin ?? '-' }} Hari</td>
                </tr>
                <tr>
                    <td>Tanpa Keterangan</td>
                    <td>:</td>
                    <td>{{ $tanpa_keterangan ?? '-' }} Hari</td>
                </tr>
            </table>
        </div>

        <!-- Teacher Notes -->
        <div class="notes">
            <h3>CATATAN WALI KELAS</h3>
            <p>{{ $catatan ?? 'Semangat ya! Jangan pernah menyerah untuk mencoba teruslah raih cita-citamu nak. Doa ibu/bapak guru akan selalu menyertai kalian.' }}
            </p>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <table>
                <tr>
                    <td>
                        <div>Mengetahui,</div>
                        <div>Orang Tua/Wali,</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">………………………………</div>
                    </td>
                    <td>
                        <div>{{ $kota ?? 'Kab. Bekasi' }}, {{ $tanggal ?? '5 Mei 2025' }}</div>
                        <div>Wali Kelas,</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ $wali_kelas ?? 'Rusdi, S.Pd.I.' }}</div>
                    </td>
                    <td>
                        <div>Mengetahui</div>
                        <div>Kepala Sekolah,</div>
                        <div class="signature-box"></div>
                        <div class="signature-name">{{ $kepala_sekolah ?? 'Novita Yusnaini, S.S., M.Pd.' }}</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- School Code -->
        <div class="school-code">
            {{ $kode_sekolah ?? 'NUKS. 19023L0550222242079707' }}
        </div>
    </div>
</body>

</html>
