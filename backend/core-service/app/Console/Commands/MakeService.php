<?php

namespace App\Console\Commands;

use Illuminate\Console\GeneratorCommand;

class MakeService extends GeneratorCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:service {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new service class';

    /**
     * The type of class being generated.
     *
     * @var string
     */
    protected $type = 'Service';

    /**
     * Create the service class automatically.
     */
    public function handle()
    {
        // Create the service class
        $this->createServiceClass();

        $this->info("Service created for {$this->argument('name')} successfully.");
    }

    protected function createServiceClass()
    {
        $name = ucfirst($this->argument('name'));
        $path = app_path('Services');
        $filePath = $path . '/' . $name . 'Service.php';

        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        $stub = file_get_contents(base_path('/stubs/service.stub'));
        $stub = str_replace('Dummy', $name, $stub);

        file_put_contents($filePath, $stub);

        $this->info("Service class created: {$filePath}");
    }

    /**
     * Get the stub file for the generator.
     *
     * @return string
     */
    protected function getStub()
    {
        return base_path('/stubs/service.stub');
    }

    /**
     * Get the default namespace for the class.
     *
     * @param string $rootNamespace
     * @return string
     */
    protected function getNamespace($rootNamespace)
    {
        return $rootNamespace . '\Services'; // Service will be placed in app/Services
    }

    /**
     * Build the class contents.
     *
     * @param string $name
     * @return string
     */
    protected function buildClass($name)
    {
        // Build the service class content
        $stub = parent::buildClass($name);

        // Replace placeholders for model names
        return str_replace(['Dummy'], [ucfirst($name)], $stub);
    }

    /**
     * Get the path where the service class should be created.
     *
     * @param string $name
     * @return string
     */
    protected function getPath($name)
    {
        return app_path('Services') . '/' . $this->getNameInput() . 'Service.php'; // Save in app/Services
    }

    /**
     * Build the class name from the name.
     *
     * @param string $name
     * @return string
     */
    protected function getNameInput()
    {
        return ucfirst($this->argument('name'));
    }
}
