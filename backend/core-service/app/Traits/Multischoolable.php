<?php

namespace App\Traits;

use App\Scopes\SchoolScope;

trait Multischoolable
{
    /**
     * Boot the trait.
     * Menerapkan Global Scope secara otomatis ke model.
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new SchoolScope);

        /**
         * Otomatis mengisi kolom 'school_id' setiap kali data baru dibuat.
         * Ini mencegah Anda lupa mengisi school_id secara manual.
         */
        static::creating(function ($model) {
            if (session()->has('active_school_id')) {
                $model->school_id = session('active_school_id');
            }
        });
    }
}
