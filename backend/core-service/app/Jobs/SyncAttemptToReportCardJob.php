<?php
// app/Jobs/SyncAttemptToReportCardJob.php

namespace App\Jobs;

use App\Enums\GradeType;
use App\Models\ExamAttempt;
use App\Models\Grade;
use App\Models\ReportCard;
use App\Services\ReportCardService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class SyncAttemptToReportCardJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public function __construct(public int $attemptId) {}

    public function uniqueId(): string
    {
        return 'attempt-' . $this->attemptId;
    }

    public function handle(): void
    {
        /** @var ExamAttempt|null $attempt */
        $attempt = ExamAttempt::query()
            ->with([
                'exam:id,sub_classroom_subject_id,term_id,term_id,exams_type',
                'answers:id,exam_attempt_id,exam_question_id,points_awarded,selected_option_id',
                'answers.examQuestion:id,points',
                'answers.selectedOption:id,is_correct',
            ])
            ->find($this->attemptId);

        if (!$attempt || $attempt->status !== 'graded') return;

        $exam = $attempt->exam;
        if (!$exam) return;

        $gradeTypeStr = $this->mapExamType($exam->exams_type);
        if (!$gradeTypeStr) return;
        $gradeType = GradeType::from($gradeTypeStr);


        $finalScore = $attempt->score;

        if ($finalScore === null) {
            $sum = 0;
            foreach ($attempt->answers as $ans) {
                if ($ans->selectedOption && $ans->selectedOption->is_correct) {
                    $sum += (int) ($ans->examQuestion->points ?? 0);
                } elseif ($ans->points_awarded !== null) {
                    $sum += (int) $ans->points_awarded;
                }
            }
            $finalScore = $sum;
        }

        $finalScore = (int) round($finalScore);

        DB::transaction(function () use ($attempt, $exam, $finalScore, $gradeType) {
            $reportCard = ReportCard::firstOrCreate(
                [
                    'term_id'         => $exam->term_id,
                    'student_user_id'          => $attempt->user_id,
                    'sub_classroom_subject_id' => $exam->sub_classroom_subject_id,
                ],
                [
                    'final_score' => 0,
                    'grade'       => null,
                    'notes'       => null,
                    'created_by'  => $attempt->updated_by,
                ]
            );

            $grade = Grade::query()
                ->where('term_id', $exam->term_id)
                ->where('student_user_id',  $attempt->user_id)
                ->where('report_card_id',   $reportCard->id)
                ->where('type',             $gradeType)
                ->where(function ($q) use ($attempt) {
                    $q->where('exam_id', $attempt->exam_id)
                        ->orWhereNull('exam_id');
                })
                ->first();

            if (!$grade) {
                $grade = new Grade([
                    'term_id' => $exam->term_id,
                    'student_user_id'  => $attempt->user_id,
                    'report_card_id'   => $reportCard->id,
                    'type'             => $gradeType,
                    'created_by'       => $attempt->updated_by,
                ]);
            }

            $grade->exam_id    = $attempt->exam_id;
            $grade->score      = $finalScore;
            $grade->updated_by = $attempt->updated_by;
            $grade->save();

            app(ReportCardService::class)->recalculate($reportCard->fresh());
        });
    }


    private function mapExamType(string|\BackedEnum|null $t): ?string
    {
        if ($t instanceof \BackedEnum) $t = $t->value;

        $t = strtolower((string)$t);
        return match ($t) {
            'mid_exam', 'midterm', 'uts', 'sts'   => 'mid_exam',
            'final_exam', 'final', 'uas', 'sas'   => 'final_exam',
            'assignment', 'tugas'                 => 'assignment',
            default                               => null,
        };
    }
}
