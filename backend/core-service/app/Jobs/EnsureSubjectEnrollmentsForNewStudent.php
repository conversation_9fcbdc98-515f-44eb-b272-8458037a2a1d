<?php

namespace App\Jobs;

use App\Models\SubClassroomSubject;
use App\Models\SubClassroomSubjectHasStudent;
use App\Models\SubjectEnrollment;
use App\Models\Term;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class EnsureSubjectEnrollmentsForNewStudent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $subClassroomId;
    protected int $studentUserId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $subClassroomId, int $studentUserId)
    {
        $this->subClassroomId = $subClassroomId;
        $this->studentUserId = $studentUserId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $term = Term::where('is_active', true)->orderByDesc('id')->first();
        if (!$term) return;

        $subjectIds = SubClassroomSubject::where('sub_classroom_id', $this->subClassroomId)
            ->pluck('id');
        if ($subjectIds->isEmpty()) return;

        $now = Carbon::now();
        $rows = [];
        foreach ($subjectIds as $scsId) {
            $rows[] = [
                'term_id' => $term->id,
                'sub_classroom_subject_id' => (int)$scsId,
                'user_id' => $this->studentUserId,
                'is_enrolled' => false,
                'enrolled_at' => null,
                'unenrolled_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::transaction(function () use ($rows) {
            SubClassroomSubjectHasStudent::upsert(
                $rows,
                ['term_id', 'sub_classroom_subject_id', 'user_id'],
                [] 
            );
        });
    }
}
