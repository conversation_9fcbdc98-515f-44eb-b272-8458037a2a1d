<?php

namespace App\Jobs;

use App\Models\SubClassroomSubject;
use App\Models\SubClassroomSubjectHasStudent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class CleanupSubjectEnrollmentsForRemovedStudent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $subClassroomId;
    protected int $studentUserId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $subClassroomId, int $studentUserId)
    {
        $this->subClassroomId = $subClassroomId;
        $this->studentUserId = $studentUserId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $subjectIds = SubClassroomSubject::where('sub_classroom_id', $this->subClassroomId)
            ->pluck('id');

        if ($subjectIds->isEmpty()) return;

        DB::transaction(function () use ($subjectIds) {
            SubClassroomSubjectHasStudent::whereIn('sub_classroom_subject_id', $subjectIds)
                ->where('user_id', $this->studentUserId)
                ->delete();
        });
    }
}
