<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SchoolScope implements Scope
{
    /**
     * Terapkan scope ke query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        // 1. Periksa dulu apakah pengguna adalah Super Admin.
        // Jika ya, jangan lakukan apa-apa. Biarkan mereka melihat semua data.
        if (Auth::check() && Auth::user()->is_superadmin) {
            return; // Keluar dari scope
        }

        // 2. Jika bukan Super Admin, filter data berdasarkan sekolah yang aktif di session.
        if (Session::has('active_school_id')) {
            $builder->where($model->getTable() . '.school_id', Session::get('active_school_id'));
        }
    }
}
