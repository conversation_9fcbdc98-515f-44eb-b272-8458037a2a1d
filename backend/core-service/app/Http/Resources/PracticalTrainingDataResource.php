<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PracticalTrainingDataResource extends JsonResource
{
    /**
     * Transform the resource into an array (no nested relations).
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                      => $this->id,
            'practical_training_id'   => $this->practical_training_id,
            'learning_objective'      => $this->learning_objective,
            'final_score'             => isset($this->final_score) ? (float) $this->final_score : null,
            'description'             => $this->description,
            'created_at'              => $this->created_at,
            'updated_at'              => $this->updated_at,
        ];
    }
}
