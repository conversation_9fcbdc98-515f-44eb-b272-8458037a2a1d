<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $now = now();
        $data = [
            'id' => $this->id,
            'title' => $this->title,
            'exams_type' => $this->exams_type,
            'description' => $this->description,
            'is_published' => $this->is_published,
            'start_datetime' => $this->start_datetime,
            'end_datetime' => $this->end_datetime,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_shuffled' => $this->is_shuffled,
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                    'email' => $this->creator->email,
                ];
            }),
            'sub_classroom_subject_id' => $this->whenLoaded('subClassroomSubject', function () {
                return $this->subClassroomSubject->id;
            }),
            'questions_count' => $this->questions_count ?? 0,

            'term_id' => $this->term_id,
            'term' => $this->whenLoaded('term', fn() => [
                'id' => $this->term->id,
                'name' => $this->term->name,
                'academic_year_id' => $this->term->academic_year_id,
                'order' => (int) $this->term->order,
                'start_date' => optional($this->term->start_date)->format('Y-m-d'),
                'end_date' => optional($this->term->end_date)->format('Y-m-d'),
            ]),
        ];

        if (
            $this->relationLoaded('subClassroomSubject') &&
            $this->subClassroomSubject &&
            $this->subClassroomSubject->subClassroom
        ) {
            $data['sub_classroom'] = [
                'id' => $this->subClassroomSubject->subClassroom->id,
                'name' => $this->subClassroomSubject->subClassroom->full_name,
            ];
        }

        // Add subject as a top-level property if available
        if (
            $this->relationLoaded('subClassroomSubject') &&
            $this->subClassroomSubject &&
            $this->subClassroomSubject->subject
        ) {
            $data['subject'] = [
                'id' => $this->subClassroomSubject->subject->id,
                'name' => $this->subClassroomSubject->subject->name,
            ];
        }

        if (
            $this->relationLoaded('subClassroomSubject') &&
            $this->subClassroomSubject &&
            $this->subClassroomSubject->teacher
        ) {
            $data['teacher'] = [
                'id' => $this->subClassroomSubject->teacher->id,
                'name' => $this->subClassroomSubject->teacher->name,
            ];
        }

        if ($this->relationLoaded('attempts')) {
            if ($this->attempts->isNotEmpty()) {
                $latestAttempt = $this->attempts->first();
                $data['attempt'] = [
                    'id' => $latestAttempt->id,
                    'score' => $latestAttempt->score,
                    'graded_at' => $latestAttempt->graded_at,
                ];
                if ($latestAttempt->status == 'in_progress' && $now > $this->end_datetime) {
                    $data['status'] = 'completed';
                } else {
                    $data['status'] = $latestAttempt->status;
                }
            } else {
                if ($this->is_published && $this->start_datetime && $now < $this->start_datetime) {
                    $data['status'] = 'waiting';
                } elseif ($this->end_datetime && $now > $this->end_datetime) {
                    $data['status'] = 'overdue';
                } else {
                    $data['status'] = 'ready';
                }
            }
        }

        return $data;
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function with($request)
    {
        return [
            'success' => true,
        ];
    }
}
