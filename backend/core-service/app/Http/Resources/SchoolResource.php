<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SchoolResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'address' => $this->address,
            'subdistrict' => $this->subdistrict,
            'district' => $this->district,
            'city' => $this->city,
            'province' => $this->province,
            'registration_number' => $this->registration_number,
            'year_founded' => $this->year_founded,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'website' => $this->website,
            'logo' => $this->logo,
            'foundation_id' => $this->foundation_id,
            'school_level_id' => $this->school_level_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'headmaster' => $this->headmaster(),
            'school_level' => $this->whenLoaded('schoolLevel'),
            'foundation' => $this->whenLoaded('foundation'),
        ];
    }
}
