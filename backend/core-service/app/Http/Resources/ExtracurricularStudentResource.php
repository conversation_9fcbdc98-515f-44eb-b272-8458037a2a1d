<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExtracurricularStudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $student   = $this->student;
        $sc        = $student?->subClassroom;
        $className = $sc?->full_name
            ?? trim(($sc?->classroom?->name ?? '') . ' - ' . ($sc?->sequence ?? ''), ' -');

        return [
            'id'                 => $this->id,
            'extracurricular_id' => $this->extracurricular_id,
            'term_id'            => $this->term_id,
            'student_user_id'    => $this->student_user_id,
            'student_name'       => $student?->name,
            'student_nisn'       => $student?->registration_number,
            'class_name'         => $className ?: null,
            'predicate'          => $this->predicate,
            'created_at'         => optional($this->created_at)?->toJSON(),
            'updated_at'         => optional($this->updated_at)?->toJSON(),
        ];
    }
}
