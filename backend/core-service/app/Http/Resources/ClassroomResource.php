<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassroomResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {

        $subjectIds = $this->whenLoaded('subClassrooms', function () {
            return $this->subClassrooms
                ->flatMap(fn($sc) => $sc->relationLoaded('subjects') ? $sc->subjects->pluck('id') : collect())
                ->filter()
                ->unique()
                ->values()
                ->map(fn($id) => (int) $id)
                ->all();
        });

        return [
            'id' => $this->id,
            'name' => $this->name,
            'sub_classrooms' => SubClassroomResource::collection($this->whenLoaded('subClassrooms')),
            'subject_ids'    => $subjectIds,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
