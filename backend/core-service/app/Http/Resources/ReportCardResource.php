<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportCardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'term_id' => $this->term_id,
            'student_user_id' => $this->student_user_id,
            'sub_classroom_subject_id' => $this->sub_classroom_subject_id,
            'final_score' => $this->final_score,
            'revision_score' => $this->revision_score,
            'grade' => $this->grade,

            'mid_term_note' => $this->mid_term_note,
            'final_term_note' => $this->final_term_note,
            'mid_locked_score' => $this->mid_locked_score,

            'mid_exported_at' => $this->mid_exported_at,
            'final_exported_at' => $this->final_exported_at,
            'final_locked_score' => $this->final_locked_score,

            'display_score' => $this->revision_score !== null ? (float)$this->revision_score : (float)$this->final_score,

            'class_teacher_note' => $this->class_teacher_note,


            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_enrolled' => (bool) ($this->is_enrolled ?? false),

            'grades' => GradeResource::collection($this->whenLoaded('grades')),
            'sub_classroom_subject' => new SubClassroomSubjectResource($this->whenLoaded('subClassroomSubject')),
        ];
    }
}
