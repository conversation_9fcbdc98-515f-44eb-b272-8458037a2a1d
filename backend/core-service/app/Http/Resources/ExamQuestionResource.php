<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ExamQuestionResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'exam_id'             => $this->exam_id,
            'content'             => $this->content,
            'answer_key_essay'    => $this->answer_key_essay,
            'points'              => $this->points,
            'order_index'         => $this->order_index,
            'media_questions_url' => $this->media_questions ? Storage::disk('gcs')->temporaryUrl($this->media_questions, now()->addMinutes(30)) : null,
            'question_type'       => $this->question_type,

            'kd_number'           => $this->kd_number,
            'learning_outcome'    => $this->learning_outcome,
            'competency_indicator'=> $this->competency_indicator,
            'level_kognitif'      => $this->level_kognitif,

            'answer_options'      => ExamQuestionHasAnswerOptionResource::collection($this->whenLoaded('answerOptions')),
            'created_at'          => $this->created_at,
            'updated_at'          => $this->updated_at,
        ];
    }
}
