<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubjectEnrollmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                      => $this->id,
            'term_id'                 => $this->term_id,
            'sub_classroom_subject_id'=> $this->sub_classroom_subject_id,
            'student_user_id'         => $this->student_user_id,
            'is_enrolled'             => (bool) $this->is_enrolled,
            'note'                    => $this->note,
            'enrolled_at'             => $this->enrolled_at,
            'unenrolled_at'           => $this->unenrolled_at,
            'created_at'              => $this->created_at,
            'updated_at'              => $this->updated_at,

            'subject' => new SubjectResource($this->whenLoaded('subject')),
            'student' => new UserResource($this->whenLoaded('student')),
        ];
    }
}
