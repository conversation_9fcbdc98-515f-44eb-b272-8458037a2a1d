<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ExamAttemptAnswerResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'exam_attempt_id'   => $this->exam_attempt_id,
            'selected_option_id' => $this->selected_option_id,
            'essay_answer'      => $this->essay_answer,
            'points_awarded'    => $this->points_awarded,
            'order_index'       => $this->order_index,
            // gunakan S3 URL
            'media_answer_url'  => $this->media_answer ?  Storage::disk('gcs')->temporaryUrl($this->media_answer, now()->addMinutes(30)) : null,

            'question' => $this->when($this->relationLoaded('examQuestion'), [
                'content'             => $this->examQuestion->content,
                'answer_key_essay'    => $this->when(
                    auth()->check()
                        && method_exists(auth()->user(), 'hasRole')
                        && !auth()->user()->hasRole('student'),
                    $this->examQuestion->answer_key_essay
                ),
                'points'              => $this->examQuestion->points,
                'media_questions_url' => $this->examQuestion->media_questions
                    ? Storage::disk('gcs')->temporaryUrl($this->examQuestion->media_questions, now()->addMinutes(30)) : null,
                'question_type'       => $this->examQuestion->question_type,
                'options'             => $this->examQuestion->answerOptions->map(fn($o) => [
                    'id'      => $o->id,
                    'content' => $o->content,
                ]),
            ]),
        ];
    }
}
