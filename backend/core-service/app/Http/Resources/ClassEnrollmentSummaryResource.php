<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassEnrollmentSummaryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $combinations = (int) ($this->resource['combinations'] ?? 0);
        $checked      = (int) ($this->resource['enrolled_checked'] ?? 0);
        $percent      = $combinations > 0 ? round(($checked / $combinations) * 100) : 0;

        return [
            'term_id'           => (int) ($this->resource['term_id'] ?? 0),
            'sub_classroom_id'  => (int) ($this->resource['sub_classroom_id'] ?? 0),
            'combinations'      => $combinations,
            'existing_rows'     => (int) ($this->resource['existing_rows'] ?? 0),
            'enrolled_checked'  => $checked,
            'percent_checked'   => $percent,
            'updated_at'        => now()->toISOString(),
        ];
    }
}
