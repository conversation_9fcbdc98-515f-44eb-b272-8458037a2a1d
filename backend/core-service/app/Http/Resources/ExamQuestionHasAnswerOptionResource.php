<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ExamQuestionHasAnswerOptionResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                        => $this->id,
            'exam_question_id'          => $this->exam_question_id,
            'content'                   => $this->content,
            'is_correct'                => (bool) $this->is_correct,
            'order_index'               => $this->order_index,
            'media_answer_options_url'  => $this->media_answer_options ? Storage::disk('gcs')->temporaryUrl($this->media_answer_options, now()->addMinutes(30)) : null,
            'question_type'       => $this->question_type,
            'created_at'                => $this->created_at,
            'updated_at'                => $this->updated_at,
        ];
    }
}
