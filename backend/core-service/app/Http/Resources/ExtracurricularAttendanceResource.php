<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExtracurricularAttendanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'extracurricular_id' => $this->extracurricular_id,
            'checked_in_at' => $this->checked_in_at,
            'is_present' => $this->is_present,
            'notes' => $this->notes,
            'photo' => $this->photo ? asset('storage/' . $this->photo) : null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user' => new UserResource($this->whenLoaded('user')),
            'extracurricular' => new ExtracurricularResource($this->whenLoaded('extracurricular')),
        ];
    }
}
