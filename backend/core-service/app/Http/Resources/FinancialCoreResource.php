<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FinancialCoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'schoolId' => $this->school_id,
            'studentUserId' => $this->student_user_id,
            'parentUserId' => $this->parent_user_id,
            'transactionNumber' => $this->transaction_number,
            'referenceType' => $this->reference_type->value ?? $this->reference_type,
            'referenceTypeLabel' => $this->reference_type->label() ?? $this->reference_type,
            'referenceId' => $this->reference_id,
            'debitAmount' => $this->debit_amount,
            'creditAmount' => $this->credit_amount,
            'balance' => $this->balance,
            'formattedDebit' => $this->formatted_debit,
            'formattedCredit' => $this->formatted_credit,
            'formattedBalance' => $this->formatted_balance,
            'netAmount' => $this->net_amount,
            'notes' => $this->notes,
            'status' => $this->status,
            'transactionDate' => $this->transaction_date,
            'transactionTime' => $this->transaction_time,
            'transactionTimezone' => $this->transaction_timezone,
            'isDebit' => $this->isDebit(),
            'isCredit' => $this->isCredit(),
            'createdAt' => $this->created_at,
            'updatedAt' => $this->updated_at,
            
            // Relationships
            'student' => $this->whenLoaded('student', function () {
                return [
                    'id' => $this->student->id,
                    'name' => $this->student->name,
                    'email' => $this->student->email,
                ];
            }),
            'parent' => $this->whenLoaded('parent', function () {
                return [
                    'id' => $this->parent->id,
                    'name' => $this->parent->name,
                    'email' => $this->parent->email,
                ];
            }),
            'school' => $this->whenLoaded('school', function () {
                return [
                    'id' => $this->school->id,
                    'name' => $this->school->name,
                ];
            }),
        ];
    }
}
