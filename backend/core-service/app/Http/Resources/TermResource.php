<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TermResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id'                => $this->id,
            'name'              => $this->name,
            'academic_year_id'  => $this->academic_year_id,
            'start_date'        => $this->start_date,
            'end_date'          => $this->end_date,
            'created_at'        => $this->created_at,
            'updated_at'        => $this->updated_at,

            'academic_year'     => new AcademicYearResource($this->whenLoaded('academicYear')),
        ];
    }
}
