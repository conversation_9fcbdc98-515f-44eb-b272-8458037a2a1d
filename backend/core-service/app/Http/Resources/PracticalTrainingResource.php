<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PracticalTrainingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'term_id'             => $this->term_id,
            'student_user_id'     => $this->student_user_id,
            'program_of_study'    => $this->program_of_study,
            'specialization'      => $this->specialization,
            'place'               => $this->place,
            'period_start'        => $this->period_start,
            'period_end'          => $this->period_end,
            'instructor_name'     => $this->instructor_name,
            'mentor_name'         => $this->mentor_name,

            'sick'                => $this->sick,
            'leave'               => $this->leave,
            'present'             => $this->present,
            'alpha'               => $this->alpha,

            'class_teacher_note'  => $this->class_teacher_note,

            'created_at'          => $this->created_at,
            'updated_at'          => $this->updated_at,

            'student_user' => new UserResource($this->whenLoaded('student')),
            'term'        => new TermResource($this->whenLoaded('term')),
        ];
    }
}
