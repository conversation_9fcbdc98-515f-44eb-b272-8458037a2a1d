<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExamAttemptDetailResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'exam_id'           => $this->exam_id,
            'user_id'           => $this->user_id,
            'start_datetime'    => $this->start_datetime,
            'submit_datetime'   => $this->submit_datetime,
            'exam' => $this->when($this->relationLoaded('exam'), [
                'id' => $this->exam->id,
                'title' => $this->exam->title,
                'sub_classroom' => $this->whenLoaded('exam', function () {
                    return [
                        'id' => $this->exam->subClassroomSubject->subclassroom->id,
                        'name' => $this->exam->subClassroomSubject->subClassroom->full_name ?? null,
                    ];
                }),
                'subject' => $this->whenLoaded('exam', function () {
                    return [
                        'id' => $this->exam->subClassroomSubject->subject->id,
                        'name' => $this->exam->subClassroomSubject->subject->name ?? null,
                    ];
                }),
            ]),
            'student' => $this->when($this->relationLoaded('student'), [
                'id' => $this->student->id,
                'name' => $this->student->name,
            ]),
            'answers'           => ExamAttemptAnswerResource::collection($this->whenLoaded('answers')),
        ];
    }
}
