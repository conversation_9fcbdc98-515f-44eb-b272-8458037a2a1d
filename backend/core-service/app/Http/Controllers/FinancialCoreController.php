<?php

namespace App\Http\Controllers;

use App\Http\Requests\FinancialCore\StoreFinancialCoreRequest;
use App\Http\Requests\FinancialCore\UpdateFinancialCoreRequest;
use App\Http\Resources\FinancialCoreResource;
use App\Models\FinancialCore;
use App\Services\FinancialCoreService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class FinancialCoreController extends Controller
{
    protected FinancialCoreService $service;

    public function __construct(FinancialCoreService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All FinancialCore
     */
    public function index(Request $request)
    {
        try {
            $financialCores = $this->service->paginate($request);
            return $this->successResponse(FinancialCoreResource::collection($financialCores));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve financialCores', $e->getMessage(), 500);
        }
    }

    /**
     * Store FinancialCore
     */
    public function store(StoreFinancialCoreRequest $request)
    {
        try {
            $financialCore = $this->service->store($request);
            return $this->successResponse(new FinancialCoreResource($financialCore), 201, 'FinancialCore created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create financialCore', $e->getMessage(), 500);
        }
    }

    /**
     * Get FinancialCore Detail
     */
    public function show(FinancialCore $financialCore)
    {
        try {
            return $this->successResponse(new FinancialCoreResource($this->service->show($financialCore)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('FinancialCore not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve financialCore', $e->getMessage(), 500);
        }
    }

    /**
     * Update FinancialCore
     */
    public function update(UpdateFinancialCoreRequest $request, FinancialCore $financialCore)
    {
        try {
            $financialCore = $this->service->update($financialCore, $request);
            return $this->successResponse(new FinancialCoreResource($financialCore), 200, 'FinancialCore updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('FinancialCore not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update financialCore', $e->getMessage(), 500);
        }
    }

    /**
     * Delete FinancialCore
     */
    public function destroy(FinancialCore $financialCore)
    {
        try {
            $this->service->delete($financialCore);
            return $this->successResponse('FinancialCore deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('FinancialCore not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete financialCore', $e->getMessage(), 500);
        }
    }
}
