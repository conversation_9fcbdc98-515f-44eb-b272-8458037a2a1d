<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResource;
use App\Models\School;
use App\Services\TeacherService;
use Exception;
use Illuminate\Http\Request;

class TeacherController extends Controller
{
    protected TeacherService $service;

    public function __construct(TeacherService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            return $this->successResponse(UserResource::collection(
                $this->service->paginate($request)
            ));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve teachers', $e->getMessage(), 500);
        }
    }

    public function import(Request $request)
    {
        $request->validate(['file' => 'required|file|mimes:xlsx,xls']);

        try {
            // Ambil dari header saja
            $schoolId = (int) $request->header('X-Active-School-Id');

            if (!$schoolId) {
                return response()->json([
                    'status' => false,
                    'message' => 'school_id wajib dikirim lewat header X-Active-School-Id.'
                ], 422);
            }

            $this->service->import($request->file('file'), $schoolId);

            return response()->json([
                'status' => true,
                'message' => 'Import data Guru berhasil'
            ], 201);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => false,
                'message' => 'Import gagal: ' . $e->getMessage()
            ], 422);
        }
    }

    public function export()
    {
        try {
            return $this->service->export();
        } catch (Exception $e) {
            return $this->errorResponse('Failed to export teachers', $e->getMessage(), 500);
        }
    }

    public function count()
    {
        try {
            $activeSchoolId = session('active_school_id');
            $count = School::find($activeSchoolId)->teacherCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count teachers', $e->getMessage(), 500);
        }
    }
}
