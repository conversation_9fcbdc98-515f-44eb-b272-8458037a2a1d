<?php

namespace App\Http\Controllers;

use App\Http\Requests\Exam\AssignExamRequest;
use App\Http\Requests\Exam\DuplicateExamRequest;
use App\Http\Requests\Exam\StartExamRequest;
use App\Http\Requests\Exam\StoreExamRequest;
use App\Http\Requests\Exam\UpdateExamRequest;
use App\Http\Resources\ExamAttemptDetailResource;
use App\Http\Resources\ExamAttemptResource;
use App\Http\Resources\ExamResource;
use App\Http\Resources\ExamResultResource;
use App\Models\Exam;
use App\Models\User;
use App\Services\ExamService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ExamController extends Controller
{
    protected ExamService $service;

    public function __construct(ExamService $service)
    {
        parent::__construct();
        $this->service = $service;

        $this->middleware('role:student')->only(['start', 'submit']);
        $this->middleware('role:teacher,superadmin,foundation_admin,school_admin')->only(['store', 'update', 'destroy', 'publish', 'unpublish', 'grade', 'showResult', 'duplicate']);
        $this->middleware('role:teacher,student,superadmin,foundation_admin,school_admin')->only(['index', 'show']);
    }

    /**
     * Get All Exam
     */
    public function index(Request $request)
    {
        try {
            $exams = $this->service->paginate($request);
            return $this->successResponse(ExamResource::collection($exams));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve exams', $e->getMessage(), 500);
        }
    }

    /**
     * Store Exam
     */
    public function store(StoreExamRequest $request)
    {
        try {
            $exam = $this->service->store($request);
            return $this->successResponse(new ExamResource($exam), 201, 'Exam created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create exam', $e->getMessage(), 500);
        }
    }

    /**
     * Get Exam Detail
     */
    public function show(Exam $exam)
    {
        try {
            return $this->successResponse(new ExamResource($this->service->show($exam)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve exam', $e->getMessage(), 500);
        }
    }

    /**
     * Update Exam
     */
    public function update(UpdateExamRequest $request, Exam $exam)
    {
        try {
            $exam = $this->service->update($request, $exam->id);
            return $this->successResponse(new ExamResource($exam), 200, 'Exam updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update exam', $e->getMessage(), 500);
        }
    }

    /**
     * Delete Exam
     */
    public function destroy(Exam $exam)
    {
        try {
            $this->service->delete($exam);
            return $this->successResponse('Exam deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete exam', $e->getMessage(), 500);
        }
    }

    public function publish(Exam $exam)
    {
        try {
            $this->service->publish($exam);
            return $this->successResponse('Exam published successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to publish exam', $e->getMessage(), 500);
        }
    }

    public function unpublish(Exam $exam)
    {
        try {
            $this->service->unpublish($exam);
            return $this->successResponse('Exam unpublished successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to unpublish exam', $e->getMessage(), 500);
        }
    }

    public function start(Exam $exam, StartExamRequest $request)
    {
        try {
            return $this->successResponse(new ExamAttemptResource($this->service->start($exam, $request)), 200, 'Exam started successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to start exam', $e->getMessage(), 500);
        }
    }

    public function submit(Exam $exam)
    {
        try {
            return $this->successResponse(new ExamAttemptResource($this->service->submit($exam)), 200, 'Exam submitted successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to submit exam', $e->getMessage(), 500);
        }
    }

    public function grade(Exam $exam)
    {
        try {
            $this->service->grade($exam);
            return $this->successResponse('Exam graded successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to grade exam', $e->getMessage(), 500);
        }
    }

    public function showResult(Exam $exam, Request $request)
    {
        try {
            $examResults = $this->service->paginateResult($exam, $request);
            return $this->successResponse(
                ExamResultResource::collection($examResults)
            );
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve exams', $e->getMessage(), 500);
        }
    }

    public function countStudentExam(Exam $exam)
    {
        try {
            $examResults = $this->service->countStudentExam($exam);
            return $this->successResponse($examResults);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve exams', $e->getMessage(), 500);
        }
    }

    public function resetStudentExam(Exam $exam, User $student)
    {
        try {
            $this->service->resetStudentExam($exam, $student);

            return $this->successResponse(null, 200, 'Reset Student Exam successfully.');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to reset student exams', $e->getMessage(), 500);
        }
    }

    public function forceSubmitStudentExam(Exam $exam, User $student)
    {
        try {
            $this->service->forceSubmitStudentExam($exam, $student);

            return $this->successResponse(null, 200, 'Exam has been forcibly submitted.');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to force submit exam', $e->getMessage(), 500);
        }
    }

    public function getStudentAttempt(Exam $exam, User $student, $attemptId)
    {
        try {
            $examAttempt = $this->service->getStudentAttempt($exam->id, $student->id, $attemptId);
            return $this->successResponse(new ExamAttemptDetailResource($examAttempt));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamAttempt not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve result details', $e->getMessage(), 500);
        }
    }

    public function duplicateExam(DuplicateExamRequest $request, Exam $exam)
    {
        try {

            $copy = $this->service->duplicateExam($exam, $request->validated());
            return $this->successResponse(new ExamResource($copy), 201, 'Exam duplicated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Exam not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to Copy Exam', $e->getMessage(), 500);
        }
    }

    public function assignTeacher(Request $request, Exam $exam)
    {
        $request->validate([
            'sub_classroom_subject_id' => ['required', 'integer', 'exists:sub_classroom_subjects,id'],
        ]);

        try {
            $copy = $this->service->assignDirectlyToSubClassroomSubject($exam, $request->input('sub_classroom_subject_id'));
            return $this->successResponse(new ExamResource($copy), 201, 'Exam assigned successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('SubClassroomSubject not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign exam', $e->getMessage(), 500);
        }
    }


    public function download(Request $request)
    {
        $validated = $request->validate([
            'exam_id' => ['required', 'integer', 'exists:exams,id'],
        ]);

        return $this->service->exportUnifiedByExamId((int)$validated['exam_id']);
    }

    public function downloadGrid(Request $request)
    {
        $examId = $request->integer('exam_id');
        abort_if(!$examId, 400, 'Exam ID is required.');

        return $this->service->downloadGrid($request, $examId);
    }
}
