<?php

namespace App\Http\Controllers;

use App\Http\Requests\Extracurricular\EnrollStudentRequest;
use App\Http\Requests\Extracurricular\UpdatePredicateRequest;
use App\Http\Resources\ExtracurricularStudentResource;
use App\Services\ExtracurricularStudentService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ExtracurricularStudentController extends Controller
{
    use ApiResponse;

    public function __construct(private ExtracurricularStudentService $service) {}

    /**
     * GET /extracurricular/students?term_id=&class_id=&per_page=
     * List peserta ekskul (ekskul otomatis dari guru yang login).
     */
    public function index(Request $request)
    {
        try {
            $termId   = $request->integer('term_id') ?: null;
            $classId  = $request->integer('class_id') ?: null;
            $perPage  = max(1, (int) $request->integer('per_page') ?: 10);

            // service mengembalikan LengthAwarePaginator
            $paginator = $this->service->paginate($termId, $classId, $perPage);

            // bungkus dengan Resource::collection($paginator)
            $result = ExtracurricularStudentResource::collection($paginator);

            // successResponse akan mendeteksi AnonymousResourceCollection
            // dan mengisi page & total_records
            return $this->successResponse($result, 200);
        } catch (\Throwable $e) {
            return $this->errorResponse(
                'Failed to retrieve extracurricular students',
                app()->isLocal() ? $e->getMessage() : null,
                500
            );
        }
    }

    public function store(EnrollStudentRequest $request)
    {
        try {
            $payload = $request->validated();

            $row = $this->service->enroll(
                (int) $payload['student_user_id'],
                (int) $payload['term_id']
            );

            return $this->successResponse(
                // single row → langsung pakai Resource biar bentuk sama
                (new ExtracurricularStudentResource($row)),
                201,
                'Enrolled'
            );
        } catch (ValidationException $ve) {
            return $this->errorResponse('Failed to enroll student', $ve->errors(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to enroll student', app()->isLocal() ? $e->getMessage() : null, 500);
        }
    }

    public function update(UpdatePredicateRequest $request, int $id)
    {
        try {
            $row = $this->service->updatePredicate($id, (string) $request->input('predicate'));

            return $this->successResponse(
                (new ExtracurricularStudentResource($row)),
                200,
                'Updated'
            );
        } catch (ValidationException $ve) {
            return $this->errorResponse('Failed to update predicate', $ve->errors(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to update predicate', app()->isLocal() ? $e->getMessage() : null, 500);
        }
    }

    public function destroy(int $id)
    {
        try {
            $this->service->remove($id);
            return $this->successResponse(['message' => 'Berhasil dihapus'], 200, 'Deleted');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to delete extracurricular student', app()->isLocal() ? $e->getMessage() : null, 500);
        }
    }

    public function listStudentsByClass(Request $request, int $subClassroomId)
    {
        try {
            $schoolId = $request->user()->active_school_id
                ?? $request->integer('school_id')
                ?? session('active_school_id');

            $items = $this->service->listStudentsByClass($subClassroomId, $schoolId);

            return $this->successResponse($items, 200);
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to retrieve students', app()->isLocal() ? $e->getMessage() : null, 500);
        }
    }
}
