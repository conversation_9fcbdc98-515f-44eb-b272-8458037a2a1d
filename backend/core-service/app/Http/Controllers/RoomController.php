<?php

namespace App\Http\Controllers;

use App\Http\Requests\Room\StoreRoomRequest;
use App\Http\Requests\Room\UpdateRoomRequest;
use App\Http\Resources\RoomResource;
use App\Models\Room;
use App\Services\RoomService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class RoomController extends Controller
{
    protected RoomService $service;

    public function __construct(RoomService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All Room
     */
    public function index(Request $request)
    {
        try {
            $rooms = $this->service->paginate($request);
            return $this->successResponse(RoomResource::collection($rooms));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve rooms', $e->getMessage(), 500);
        }
    }

    /**
     * Store Room
     */
    public function store(StoreRoomRequest $request)
    {
        try {
            $room = $this->service->store($request);
            return $this->successResponse(new RoomResource($room), 201, 'Room created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create room', $e->getMessage(), 500);
        }
    }

    /**
     * Get Room Detail
     */
    public function show(Room $room)
    {
        try {
            return $this->successResponse(new RoomResource($this->service->show($room)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Room not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve room', $e->getMessage(), 500);
        }
    }

    /**
     * Update Room
     */
    public function update(UpdateRoomRequest $request, Room $room)
    {
        try {
            $room = $this->service->update($room, $request);
            return $this->successResponse(new RoomResource($room), 200, 'Room updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Room not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update room', $e->getMessage(), 500);
        }
    }

    /**
     * Delete Room
     */
    public function destroy(Room $room)
    {
        try {
            $this->service->delete($room);
            return $this->successResponse('Room deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Room not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete room', $e->getMessage(), 500);
        }
    }
}
