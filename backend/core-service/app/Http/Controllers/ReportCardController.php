<?php

namespace App\Http\Controllers;

use App\Exports\NotesTemplateExport;
use App\Http\Requests\ReportCard\RevisionScoreRequest;
use App\Http\Requests\ReportCard\StoreReportCardRequest;
use App\Http\Requests\ReportCard\UpdateReportCardRequest;
use App\Http\Requests\ReportCard\UpsertAssignmentGradesRequest;
use App\Http\Requests\ReportCard\UpsertAssignmentGradesSmartRequest;
use App\Http\Requests\ReportCard\UpsertNoteByKeysRequest;
use App\Http\Requests\ReportCard\ImportNotesRequest;
use App\Http\Requests\ReportCard\UpdateClassTeacherNoteRequest;
use App\Http\Resources\ReportCardResource;
use App\Models\ReportCard;
use App\Models\Term;
use App\Models\User;
use App\Services\ReportCardService;
use Dompdf\Dompdf;
use Dompdf\Options;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelFormat;
use Symfony\Component\HttpFoundation\Response;

class ReportCardController extends Controller
{
    protected ReportCardService $service;

    public function __construct(ReportCardService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /* =========================
     * Helpers: timeout & DB guard
     * ========================= */
    private function applyLongRunGuards(int $seconds = 120, string $memory = '512M'): void
    {
        // PHP execution time
        ini_set('max_execution_time', (string)$seconds);
        @set_time_limit($seconds);

        // Network sockets (mis. fetch asset)
        ini_set('default_socket_timeout', (string)min($seconds, 30)); // 15–30s per koneksi

        // Memory
        ini_set('memory_limit', $memory);
    }

    private function applyDbStatementTimeout(int $seconds = 120): void
    {
        try {
            // PostgreSQL
            DB::statement("SET LOCAL statement_timeout = '{$seconds}s'");
        } catch (\Throwable $e) {
            // MySQL fallback (abaikan jika bukan MySQL)
            try {
                DB::statement('SET SESSION MAX_EXECUTION_TIME = ' . ($seconds * 1000));
            } catch (\Throwable $ignore) {
            }
        }
    }

    /* =========================
     * Get All ReportCard
     * ========================= */
    public function index(Request $request)
    {
        try {
            $reportCards = $this->service->paginate($request);
            return $this->successResponse(ReportCardResource::collection($reportCards));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve reportCards', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Store
     * ========================= */
    public function store(StoreReportCardRequest $request)
    {
        try {
            // kemungkinan berat jika triggers agregasi banyak
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $reportCard = $this->service->store($request);
            return $this->successResponse(new ReportCardResource($reportCard), 201, 'ReportCard created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create reportCard', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Show
     * ========================= */
    public function show(ReportCard $reportCard)
    {
        try {
            return $this->successResponse(new ReportCardResource($this->service->show($reportCard)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ReportCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve reportCard', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Update
     * ========================= */
    public function update(UpdateReportCardRequest $request, ReportCard $reportCard)
    {
        try {
            // update bisa memicu recalculation
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $reportCard = $this->service->update($reportCard, $request);
            return $this->successResponse(new ReportCardResource($reportCard), 200, 'ReportCard updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ReportCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update reportCard', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Delete
     * ========================= */
    public function destroy(ReportCard $reportCard)
    {
        try {
            $this->service->delete($reportCard);
            return $this->successResponse('ReportCard deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ReportCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete reportCard', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Upsert assignments (manual list)
     * ========================= */
    public function assignmentsUpsert(UpsertAssignmentGradesRequest $request, ReportCard $reportCard)
    {
        try {
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $result = $this->service->upsertAssignments($reportCard, $request);
            return $this->successResponse($result, 200, 'Assignments updated successfully');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to update assignments', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Upsert assignments (smart by keys)
     * ========================= */
    public function assignmentsUpsertSmart(UpsertAssignmentGradesSmartRequest $request)
    {
        try {
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $reportCard = $this->service->ensureReportCardByKeys(
                (int) $request->input('term_id'),
                (int) $request->input('student_user_id'),
                (int) $request->input('sub_classroom_subject_id')
            );

            $result = $this->service->upsertAssignments($reportCard, $request);
            return $this->successResponse($result, 200, 'Assignments upserted successfully');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to upsert assignments', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Revision score
     * ========================= */
    public function revisionScore($reportCardId, RevisionScoreRequest $request)
    {
        try {
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $this->service->revisionScore($reportCardId, $request->validated());
            return $this->successResponse(null, 200, 'Revision score updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('examattempt not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve grade', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Upsert note by keys
     * ========================= */
    public function upsertNoteByKeys(UpsertNoteByKeysRequest $request)
    {
        try {
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $rc = $this->service->upsertNoteByKeys($request->validated());
            return $this->successResponse(new ReportCardResource($rc), 200, 'Note saved');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to upsert note', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Import notes (Excel -> DB)
     * ========================= */
    public function importNotes(ImportNotesRequest $request)
    {
        try {
            // ini cenderung berat (read file + validasi + update massal)
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $res = $this->service->importNotes($request->validated() + ['file' => $request->file('file')]);
            return $this->successResponse($res, 200, 'Notes imported');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to import notes', $e->getMessage(), 500);
        }
    }

    /* =========================
     * Download template notes (Excel)
     * ========================= */
    public function notesTemplate(Request $request)
    {
        $request->validate([
            'term_id'                  => 'required|integer|exists:terms,id',
            'sub_classroom_subject_id' => 'required|integer|exists:sub_classroom_subjects,id',
            'format'                   => 'nullable|string|in:xlsx,csv',
        ]);

        // excel export bisa lama -> 2 menit
        $this->applyLongRunGuards();
        $this->applyDbStatementTimeout();

        $termId    = (int) $request->term_id;
        $scsId     = (int) $request->sub_classroom_subject_id;
        $format    = $request->get('format', 'xlsx');

        $rows      = $this->service->getStudentsForTermSubject($termId, $scsId);
        $export    = new NotesTemplateExport(collect($rows));
        $filename  = "notes_template_term{$termId}_scs{$scsId}.{$format}";
        $writer    = $format === 'csv' ? ExcelFormat::CSV : ExcelFormat::XLSX;

        return Excel::download($export, $filename, $writer);
    }

    /* =========================
     * Get/Update class teacher note
     * ========================= */
    public function showClassTeacherNote(Request $request, int $term, int $student)
    {
        try {
            $data = $this->service->getClassTeacherNote($term, $student);
            return $this->successResponse(['class_teacher_note' => $data], 200);
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to fetch class teacher note', $e->getMessage(), 500);
        }
    }

    public function updateClassTeacherNote(UpdateClassTeacherNoteRequest $request, int $term, int $student)
    {
        try {
            $this->applyLongRunGuards();
            $this->applyDbStatementTimeout();

            $payload = $request->validated();
            $this->service->updateClassTeacherNote($term, $student, $payload['class_teacher_note'] ?? null);

            return $this->successResponse(null, 200, 'Class teacher note updated');
        } catch (\Throwable $e) {
            return $this->errorResponse('Failed to update class teacher note', $e->getMessage(), 500);
        }
    }

    /* =========================
     * PDF report
     * ========================= */
    public function reportDownload(Request $request, Term $term, User $student)
    {
        $this->applyLongRunGuards();
        $this->applyDbStatementTimeout();

        $request->validate([
            'stage' => 'required|in:mid,final',
            'graduation_status' => 'nullable|in:pass,fail',
        ]);

        $stage = $request->string('stage')->toString();
        $graduationStatus = $request->input('graduation_status');

        $data = $this->service->buildReportData($term->id, $student->id, $stage);
        $data['is_final'] = $stage === 'final';
        if ($data['is_final'] && in_array($graduationStatus, ['pass', 'fail'], true)) {
            $data['graduation_status'] = $graduationStatus;
        } else {
            unset($data['graduation_status']);
        }

        $view = $stage === 'mid' ? 'pdf.report_mid' : 'pdf.report_final';
        $html = view($view, $data)->render();

        try {
            $options = new Options();
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isRemoteEnabled', true);
            $options->set('defaultFont', 'Times-Roman');

            $httpContext = stream_context_create([
                'http' => ['timeout' => 15, 'follow_location' => 1],
                'ssl'  => ['verify_peer' => true, 'verify_peer_name' => true],
            ]);
            $options->setHttpContext($httpContext);

            $dompdf = new Dompdf($options);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->loadHtml($html);
            $dompdf->render();

            $this->service->markExportedByStudent($term->id, $student->id, $stage);

            $filename = sprintf(
                'Rapor_%s_Term-%d_%s.pdf',
                $stage,
                $term->id,
                str_replace(' ', '_', $data['nama'] ?? 'Siswa')
            );

            return response($dompdf->output(), 200, [
                'Content-Type'        => 'application/pdf',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'message' => 'Gagal membuat PDF.',
                'error'   => app()->isProduction() ? null : $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /* =========================
     * PDF cover report
     * ========================= */
    public function reportCoverDownload(Request $request, User $student)
    {
        $this->applyLongRunGuards();
        $this->applyDbStatementTimeout();

        $data = $this->service->buildCoverData(studentId: $student->id);
        $html = view('pdf.rapor_cover', $data)->render();

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('defaultFont', 'Times-Roman');

        $dompdf = new Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');

        // (Opsional) jika butuh relaxed SSL untuk logo self-signed
        $dompdf->setHttpContext(stream_context_create([
            'ssl' => [
                'verify_peer'       => false,
                'verify_peer_name'  => false,
                'allow_self_signed' => true,
            ]
        ]));

        $dompdf->render();

        $filename = sprintf(
            'Sampul_Rapor_%s.pdf',
            str_replace(' ', '_', $data['nama'] ?? $student->name ?? 'Siswa')
        );

        return response($dompdf->output(), 200, [
            'Content-Type'        => 'application/pdf',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }
}
