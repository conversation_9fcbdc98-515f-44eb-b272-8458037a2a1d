<?php

namespace App\Http\Controllers;

use App\Http\Requests\PracticalTraining\StorePracticalTrainingRequest;
use App\Http\Requests\PracticalTraining\UpdatePracticalTrainingRequest;
use App\Http\Resources\PracticalTrainingResource;
use App\Models\PracticalTraining;
use App\Services\PracticalTrainingService;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PracticalTrainingController extends Controller
{
    protected PracticalTrainingService $service;

    public function __construct(PracticalTrainingService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All PracticalTraining
     */
    public function index(Request $request)
    {
        try {
            $practicalTrainings = $this->service->paginate($request);
            return $this->successResponse(PracticalTrainingResource::collection($practicalTrainings));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve practicalTrainings', $e->getMessage(), 500);
        }
    }

    /**
     * Store PracticalTraining
     */
    public function store(StorePracticalTrainingRequest $request)
    {
        try {
            $practicalTraining = $this->service->store($request);
            return $this->successResponse(new PracticalTrainingResource($practicalTraining), 201, 'PracticalTraining created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create practicalTraining', $e->getMessage(), 500);
        }
    }

    /**
     * Get PracticalTraining Detail
     */
    public function show(PracticalTraining $practicalTraining)
    {
        try {
            return $this->successResponse(new PracticalTrainingResource($this->service->show($practicalTraining)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTraining not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve practicalTraining', $e->getMessage(), 500);
        }
    }

    /**
     * Update PracticalTraining
     */
    public function update(UpdatePracticalTrainingRequest $request, PracticalTraining $practicalTraining)
    {
        try {
            $practicalTraining = $this->service->update($practicalTraining, $request);
            return $this->successResponse(new PracticalTrainingResource($practicalTraining), 200, 'PracticalTraining updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTraining not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update practicalTraining', $e->getMessage(), 500);
        }
    }

    /**
     * Delete PracticalTraining
     */
    public function destroy(PracticalTraining $practicalTraining)
    {
        try {
            $this->service->delete($practicalTraining);
            return $this->successResponse('PracticalTraining deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTraining not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete practicalTraining', $e->getMessage(), 500);
        }
    }

    public function downloadReportPracticalTraining(PracticalTraining $practicalTraining)
    {
        try {
            $data = $this->service->prepareReportDataPracticalTraining($practicalTraining);

            $pdf = Pdf::loadView('exports.pkl-report', $data)->setPaper('A4');

            $filename = "laporan_pkl_{$data['nama']}.pdf";

            return $pdf->download($filename);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Data PKL tidak ditemukan', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Gagal membuat laporan PKL', $e->getMessage(), 500);
        }
    }
}
