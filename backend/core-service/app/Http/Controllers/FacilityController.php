<?php

namespace App\Http\Controllers;

use App\Http\Requests\Facility\StoreFacilityRequest;
use App\Http\Requests\Facility\UpdateFacilityRequest;
use App\Http\Resources\FacilityResource;
use App\Models\Facility;
use App\Services\FacilityService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class FacilityController extends Controller
{
    protected FacilityService $service;

    public function __construct(FacilityService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $facilitys = $this->service->paginate($request);
            return $this->successResponse(FacilityResource::collection($facilitys));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve facilitys', $e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFacilityRequest $request)
    {
        try {
            $facility = $this->service->store($request->validated());
            return $this->successResponse(new FacilityResource($facility), 201, 'Facility created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create facility', $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Facility $facility)
    {
        try {
            return $this->successResponse(new FacilityResource($this->service->show($facility)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Facility not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve facility', $e->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFacilityRequest $request, Facility $facility)
    {
        try {
            $facility = $this->service->update($facility, $request->validated());
            return $this->successResponse(new FacilityResource($facility), 200, 'Facility updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Facility not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update facility', $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Facility $facility)
    {
        try {
            $this->service->delete($facility);
            return $this->successResponse(null, 200, 'Facility deleted successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Facility not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete facility', $e->getMessage(), 500);
        }
    }
    
    /**
     * Import facilities from Excel file
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        try {
            $user = $request->user();
            $schoolId = null;
            
            // Get school ID from X-School-ID header
            if ($request->hasHeader('X-School-ID')) {
                $schoolId = $request->header('X-School-ID');
            }
            // Fallback to session
            elseif (session()->has('active_school_id')) {
                $schoolId = session('active_school_id');
            } 
            // Fallback to user's first school if they only have one
            elseif ($user && $user->is_superadmin && $user->schools()->count() === 1) {
                $schoolId = $user->schools()->first()->id;
            }
            
            if (!$schoolId) {
                return response()->json([
                    'status' => false,
                    'message' => 'Import gagal: School ID tidak ditemukan. Pastikan Anda memiliki akses ke sekolah.',
                ], 422);
            }
            
            return response()->json($this->service->import($request, $schoolId));
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Import gagal: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Export facilities to Excel file
     */
    public function export()
    {
        try {
            return $this->service->export();
        } catch (Exception $e) {
            return $this->errorResponse('Failed to export fasilitas', $e->getMessage(), 500);
        }
    }
    
    /**
     * Download import template for facilities
     */
    public function importTemplate()
    {
        try {
            return $this->service->downloadImportTemplate();
        } catch (Exception $e) {
            return $this->errorResponse('Failed to download template', $e->getMessage(), 500);
        }
    }
}
