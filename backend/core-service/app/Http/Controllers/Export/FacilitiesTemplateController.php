<?php

namespace App\Http\Controllers\Export;

use App\Http\Controllers\Controller;
use App\Imports\Facility\FacilitiesImportTemplate;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FacilitiesTemplateController extends Controller
{
    /**
     * Download template Excel untuk import fasilitas.
     *
     * @param Request $request
     * @return BinaryFileResponse
     */
    public function download(Request $request): BinaryFileResponse
    {
        // Get school ID from header or session, but don't enforce it for template download
        $schoolId = null;
        
        // Get school ID from X-School-ID header
        if ($request->hasHeader('X-School-ID')) {
            $schoolId = $request->header('X-School-ID');
            // Store in session for future use
            session(['active_school_id' => $schoolId]);
        }
        // Fallback to session
        elseif (session()->has('active_school_id')) {
            $schoolId = session('active_school_id');
        }
        
        // The template download doesn't actually need the school ID, but we retrieve 
        // it to maintain consistency with session management in other controllers
        return Excel::download(
            new FacilitiesImportTemplate,
            'template_import_fasilitas.xlsx'
        );
    }
}
