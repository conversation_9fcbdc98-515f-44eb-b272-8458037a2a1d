<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClassAttendance\StoreClassAttendanceRequest;
use App\Http\Requests\ClassAttendance\UpdateClassAttendanceRequest;
use App\Http\Resources\ClassAttendanceResource;
use App\Models\ClassAttendance;
use App\Services\ClassAttendanceService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ClassAttendanceController extends Controller
{
    protected ClassAttendanceService $service;

    public function __construct(ClassAttendanceService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All ClassAttendance (paginate)
     */
    public function index(Request $request)
    {
        try {
            // Jika dikasih term_id → kembalikan roster siswa (bukan paginate)
            if ($request->filled('term_id')) {
                $request->validate([
                    'term_id' => 'required|integer|exists:terms,id',
                    'sub_classroom_id' => 'nullable|integer',
                ]);
                $data = $this->service->listRoster(
                    (int)$request->input('term_id'),
                    $request->integer('sub_classroom_id')
                );
                return $this->successResponse($data);
            }

            // default lama: paginate
            $classAttendances = $this->service->paginate($request);
            return $this->successResponse(ClassAttendanceResource::collection($classAttendances));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve classAttendances', $e->getMessage(), 500);
        }
    }


    public function store(StoreClassAttendanceRequest $request)
    {
        try {
            $classAttendance = $this->service->store($request);
            return $this->successResponse(new ClassAttendanceResource($classAttendance), 201, 'ClassAttendance created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create classAttendance', $e->getMessage(), 500);
        }
    }

    public function show(ClassAttendance $classAttendance)
    {
        try {
            return $this->successResponse(new ClassAttendanceResource($this->service->show($classAttendance)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ClassAttendance not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve classAttendance', $e->getMessage(), 500);
        }
    }

    public function update(UpdateClassAttendanceRequest $request, ClassAttendance $classAttendance)
    {
        try {
            $classAttendance = $this->service->update($classAttendance, $request);
            return $this->successResponse(new ClassAttendanceResource($classAttendance), 200, 'ClassAttendance updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ClassAttendance not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update classAttendance', $e->getMessage(), 500);
        }
    }

    public function destroy(ClassAttendance $classAttendance)
    {
        try {
            $this->service->delete($classAttendance);
            return $this->successResponse('ClassAttendance deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ClassAttendance not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete classAttendance', $e->getMessage(), 500);
        }
    }

    /* ======================== CUSTOM ENDPOINTS ======================== */

    // POST /api/classAttendance/upsert
    public function upsert(Request $request)
    {
        try {
            $validated = $request->validate([
                'term_id' => 'required|integer|exists:terms,id',
                'student_user_id' => 'required|integer|exists:users,id',
                'sick' => 'required|integer|min:0',
                'leave' => 'required|integer|min:0',
                'present' => 'required|integer|min:0',
                'alpha' => 'required|integer|min:0',
            ]);
            $actorId = optional($request->user())->id;

            $row = $this->service->upsertOne(
                (int)$validated['term_id'],
                (int)$validated['student_user_id'],
                (int)$validated['sick'],
                (int)$validated['leave'],
                (int)$validated['present'],
                (int)$validated['alpha'],

                $actorId
            );

            return $this->successResponse(new ClassAttendanceResource($row), 200, 'Upsert success');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to upsert', $e->getMessage(), 500);
        }
    }

    // POST /api/classAttendance/bulk-append
    public function bulkAppend(Request $request)
    {
        try {
            $validated = $request->validate([
                'items' => 'required|array|min:1',
                'items.*.term_id' => 'required|integer|exists:terms,id',
                'items.*.student_user_id' => 'required|integer|exists:users,id',
                'items.*.sick' => 'nullable|integer|min:0',
                'items.*.leave' => 'nullable|integer|min:0',
                'items.*.present' => 'nullable|integer|min:0',
                'items.*.alpha' => 'nullable|integer|min:0',

            ]);

            $actorId = optional($request->user())->id;
            $this->service->bulkAppend($validated['items'], $actorId);

            return $this->successResponse('Bulk append success', 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to bulk append', $e->getMessage(), 500);
        }
    }

    // PATCH /api/classAttendance/{id}/adjust
    public function adjust(int $id, Request $request)
    {
        try {
            $validated = $request->validate([
                'field' => 'required|string|in:sick,leave,present,alpha',
                'delta' => 'required|integer',
            ]);

            $actorId = optional($request->user())->id;
            $row = $this->service->adjust($id, $validated['field'], (int)$validated['delta'], $actorId);

            return $this->successResponse(new ClassAttendanceResource($row), 200, 'Adjusted');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ClassAttendance not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to adjust', $e->getMessage(), 500);
        }
    }

    // GET /api/classAttendance/template?term_id=...&sub_classroom_id=?
    public function downloadTemplate(Request $request)
    {
        $validated = $request->validate([
            'term_id' => 'required|integer|exists:terms,id',
            'sub_classroom_id' => 'nullable|integer|min:1',
        ]);

        return $this->service->makeTemplateExcel(
            (int) $validated['term_id'],
            $request->integer('sub_classroom_id')
        );
    }

    // POST /api/classAttendance/import
    public function import(Request $request)
    {
        try {
            $validated = $request->validate([
                'file' => 'required|file|mimes:xlsx',
            ]);
            $actorId = optional($request->user())->id;

            $this->service->importFromFile($validated['file'], $actorId);

            return $this->successResponse('Imported', 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to import', $e->getMessage(), 500);
        }
    }
}
