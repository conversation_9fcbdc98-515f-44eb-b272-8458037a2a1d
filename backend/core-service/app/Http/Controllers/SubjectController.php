<?php

namespace App\Http\Controllers;

use App\Http\Requests\Subject\StoreSubjectRequest;
use App\Http\Requests\Subject\UpdateSubjectRequest;
use App\Http\Resources\SubClassroomSubjectResource;
use App\Http\Resources\SubjectResource;
use App\Models\Subject;
use App\Services\SubjectService;
use Exception;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    protected SubjectService $service;

    public function __construct(SubjectService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            return $this->successResponse(
                SubjectResource::collection($this->service->paginate($request))
            );
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve subjects', $e->getMessage(), 500);
        }
    }

    public function store(StoreSubjectRequest $request)
    {
        try {
            $subject = $this->service->store($request->validated());
            return $this->successResponse(new SubjectResource($subject), 201, 'Subject created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create subject', $e->getMessage(), 500);
        }
    }

    public function show(Subject $subject)
    {
        try {
            return $this->successResponse(new SubjectResource($this->service->show($subject)));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve subject', $e->getMessage(), 500);
        }
    }

    public function update(UpdateSubjectRequest $request, Subject $subject)
    {
        try {
            $subject = $this->service->update($subject, $request->validated());
            return $this->successResponse(new SubjectResource($subject), 200, 'Subject updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update subject', $e->getMessage(), 500);
        }
    }

    public function destroy(Subject $subject)
    {
        try {
            $this->service->delete($subject);
            return $this->successResponse('Subject deleted successfully', 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete subject', $e->getMessage(), 500);
        }
    }

    public function getAssignedSubjects()
    {
        try {
            $subjects = $this->service->getAssignedSubjects(
                $this->user->detail()->id,
                request()
            );
            return $this->successResponse(SubClassroomSubjectResource::collection($subjects));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve subject', $e->getMessage(), 500);
        }
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        try {
            $user = $request->user();
            $schoolId = null;
            
            // Get school ID from X-School-ID header
            if ($request->hasHeader('X-School-ID')) {
                $schoolId = $request->header('X-School-ID');
            }
            // Fallback to session
            elseif (session()->has('active_school_id')) {
                $schoolId = session('active_school_id');
            } 
            // Fallback to user's first school if they only have one
            elseif ($user && !$user->is_superadmin && $user->schools()->count() === 1) {
                $schoolId = $user->schools()->first()->id;
            }
            
            if (!$schoolId) {
                return response()->json([
                    'status' => false,
                    'message' => 'Import gagal: School ID tidak ditemukan. Pastikan Anda memiliki akses ke sekolah.',
                ], 422);
            }
            
            return response()->json($this->service->import($request, $schoolId));
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Import gagal: ' . $e->getMessage(),
            ], 422);
        }
    }

    public function export()
    {
        try {
            return $this->service->export();
        } catch (Exception $e) {
            return $this->errorResponse('Failed to export mata pelajaran', $e->getMessage(), 500);
        }
    }
}
