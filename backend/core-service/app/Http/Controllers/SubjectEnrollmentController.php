<?php

namespace App\Http\Controllers;

use App\Http\Requests\Enrollment\BulkUpsertEnrollmentRequest;
use App\Http\Requests\SubjectEnrollment\MatrixQueryRequest;
use App\Http\Requests\SubjectEnrollment\UpdateSingleEnrollmentRequest;
use App\Services\SubjectEnrollmentService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class SubjectEnrollmentController extends Controller
{
    protected SubjectEnrollmentService $service;

    public function __construct(SubjectEnrollmentService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * GET /subjectEnrollment/enrollments/matrix?term_id=&sub_classroom_id=
     */
    public function matrix(MatrixQueryRequest $request): JsonResponse
    {
        try {
            $v = $request->validated();

            $termRaw = is_array($v['term_id']) ? Arr::first($v['term_id']) : $v['term_id'];
            $subRaw  = is_array($v['sub_classroom_id']) ? Arr::first($v['sub_classroom_id']) : $v['sub_classroom_id'];

            $termId = (int) $termRaw;
            $subClassroomId = (int) $subRaw;

            $result = $this->service->getMatrix($termId, $subClassroomId);

            $hasStudents = !empty($result['students']) && count($result['students']) > 0;
            $hasSubjects = !empty($result['subjects']) && count($result['subjects']) > 0;

            if (!$hasStudents || !$hasSubjects) {
                return $this->successResponse([
                    'term_id'          => $termId,
                    'sub_classroom_id' => $subClassroomId,
                    'students'         => [],
                    'subjects'         => [],
                    'matrix'           => [],
                ], 200, 'Matrix empty');
            }

            $hasAnyEnrollment = collect($result['matrix'] ?? [])
                ->flatMap(fn($row) => $row['subjects'] ?? [])
                ->contains(fn($cell) => (bool)($cell['is_enrolled'] ?? false) === true);

            if (!$hasAnyEnrollment) {
                return $this->successResponse([
                    'term_id'          => $termId,
                    'sub_classroom_id' => $subClassroomId,
                    'students'         => [],
                    'subjects'         => [],
                    'matrix'           => [],
                ], 200, 'Matrix empty (no enrollment)');
            }

            // Normalisasi output (pastikan array of {id,name})
            $students = collect($result['students'])->map(function ($u) {
                if (is_array($u)) {
                    return ['id' => $u['id'] ?? null, 'name' => $u['name'] ?? null];
                }
                return ['id' => $u->id ?? null, 'name' => $u->name ?? null];
            })->values();

            $subjects = collect($result['subjects'])->map(function ($s) {
                if (is_array($s)) {
                    $name = $s['name'] ?? ($s['subject']['name'] ?? null);
                    return ['id' => $s['id'] ?? null, 'name' => $name];
                }
                $name = $s->name ?? ($s->subject->name ?? null);
                return ['id' => $s->id ?? null, 'name' => $name];
            })->values();

            return $this->successResponse([
                'term_id'          => $termId,
                'sub_classroom_id' => $subClassroomId,
                'students'         => $students,
                'subjects'         => $subjects,
                'matrix'           => $result['matrix'] ?? [],
            ], 200, 'Matrix retrieved successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class or term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve matrix', $e->getMessage(), 500);
        }
    }


    /**
     * PATCH /subjectEnrollment/enrollments  (toggle satu cell)
     */
    public function updateSingle(UpdateSingleEnrollmentRequest $request)
    {
        try {
            $this->service->toggleSingle($request->validated());
            return $this->successResponse(null, 200, 'Cell updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Enrollment target not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update cell', $e->getMessage(), 500);
        }
    }

    /**
     * PUT /subjectEnrollment/enrollments/bulk (kirim banyak cell sekaligus)
     */
    public function bulkUpsert(BulkUpsertEnrollmentRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $updated   = $this->service->bulkUpsert((int) $validated['term_id'], $validated['rows']);

            return $this->successResponse([
                'updated' => $updated,
            ], 200, 'Bulk enrollments upserted successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class or term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to bulk upsert enrollments', $e->getMessage(), 500);
        }
    }
}
