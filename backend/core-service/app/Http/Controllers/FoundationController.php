<?php

namespace App\Http\Controllers;

use App\Http\Requests\Foundation\AddFoundationSchoolRequest;
use App\Http\Requests\Foundation\AssignFoundationAdminRequest;
use App\Http\Requests\Foundation\StoreFoundationRequest;
use App\Http\Requests\Foundation\UpdateFoundationRequest;
use App\Http\Resources\FoundationResource;
use App\Http\Resources\SchoolResource;
use App\Http\Resources\UserResource;
use App\Models\Foundation;
use App\Services\FoundationService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class FoundationController extends Controller
{
    protected FoundationService $service;

    public function __construct(FoundationService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $foundations = $this->service->paginate($request);
            return $this->successResponse(FoundationResource::collection($foundations));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve foundations', $e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFoundationRequest $request)
    {
        try {
            $foundation = $this->service->store($request);
            return $this->successResponse(new FoundationResource($foundation), 201, 'Foundation created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create foundation', $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Foundation $foundation)
    {
        try {
            return $this->successResponse(new FoundationResource($this->service->show($foundation)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve foundation', $e->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFoundationRequest $request, Foundation $foundation)
    {
        try {
            $foundation = $this->service->update($foundation, $request);
            return $this->successResponse(new FoundationResource($foundation), 200, 'Foundation updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update foundation', $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Foundation $foundation)
    {
        try {
            $this->service->delete($foundation);
            return $this->successResponse('Foundation deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete foundation', $e->getMessage(), 500);
        }
    }

    public function getAdmins(Foundation $foundation)
    {
        try {
            $admins = UserResource::collection($this->service->getAdmins($foundation));
            return $this->successResponse($admins, 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve foundation admins', $e->getMessage(), 500);
        }
    }

    public function assignAdmin(AssignFoundationAdminRequest $request, Foundation $foundation)
    {
        try {
            $admin = $this->service->assignAdmin($request, $foundation);
            return $this->successResponse(new UserResource($admin), 200, 'Admin assigned successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign admin', $e->getMessage(), 500);
        }
    }

    public function getSchools(Foundation $foundation)
    {
        try {
            $schools = SchoolResource::collection($this->service->getSchools($foundation));
            return $this->successResponse($schools, 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve foundation schools', $e->getMessage(), 500);
        }
    }

    public function addSchool(AddFoundationSchoolRequest $request, Foundation $foundation)
    {
        try {
            $school = $this->service->addSchool($request, $foundation);
            return $this->successResponse(new SchoolResource($school), 200, 'School added successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Foundation not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to add school', $e->getMessage(), 500);
        }
    }

    public function schoolCount(Foundation $foundation)
    {
        try {
            $count = $foundation->schoolCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count schools', $e->getMessage(), 500);
        }
    }

    public function studentCount(Foundation $foundation)
    {
        try {
            $count = $foundation->studentCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count students', $e->getMessage(), 500);
        }
    }

    public function teacherCount(Foundation $foundation)
    {
        try {
            $count = $foundation->teacherCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count teachers', $e->getMessage(), 500);
        }
    }

    public function subClassroomCount(Foundation $foundation)
    {
        try {
            $count = $foundation->subClassroomCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count sub-classrooms', $e->getMessage(), 500);
        }
    }
}
