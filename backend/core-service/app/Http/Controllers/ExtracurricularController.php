<?php

namespace App\Http\Controllers;

use App\Http\Requests\Extracurricular\StoreExtracurricularRequest;
use App\Http\Requests\Extracurricular\UpdateExtracurricularRequest;
use App\Http\Requests\Extracurricular\CheckInExtracurricularRequest;
use App\Http\Requests\ExtracurricularActivity\ApproveExtracurricularActivityRequest;
use App\Http\Requests\ExtracurricularActivity\StoreExtracurricularActivityRequest;
use App\Http\Resources\ExtracurricularActivityResource;
use App\Http\Resources\ExtracurricularAttendanceResource;
use App\Http\Resources\ExtracurricularResource;
use App\Http\Resources\UserResource;
use App\Models\Extracurricular;
use App\Models\ExtracurricularActivity;
use App\Models\User;
use App\Services\ExtracurricularService;
use Exception;
use Illuminate\Http\Request;

class ExtracurricularController extends Controller
{
    protected ExtracurricularService $service;

    public function __construct(ExtracurricularService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            $result = ExtracurricularResource::collection($this->service->paginate($request));
            return $this->successResponse($result, 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve extracurriculars', $e->getMessage(), 500);
        }
    }

    public function store(StoreExtracurricularRequest $request)
    {
        try {
            $result = new ExtracurricularResource($this->service->store($request));
            return $this->successResponse($result, 200, 'Extracurricular created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create extracurricular', $e->getMessage(), 500);
        }
    }

    public function show(Extracurricular $extracurricular)
    {
        try {
            $result = new ExtracurricularResource($extracurricular);
            return $this->successResponse($result, 200, 'Extracurricular retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve extracurricular', $e->getMessage(), 500);
        }
    }

    public function update(UpdateExtracurricularRequest $request, Extracurricular $extracurricular)
    {
        try {
            $result = new ExtracurricularResource($this->service->update($extracurricular, $request));
            return $this->successResponse($result, 200, 'Extracurricular updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update extracurricular', $e->getMessage(), 500);
        }
    }

    public function destroy(Extracurricular $extracurricular)
    {
        try {
            $this->service->delete($extracurricular);
            return $this->successResponse('Extracurricular deleted successfully', 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete extracurricular', $e->getMessage(), 500);
        }
    }

    public function getStudents(Extracurricular $extracurricular)
    {
        try {
            $result = UserResource::collection($this->service->getStudents($extracurricular));
            return $this->successResponse($result, 200, 'Extracurricular students retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve extracurricular students', $e->getMessage(), 500);
        }
    }

    public function getAssigned(Request $request)
    {
        try {
            $result = ExtracurricularResource::collection($this->service->getAssigned($request));
            return $this->successResponse($result, 200, 'Assigned extracurriculars retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve assigned extracurriculars', $e->getMessage(), 500);
        }
    }

    public function getAttendances(Extracurricular $extracurricular)
    {
        try {
            $result = ExtracurricularAttendanceResource::collection($this->service->getAttendances($extracurricular->id));
            return $this->successResponse($result, 200, 'Attendances retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve attendances', $e->getMessage(), 500);
        }
    }

    public function assign(Extracurricular $extracurricular)
    {
        try {
            if (!$this->user->hasRole('student')) {
                return $this->errorResponse('Only students can be assigned', null, 403);
            }
            $result = new ExtracurricularResource($this->service->assignStudent($extracurricular, $this->user->detail()->id));
            return $this->successResponse($result, 200, 'Student assigned successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign student', $e->getMessage(), 500);
        }
    }

    public function unassign(Extracurricular $extracurricular)
    {
        try {
            if (!$this->user->hasRole('student')) {
                return $this->errorResponse('Only students can be unassigned', null, 403);
            }
            $result = new ExtracurricularResource($this->service->unassignStudent($extracurricular, $this->user->detail()->id));
            return $this->successResponse($result, 200, 'Student unassigned successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to unassign student', $e->getMessage(), 500);
        }
    }

    public function assignStudents(Request $request, Extracurricular $extracurricular)
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        try {
            $result = ExtracurricularResource::collection($this->service->assignStudents($extracurricular, $validated['user_ids']));
            return $this->successResponse($result, 200, 'Students assigned successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign students', $e->getMessage(), 500);
        }
    }

    public function unassignStudents(Request $request, Extracurricular $extracurricular)
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        try {
            $result = ExtracurricularResource::collection($this->service->unassignStudents($extracurricular, $validated['user_ids']));
            return $this->successResponse($result, 200, 'Students unassigned successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to unassign students', $e->getMessage(), 500);
        }
    }

    public function checkIn(CheckInExtracurricularRequest $request)
    {
        try {
            $result = new ExtracurricularAttendanceResource($this->service->checkIn($request, $this->user));
            return $this->successResponse($result, 200, 'Check-in successful');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to check in', $e->getMessage(), 500);
        }
    }

    public function checkInHistory(Request $request)
    {
        try {
            $user = User::find(request('user_id')) ?? $this->user;
            $result = ExtracurricularAttendanceResource::collection($this->service->getCheckInHistory($request, $user));
            return $this->successResponse($result, 200, 'Check-in history retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve check-in history', $e->getMessage(), 500);
        }
    }

    public function submitActivity(StoreExtracurricularActivityRequest $request)
    {
        try {
            $result = new ExtracurricularActivityResource($this->service->submitActivity($request, $this->user));
            return $this->successResponse($result, 200, 'Extracurricular activity submitted successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to submit extracurricular activity', $e->getMessage(), 500);
        }
    }

    public function approveActivity(ApproveExtracurricularActivityRequest $request, ExtracurricularActivity $activity)
    {
        try {
            $result = new ExtracurricularActivityResource($this->service->approveActivity($request, $activity));
            return $this->successResponse($result, 200, 'Extracurricular activity approved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to approve extracurricular activity', $e->getMessage(), 500);
        }
    }

    public function getAllActivity(Request $request)
    {
        try {
            $activities = ExtracurricularActivityResource::collection($this->service->getAllActivity($request));
            return $this->successResponse($activities, 200, 'Extracurricular activity retrieved successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve extracurriculars Activity', $e->getMessage(), 500);
        }
    }
}
