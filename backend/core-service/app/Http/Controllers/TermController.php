<?php

namespace App\Http\Controllers;

use App\Http\Requests\Term\StoreTermRequest;
use App\Http\Requests\Term\UpdateTermRequest;
use App\Http\Resources\TermResource;
use App\Models\Term;
use App\Services\TermService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class TermController extends Controller
{
    protected TermService $service;

    public function __construct(TermService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All Term
     */
    public function index(Request $request)
    {
        try {
            $terms = $this->service->paginate($request);
            return $this->successResponse(TermResource::collection($terms));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve terms', $e->getMessage(), 500);
        }
    }

    /**
     * Store Term
     */
    public function store(StoreTermRequest $request)
    {
        try {
            $term = $this->service->store($request);
            return $this->successResponse(new TermResource($term), 201, 'Term created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create term', $e->getMessage(), 500);
        }
    }

    /**
     * Get Term Detail
     */
    public function show(Term $term)
    {
        try {
            return $this->successResponse(new TermResource($this->service->show($term)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve term', $e->getMessage(), 500);
        }
    }

    /**
     * Update Term
     */
    public function update(UpdateTermRequest $request, Term $term)
    {
        try {
            $term = $this->service->update($term, $request);
            return $this->successResponse(new TermResource($term), 200, 'Term updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update term', $e->getMessage(), 500);
        }
    }

    /**
     * Delete Term
     */
    public function destroy(Term $term)
    {
        try {
            $this->service->delete($term);
            return $this->successResponse('Term deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete term', $e->getMessage(), 500);
        }
    }
}
