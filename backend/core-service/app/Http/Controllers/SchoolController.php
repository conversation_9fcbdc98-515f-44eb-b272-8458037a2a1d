<?php

namespace App\Http\Controllers;

use App\Http\Requests\School\AssignSchoolAdminRequest;
use App\Http\Requests\School\StoreSchoolRequest;
use App\Http\Requests\School\UpdateSchoolRequest;
use App\Http\Resources\SchoolResource;
use App\Http\Resources\UserResource;
use App\Models\School;
use App\Services\SchoolService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SchoolController extends Controller
{
    protected SchoolService $service;

    public function __construct(SchoolService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $schools = $this->service->paginate($request);
            return $this->successResponse(SchoolResource::collection($schools));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve schools', $e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolRequest $request)
    {
        try {
            $school = $this->service->store($request);
            return $this->successResponse(new SchoolResource($school), 201, 'School created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create school', $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(School $school)
    {
        try {
            return $this->successResponse(new SchoolResource($this->service->show($school)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve school', $e->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolRequest $request, School $school)
    {
        try {
            $school = $this->service->update($school, $request);
            return $this->successResponse(new SchoolResource($school), 200, 'School updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update school', $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(School $school)
    {
        try {
            $this->service->delete($school);
            return $this->successResponse('School deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete school', $e->getMessage(), 500);
        }
    }

    public function getAdmins(School $school)
    {
        try {
            $admins = $this->service->getAdmins($school);
            return $this->successResponse($admins, 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete school', $e->getMessage(), 500);
        }
    }

    public function assignAdmin(AssignSchoolAdminRequest $request, School $school)
    {
        try {
            $result = $this->service->assignAdmin($request, $school);
            return $this->successResponse($result, 200, 'Admin assigned successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete school', $e->getMessage(), 500);
        }
    }

    public function updateByAdmin(UpdateSchoolRequest $request, School $school)
    {
        try {
            $school = $this->service->updateByAdmin($school, $request);
            return $this->successResponse(new SchoolResource($school), 200, 'School updated by admin successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('School not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update school by admin', $e->getMessage(), 500);
        }
    }
}
