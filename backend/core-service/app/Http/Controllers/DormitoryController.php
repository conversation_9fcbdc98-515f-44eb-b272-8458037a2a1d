<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dormitory\StoreDormitoryRequest;
use App\Http\Requests\Dormitory\UpdateDormitoryRequest;
use App\Http\Resources\DormitoryResource;
use App\Models\Dormitory;
use App\Services\DormitoryService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DormitoryController extends Controller
{
    protected DormitoryService $service;

    public function __construct(DormitoryService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All Dormitory
     */
    public function index(Request $request)
    {
        try {
            $dormitorys = $this->service->paginate($request);
            return $this->successResponse(DormitoryResource::collection($dormitorys));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve dormitorys', $e->getMessage(), 500);
        }
    }

    /**
     * Store Dormitory
     */
    public function store(StoreDormitoryRequest $request)
    {
        try {
            $dormitory = $this->service->store($request);
            return $this->successResponse(new DormitoryResource($dormitory), 201, 'Dormitory created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create dormitory', $e->getMessage(), 500);
        }
    }

    /**
     * Get Dormitory Detail
     */
    public function show(Dormitory $dormitory)
    {
        try {
            return $this->successResponse(new DormitoryResource($this->service->show($dormitory)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Dormitory not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve dormitory', $e->getMessage(), 500);
        }
    }

    /**
     * Update Dormitory
     */
    public function update(UpdateDormitoryRequest $request, Dormitory $dormitory)
    {
        try {
            $dormitory = $this->service->update($dormitory, $request);
            return $this->successResponse(new DormitoryResource($dormitory), 200, 'Dormitory updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Dormitory not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update dormitory', $e->getMessage(), 500);
        }
    }

    /**
     * Delete Dormitory
     */
    public function destroy(Dormitory $dormitory)
    {
        try {
            $this->service->delete($dormitory);
            return $this->successResponse('Dormitory deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Dormitory not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete dormitory', $e->getMessage(), 500);
        }
    }
}
