<?php

namespace App\Http\Controllers;

use App\Http\Requests\Schedule\GetAttendanceStatusRequest;
use App\Http\Requests\Schedule\StoreScheduleRequest;
use App\Http\Requests\Schedule\UpdateScheduleRequest;
use App\Http\Resources\AttendanceResource;
use App\Http\Resources\ScheduleResource;
use App\Models\Schedule;
use App\Services\ScheduleService;
use Exception;

class ScheduleController extends Controller
{
    protected ScheduleService $service;

    public function __construct(ScheduleService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index()
    {
        $day = request('day');
        $subClassroomId = $this->user->subClassroom->id ?? request('sub_classroom_id');
        $schoolId = request('school_id'); // Accept school_id from request

        $schedules = $this->service->getSchedules($this->user, $day, $subClassroomId, $schoolId);

        return $this->successResponse($schedules, 200, 'Schedules retrieved successfully');
    }

    public function store(StoreScheduleRequest $request)
    {
        try {
            $validated = $request->validated();
            $schedule = $this->service->storeSchedule($validated);
            return $this->successResponse($schedule, 201, 'Schedule created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create schedule', $e->getMessage(), 500);
        }
    }

    public function update(UpdateScheduleRequest $request, Schedule $schedule)
    {
        try {
            $validated = $request->validated();
            $this->service->updateSchedule($schedule, $validated);
            return $this->successResponse($schedule, 200, 'Schedule updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update schedule', $e->getMessage(), 500);
        }
    }

    public function destroy(Schedule $schedule)
    {
        try {
            $this->service->destroySchedule($schedule);
            return $this->successResponse(true, 204, 'Schedule deleted successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete schedule', $e->getMessage(), 500);
        }
    }

    public function getCurrentSchedule()
    {
        $schedule = $this->service->getCurrentSchedule($this->user, now()->format('N'));
        return $schedule
            ? $this->successResponse(new ScheduleResource($schedule), 200, 'Current schedule retrieved')
            : $this->errorResponse('No current schedule found', [], 404);
    }

    public function getStudentAttendances(Schedule $schedule)
    {
        $attendances = $this->service->getAttendances($schedule->id, request('date'));
        return $this->successResponse($attendances, 200, 'Attendances retrieved');
    }

    public function getAttendanceStatus(GetAttendanceStatusRequest $request, Schedule $schedule)
    {
        try {
            $attendance = new AttendanceResource($this->service->getAttendanceStatus($request, $schedule));
            return $this->successResponse($attendance, 200, 'Attendance status retrieved');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve attendance status', $e->getMessage(), 500);
        }
    }
}
