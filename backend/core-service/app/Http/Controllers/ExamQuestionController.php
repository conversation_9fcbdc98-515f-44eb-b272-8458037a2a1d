<?php

namespace App\Http\Controllers;

use App\Exports\Exam\ExamQuestionsImportTemplate;
use App\Http\Requests\ExamQuestion\CopyExamQuestionsRequest;
use App\Http\Requests\ExamQuestion\ImportExamQuestionsRequest;
use App\Http\Requests\ExamQuestion\StoreExamQuestionRequest;
use App\Http\Requests\ExamQuestion\StoreMultipleExamQuestionsRequest;
use App\Http\Requests\ExamQuestion\UpdateExamQuestionRequest;
use App\Http\Resources\ExamQuestionResource;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Services\ExamQuestionService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class ExamQuestionController extends Controller
{
    protected ExamQuestionService $service;

    public function __construct(ExamQuestionService $service)
    {
        parent::__construct();
        $this->service = $service;
        $this->middleware('role:teacher,superadmin,foundation_admin,school_admin')->only(['index', 'store', 'storeMultiple', 'update', 'destroy', 'show', 'dwonloadTemplateQuestion', 'importQuestions']);
    }

    /**
     * Get All ExamQuestion
     */
    public function index(Request $request, Exam $exam)
    {
        try {
            $examQuestions = $this->service->paginate($request, $exam);
            return $this->successResponse(ExamQuestionResource::collection($examQuestions));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve examQuestions', $e->getMessage(), 500);
        }
    }

    /**
     * Store ExamQuestion
     */
    public function store(StoreExamQuestionRequest $request, Exam $exam)
    {
        try {
            $examQuestion = $this->service->store($request, $exam);
            return $this->successResponse(new ExamQuestionResource($examQuestion), 201, 'ExamQuestion created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create examQuestion', $e->getMessage(), 500);
        }
    }

    public function storeMultiple(StoreMultipleExamQuestionsRequest $request, Exam $exam)
    {
        try {
            $this->service->storeMultiple($request, $exam);
            return $this->successResponse('MultipleExamQuestions created successfully', 201);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create examQuestion', $e->getMessage(), 500);
        }
    }

    /**
     * Get ExamQuestion Detail
     */
    public function show(Exam $exam, ExamQuestion $examQuestion)
    {
        try {
            return $this->successResponse(new ExamQuestionResource($this->service->show($examQuestion, $exam)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamQuestion not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve examQuestion', $e->getMessage(), 500);
        }
    }

    /**
     * Update ExamQuestion
     */
    public function update(UpdateExamQuestionRequest $request, Exam $exam, ExamQuestion $examQuestion)
    {
        try {
            $examQuestion = $this->service->update($exam, $examQuestion, $request);
            return $this->successResponse(new ExamQuestionResource($examQuestion), 200, 'ExamQuestion updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamQuestion not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update examQuestion', $e->getMessage(), 500);
        }
    }

    /**
     * Delete ExamQuestion
     */
    public function destroy(Exam $exam, ExamQuestion $examQuestion)
    {
        try {
            $this->service->delete($exam, $examQuestion);
            return $this->successResponse('ExamQuestion deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamQuestion not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete examQuestion', $e->getMessage(), 500);
        }
    }

    public function downloadTemplateQuestion(Exam $exam)
    {
        try {
            $filename = 'template-import-exam-questions.xlsx';
            return Excel::download(new ExamQuestionsImportTemplate(), $filename);
        } catch (Exception $e) {
            Log::error('Failed to download template question: ' . $e->getMessage());
            return $this->errorResponse('Failed to download template question', $e->getMessage(), 500);
        }
    }

    public function importQuestions(ImportExamQuestionsRequest $request, Exam $exam)
    {
        try {
            [$summary, $errors] = $this->service->import(
                $exam,
                $request->file('file'),
                strtolower($request->input('mode', 'append'))
            );

            return response()->json([
                'message' => empty($errors) ? 'Import berhasil.' : 'Import selesai dengan beberapa kesalahan.',
                'summary' => $summary,
                'errors'  => $errors,
            ], empty($errors) ? Response::HTTP_OK : Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (Exception $e) {
            Log::error('Failed to import exam questions: ' . $e->getMessage());
            return $this->errorResponse('Failed to import exam questions', $e->getMessage(), 500);
        }
    }

    // GET /exam-questions/copy/sources?academic_year_id=&term_id=&q=
    public function listSourceExams(Request $request)
    {
        try {
            $data = $this->service->listSourceExams(
                termId: $request->integer('term_id') ?: null,
                subClassroomSubjectId: $request->integer('sub_classroom_subject_id') ?: null,
                q: trim((string) $request->get('q', '')) ?: null
            );
            return $this->successResponse($data);
        } catch (Exception $e) {
            Log::error('Failed to get list source exams: ' . $e->getMessage());
            return $this->errorResponse('Failed to get list source exams', $e->getMessage(), 500);
        }
    }

    // GET /exam-questions/copy/sources/{sourceExam}/questions
    public function listQuestionsOfSource(Exam $sourceExam)
    {
        try {
            $rows = $this->service->listQuestionsOfSource($sourceExam);
            return $this->successResponse(ExamQuestionResource::collection($rows));
        } catch (Exception $e) {
            Log::error('Failed to get list questions of source: ' . $e->getMessage());
            return $this->errorResponse('Failed to get list questions of source', $e->getMessage(), 500);
        }
    }

    // POST /exams/{exam}/questions/copy-from
    public function copyFromExam(CopyExamQuestionsRequest $request, Exam $exam)
    {
        try {
            $result = $this->service->copyFromExam(
                targetExam: $exam,
                sourceExamId: (int) $request->validated('source_exam_id'),
                questionIds: $request->validated('question_ids'),
                mode: $request->validated('insert_mode', 'append'),
                deepCopyMedia: (bool) $request->validated('deep_copy_media', false)
            );
            return $this->successResponse($result, 201, 'The question has been successfully copied.');
        } catch (Exception $e) {
            Log::error('Failed to The question has been successfully copied.: ' . $e->getMessage());
            return $this->errorResponse('Failed to The question has been successfully copied.', $e->getMessage(), 500);
        }
    }
}
