<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubClassroom\StoreSubClassroomRequest;
use App\Http\Requests\SubClassroom\UpdateSubClassroomRequest;
use App\Http\Resources\AssignmentResource;
use App\Http\Resources\SubClassroomResource;
use App\Http\Resources\SubClassroomSubjectResource;
use App\Http\Resources\SubjectResource;
use App\Http\Resources\UserResource;
use App\Models\School;
use App\Models\SubClassroom;
use App\Models\Term;
use App\Services\SubClassroomService;
use Exception;
use Illuminate\Http\Request;

class SubClassroomController extends Controller
{
    protected SubClassroomService $service;

    public function __construct(SubClassroomService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public static function assignSubject($subClassroomId, $subjectIds = [])
    {
        app(SubClassroomService::class)->assignSubject($subClassroomId, $subjectIds);
    }

    public function index(Request $request)
    {
        try {
            return $this->successResponse(
                SubClassroomResource::collection($this->service->paginate($request)),
                200
            );
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve sub-classrooms', $e->getMessage(), 500);
        }
    }

    public function store(StoreSubClassroomRequest $request)
    {
        try {
            $result = $this->service->store($request->validated());
            return $this->successResponse(new SubClassroomResource($result), 201, 'Sub-classroom created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create sub-classroom', $e->getMessage(), 500);
        }
    }

    public function show(SubClassroom $subClassroom)
    {
        try {
            return $this->successResponse(new SubClassroomResource($this->service->show($subClassroom)));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve sub-classroom', $e->getMessage(), 500);
        }
    }

    public function update(UpdateSubClassroomRequest $request, SubClassroom $subClassroom)
    {
        try {
            $result = $this->service->update($subClassroom, $request->validated());
            return $this->successResponse(new SubClassroomResource($result), 200, 'Sub-classroom updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update sub-classroom', $e->getMessage(), 500);
        }
    }

    public function destroy(SubClassroom $subClassroom)
    {
        try {
            $this->service->delete($subClassroom);
            return $this->successResponse('Sub-classroom deleted successfully', 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete sub-classroom', $e->getMessage(), 500);
        }
    }

    public function assignStudents(Request $request, SubClassroom $subClassroom)
    {
        $request->validate([
            'student_user_ids' => 'array',
            'student_user_ids.*' => 'exists:users,id',
        ]);

        $result = $this->service->assignStudents($subClassroom, $request->student_user_ids);

        if (isset($result['conflict'])) {
            return $this->errorResponse('Some students are already assigned to another sub-classroom.', [
                'conflicting_student_user_ids' => $result['conflict']
            ], 422);
        }

        return $this->successResponse('Students assigned successfully to the sub-classroom.');
    }

    public function getStudents(SubClassroom $subClassroom)
    {
        try {
            $students = $this->service->getStudents($subClassroom, request());
            return $this->successResponse(UserResource::collection($students));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve students', $e->getMessage(), 500);
        }
    }

    public function getSubjects(Request $request, SubClassroom $subClassroom)
    {
        try {
            $subjects = $this->service->getSubjects($subClassroom, $request);
            return $this->successResponse(SubClassroomSubjectResource::collection($subjects));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve subjects', $e->getMessage(), 500);
        }
    }

    public function assignSubjectTeacher(Request $request, SubClassroom $subClassroom)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'teacher_user_id' => 'required|exists:users,id'
        ]);

        try {
            $this->service->assignSubjectTeacher($subClassroom, $request->subject_id, $request->teacher_user_id);
            return $this->successResponse('Teacher assigned successfully to the subject.');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign subject teacher', $e->getMessage(), 500);
        }
    }

    public function getAssignments(SubClassroom $subClassroom)
    {
        try {
            $assignments = $this->service->getAssignments($subClassroom, request());
            return $this->successResponse(AssignmentResource::collection($assignments));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve assignments', $e->getMessage(), 500);
        }
    }

    public function getAssignedSubClassroom()
    {
        try {
            $list = $this->service->getAssignedSubClassrooms($this->user->detail()->id, request());
            return $this->successResponse(SubClassroomResource::collection($list));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve assigned sub-classrooms', $e->getMessage(), 500);
        }
    }

    public function assignTeacher(Request $request, SubClassroom $subClassroom)
    {
        $validated = $request->validate([
            'teacher_user_id' => 'required|exists:users,id',
        ]);

        try {
            $this->service->assignTeacher($subClassroom, $validated['teacher_user_id']);
            return $this->successResponse('Teacher assigned successfully to the sub-classroom.');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to assign teacher', $e->getMessage(), 500);
        }
    }

    public function syncStudents(Request $request)
    {
        try {
            $schoolId = $request->input('school_id');

            $this->service->syncStudents($schoolId);

            return $this->successResponse('Sync Success: Sub Classroom Subject Has Students has been populated.');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve subjects', $e->getMessage(), 500);
        }
    }

    public function count()
    {
        try {
            $activeSchoolId = session('active_school_id');
            $count = School::find($activeSchoolId)->subClassroomCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count sub-classrooms', $e->getMessage(), 500);
        }
    }
}
