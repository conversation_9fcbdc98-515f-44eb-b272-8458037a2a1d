<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubjectEnrollment\ClassTermRequest;
use App\Http\Requests\SubjectEnrollment\SetDefaultClassEnrollmentRequest;
use App\Http\Requests\SubjectEnrollment\ToggleAllRequest;
use App\Http\Resources\ClassEnrollmentSummaryResource;
use App\Models\SubClassroom;
use App\Models\SubClassroomSubject;
use App\Models\User;
use App\Services\ClassEnrollmentService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

class ClassEnrollmentController extends Controller
{
    protected ClassEnrollmentService $service;

    public function __construct(ClassEnrollmentService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * GET /classEnrollment/{subClassroom}/enrollments/summary?term_id=...
     */
    public function summary(SubClassroom $subClassroom, ClassTermRequest $request)
    {
        try {
            $termId = (int) $request->term_id;
            $result = $this->service->summary($termId, $subClassroom);

            $payload = [
                'term_id'           => $termId,
                'sub_classroom_id'  => $subClassroom->id,
                'combinations'      => $result['total_combinations'],
                'existing_rows'     => $result['existing_rows'],
                'enrolled_checked'  => $result['enrolled_checked'],
            ];

            return $this->successResponse(
                new ClassEnrollmentSummaryResource($payload),
                200,
                'Summary retrieved successfully'
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class or term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve summary', $e->getMessage(), 500);
        }
    }

    /**
     * PUT /classEnrollment/{subClassroom}/enrollments/sync
     */
    public function syncMatrix(SubClassroom $subClassroom, ClassTermRequest $request)
    {
        try {
            $count = $this->service->syncMatrix((int) $request->term_id, $subClassroom);

            return $this->successResponse([
                'inserted_or_kept' => $count,
            ], 200, 'Matrix synced successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class or term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to sync matrix', $e->getMessage(), 500);
        }
    }

    /**
     * POST /classEnrollment/{subClassroom}/enrollments/defaults
     */
    public function setDefault(SubClassroom $subClassroom, SetDefaultClassEnrollmentRequest $request)
    {
        try {
            $count = $this->service->setDefault(
                (int) $request->term_id,
                $subClassroom,
                (bool) $request->boolean('default'),
                $request->input('note')
            );

            return $this->successResponse([
                'affected' => $count,
            ], 200, 'Default enrollment set successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class or term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to set default enrollment', $e->getMessage(), 500);
        }
    }

    /**
     * PUT /classEnrollment/{subClassroom}/subjects/{subClassroomSubject}/enroll-all
     */
    public function enrollAllForSubject(
        SubClassroom $subClassroom,
        SubClassroomSubject $subClassroomSubject,
        ToggleAllRequest $request
    ) {
        try {
            // kolom FK benar: sub_classroom_id
            abort_if((int)$subClassroomSubject->sub_classroom_id !== (int)$subClassroom->id, 404);

            $count = $this->service->enrollAllForSubject(
                (int) $request->term_id,
                $subClassroom,
                (int) $subClassroomSubject->id,
                (bool) $request->boolean('is_enrolled'),
                $request->input('note')
            );

            return $this->successResponse([
                'affected' => $count,
            ], 200, 'Subject column updated for all students');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class/subject/term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update subject column', $e->getMessage(), 500);
        }
    }

    /**
     * PUT /classEnrollment/{subClassroom}/students/{student}/enroll-all-subjects
     */
    public function enrollAllForStudent(
        SubClassroom $subClassroom,
        User $student,
        ToggleAllRequest $request
    ) {
        try {
            $count = $this->service->enrollAllForStudent(
                (int) $request->term_id,
                $subClassroom,
                (int) $student->id,
                (bool) $request->boolean('is_enrolled'),
                $request->input('note')
            );

            return $this->successResponse([
                'affected' => $count,
            ], 200, 'Student row updated for all subjects');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Class/student/term not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update student row', $e->getMessage(), 500);
        }
    }
}
