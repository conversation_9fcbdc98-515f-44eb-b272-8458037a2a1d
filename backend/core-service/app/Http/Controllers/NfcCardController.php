<?php

namespace App\Http\Controllers;

use App\Http\Requests\NfcCard\StoreNfcCardRequest;
use App\Http\Requests\NfcCard\TopUpNfcCardRequest;
use App\Http\Requests\NfcCard\UpdateNfcCardRequest;
use App\Http\Resources\NfcCardResource;
use App\Http\Resources\NfcCardTransactionResource;
use App\Http\Resources\UserResource;
use App\Models\NfcCard;
use App\Models\NfcCardTransaction;
use App\Services\MidtransService;
use App\Services\NfcCardService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class NfcCardController extends Controller
{
    protected NfcCardService $service;
    protected MidtransService $midtransService;

    public function __construct(NfcCardService $service, MidtransService $midtransService)
    {
        parent::__construct();
        $this->service = $service;
        $this->midtransService = $midtransService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $nfcCards = $this->service->paginate($request);
            return $this->successResponse(NfcCardResource::collection($nfcCards));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve nfcCards', $e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNfcCardRequest $request)
    {
        try {
            $nfcCard = $this->service->store($request);
            return $this->successResponse(new NfcCardResource($nfcCard), 201, 'NfcCard created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create nfcCard', $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(NfcCard $nfcCard)
    {
        try {
            return $this->successResponse(new NfcCardResource($this->service->show($nfcCard)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve nfcCard', $e->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNfcCardRequest $request, NfcCard $nfcCard)
    {
        try {
            $nfcCard = $this->service->update($nfcCard, $request);
            return $this->successResponse(new NfcCardResource($nfcCard), 200, 'NfcCard updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update nfcCard', $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NfcCard $nfcCard)
    {
        try {
            $this->service->delete($nfcCard);
            return $this->successResponse('NfcCard deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete nfcCard', $e->getMessage(), 500);
        }
    }

    public function getUserByUid(NfcCard $nfcCard)
    {
        try {
            $user = $this->service->getUserByUid($nfcCard);
            return $this->successResponse(new UserResource($user));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve nfcCard', $e->getMessage(), 500);
        }
    }

    public function topUp(NfcCard $nfcCard, TopUpNfcCardRequest $request)
    {
        try {
            $nfcCard = $this->service->topUp($nfcCard, $request);
            return $this->successResponse(new NfcCardResource($nfcCard), 200, 'Transaction created successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to top up nfcCard', $e->getMessage(), 500);
        }
    }

    public function topUpWithMidtrans(NfcCard $nfcCard, TopUpNfcCardRequest $request)
    {
        try {
            $nfcCard = $this->service->topUp($nfcCard, $request);
            $snapToken = $this->midtransService->requestSnapToken([
                'order_id' => $nfcCard->transaction_number,
                'gross_amount' => $request->validated()['amount'],
            ]);
            return $this->successResponse($snapToken, 200, 'Transaction created successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('NfcCard not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to top up nfcCard', $e->getMessage(), 500);
        }
    }

    public function getAllTransactions(Request $request)
    {
        try {
            $transactions = $this->service->getAllTransactions($request);
            return $this->successResponse(NfcCardTransactionResource::collection($transactions), 200, 'Transaction approved successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Transaction not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to approve transaction', $e->getMessage(), 500);
        }
    }

    public function approveTransaction(NfcCardTransaction $nfcCardTransaction)
    {
        try {
            $transaction = $this->service->approveTransaction($nfcCardTransaction);
            return $this->successResponse($transaction, 200, 'Transaction approved successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Transaction not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to approve transaction', $e->getMessage(), 500);
        }
    }

    public function rejectTransaction(NfcCardTransaction $nfcCardTransaction)
    {
        try {
            $transaction = $this->service->rejectTransaction($nfcCardTransaction);
            return $this->successResponse($transaction, 200, 'Transaction rejected successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Transaction not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to reject transaction', $e->getMessage(), 500);
        }
    }

    public function setDailyLimit(NfcCard $nfcCard, Request $request)
    {
        try {
            $validated = $request->validate(['limit' => 'required|integer|min:0']);
            $nfcCard = $this->service->setDailyLimit($nfcCard, $validated['limit']);
            return $this->successResponse(new NfcCardResource($nfcCard), 200, 'Daily limit updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update daily limit', $e->getMessage(), 500);
        }
    }
}
