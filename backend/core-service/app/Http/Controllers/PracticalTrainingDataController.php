<?php

namespace App\Http\Controllers;

use App\Exports\PracticalTrainingDataTemplateExport;
use App\Http\Requests\PracticalTrainingData\StorePracticalTrainingDataRequest;
use App\Http\Requests\PracticalTrainingData\UpdatePracticalTrainingDataRequest;
use App\Http\Resources\PracticalTrainingDataResource;
use App\Imports\PracticalTrainingDataImport;
use App\Models\PracticalTrainingData;
use App\Services\PracticalTrainingDataService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PracticalTrainingDataController extends Controller
{
    protected PracticalTrainingDataService $service;

    public function __construct(PracticalTrainingDataService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Get All PracticalTrainingData
     */
    public function index(Request $request)
    {
        try {
            $practicalTrainingData = $this->service->paginate($request);
            return $this->successResponse(PracticalTrainingDataResource::collection($practicalTrainingData));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve practicalTrainingData', $e->getMessage(), 500);
        }
    }

    /**
     * Store PracticalTrainingData
     */
    public function store(StorePracticalTrainingDataRequest $request)
    {
        try {
            $practicalTrainingData = $this->service->store($request);
            return $this->successResponse(new PracticalTrainingDataResource($practicalTrainingData), 201, 'PracticalTrainingData created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create practicalTrainingData', $e->getMessage(), 500);
        }
    }

    /**
     * Get PracticalTrainingData Detail
     */
    public function show(PracticalTrainingData $practicalTrainingData)
    {
        try {
            return $this->successResponse(new PracticalTrainingDataResource($this->service->show($practicalTrainingData)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTrainingData not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve practicalTrainingData', $e->getMessage(), 500);
        }
    }

    /**
     * Update PracticalTrainingData
     */
    public function update(UpdatePracticalTrainingDataRequest $request, PracticalTrainingData $practicalTrainingData)
    {
        try {
            $practicalTrainingData = $this->service->update($practicalTrainingData, $request);
            return $this->successResponse(new PracticalTrainingDataResource($practicalTrainingData), 200, 'PracticalTrainingData updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTrainingData not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update practicalTrainingData', $e->getMessage(), 500);
        }
    }

    /**
     * Delete PracticalTrainingData
     */
    public function destroy(PracticalTrainingData $practicalTrainingData)
    {
        try {
            $this->service->delete($practicalTrainingData);
            return $this->successResponse('PracticalTrainingData deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTrainingData not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete practicalTrainingData', $e->getMessage(), 500);
        }
    }

    public function downloadTemplate()
    {
        return Excel::download(new PracticalTrainingDataTemplateExport, 'practical_training_data_template.xlsx');
    }

    public function import(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|mimes:xlsx,xls,csv',
                'practical_training_id' => 'required|integer|exists:practical_trainings,id',
            ], [
                'file.required' => 'Silakan pilih file Excel terlebih dahulu.',
                'file.mimes'    => 'Format file harus .xlsx, .xls, atau .csv',
                'practical_training_id.required' => 'Training ID wajib dipilih.',
                'practical_training_id.exists'   => 'Training ID tidak ditemukan di database.',
            ]);

            $trainingId = $request->integer('practical_training_id');

            Excel::import(
                new PracticalTrainingDataImport($trainingId),
                $request->file('file')
            );

            return $this->successResponse('PracticalTrainingData imported successfully.', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('PracticalTrainingData not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to import practicalTrainingData', $e->getMessage(), 500);
        }
    }
}
