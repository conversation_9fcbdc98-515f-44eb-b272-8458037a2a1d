<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResource;
use App\Models\School;
use App\Models\User;
use App\Services\StudentService;
use Exception;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Calculation\Statistical\Distributions\StudentT;

class StudentController extends Controller
{
    protected StudentService $service;

    public function __construct(StudentService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            $students = $this->service->paginate($request);
            return $this->successResponse(UserResource::collection($students));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve students', $e->getMessage(), 500);
        }
    }

public function import(Request $request)
{
    $request->validate(['file' => 'required|file|mimes:xlsx,xls']);

    try {
        // Ambil dari header saja
        $schoolId = (int) $request->header('X-Active-School-Id');

        if (!$schoolId) {
            return response()->json([
                'status' => false,
                'message' => 'school_id wajib dikirim lewat header X-Active-School-Id.'
            ], 422);
        }

        $this->service->import($request->file('file'), $schoolId);

        return response()->json([
            'status' => true,
            'message' => 'Import data Student berhasil'
        ], 201);
    } catch (\Throwable $e) {
        return response()->json([
            'status' => false,
            'message' => 'Import gagal: ' . $e->getMessage()
        ], 422);
    }
}



    public function export(Request $request)
    {
        try {
            $preset = $request->query('preset', 'students_all'); // 'students_all' | 'exam_token'

            return $this->service->export($preset);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to export students', $e->getMessage(), 500);
        }
    }

    public function count()
    {
        try {
            $activeSchoolId = session('active_school_id');
            $count = School::find($activeSchoolId)->studentCount();
            return $this->successResponse($count);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to count students', $e->getMessage(), 500);
        }
    }
}
