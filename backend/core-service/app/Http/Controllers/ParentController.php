<?php

namespace App\Http\Controllers;

use App\Http\Requests\ParentRequest;
use App\Http\Requests\Parent\LinkStudentRequest;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Resources\UserResource;
use App\Services\ParentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

class ParentController extends Controller
{
    protected ParentService $service;

    public function __construct(ParentService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            $parents = $this->service->paginate($request);
            return $this->successResponse(UserResource::collection($parents), 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve parents: ' . $e->getMessage(), 500);
        }
    }

    public function getStudents(ParentRequest $request)
    {
        try {
            $students = $this->service->getStudents($this->user->id, request('limit', config('app.pagination.max')));
            return $this->successResponse(UserResource::collection($students), 200);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve students: ' . $e->getMessage(), 500);
        }
    }

    public function addStudent(StoreUserRequest $request)
    {
        DB::beginTransaction();
        try {
            $student = $this->service->addStudent($request->validated(), $this->user->id);
            DB::commit();
            return $this->successResponse(new UserResource($student), 201, 'Student created successfully');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Failed to create student', $e->getMessage(), 500);
        }
    }

    public function linkStudent(LinkStudentRequest $request)
    {
        DB::beginTransaction();
        try {
            $result = $this->service->linkStudent($request);
            DB::commit();
            return $this->successResponse($result, 201, 'Student linked successfully');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Failed to create student', $e->getMessage(), 500);
        }
    }
    
    public function import(Request $request)
    {
        $request->validate(['file' => 'required|file|mimes:xlsx,xls']);

        try {
            // Get from header only
            $schoolId = (int) $request->header('X-Active-School-Id');

            if (!$schoolId) {
                return response()->json([
                    'status' => false,
                    'message' => 'school_id wajib dikirim lewat header X-Active-School-Id.'
                ], 422);
            }

            $this->service->import($request->file('file'), $schoolId);

            return response()->json([
                'status' => true,
                'message' => 'Import data Orang Tua berhasil'
            ], 201);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => false,
                'message' => 'Import gagal: ' . $e->getMessage()
            ], 422);
        }
    }
}
