<?php

namespace App\Http\Controllers;

use App\Http\Requests\ExamToken\GenerateTokenRequest;
use App\Http\Requests\ExamToken\StoreExamTokenRequest;
use App\Http\Requests\ExamToken\UpdateExamTokenRequest;
use App\Http\Resources\ExamTokenResource;
use App\Models\ExamToken;
use App\Services\ExamTokenService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ExamTokenController extends Controller
{
    protected ExamTokenService $service;

    public function __construct(ExamTokenService $service)
    {
        parent::__construct();
        $this->service = $service;
        $this->middleware('role:superadmin,foundation_admin,school_admin')->only(['index', 'store', 'update', 'destroy', 'show', 'genereteExamTokenForStudent', '    public function genereteExamTokenForAllStudent(GenerateTokenRequest $request)
']);
    }

    /**
     * Get All ExamToken
     */
    public function index(Request $request)
    {
        try {
            $examTokens = $this->service->paginate($request);
            return $this->successResponse(ExamTokenResource::collection($examTokens));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve examTokens', $e->getMessage(), 500);
        }
    }

    /**
     * Store ExamToken
     */
    public function store(StoreExamTokenRequest $request)
    {
        try {
            $examToken = $this->service->store($request);
            return $this->successResponse(new ExamTokenResource($examToken), 201, 'ExamToken created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create examToken', $e->getMessage(), 500);
        }
    }

    /**
     * Get ExamToken Detail
     */
    public function show(ExamToken $examToken)
    {
        try {
            return $this->successResponse(new ExamTokenResource($this->service->show($examToken)));
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamToken not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve examToken', $e->getMessage(), 500);
        }
    }

    /**
     * Update ExamToken
     */
    public function update(UpdateExamTokenRequest $request, ExamToken $examToken)
    {
        try {
            $examToken = $this->service->update($examToken, $request);
            return $this->successResponse(new ExamTokenResource($examToken), 200, 'ExamToken updated successfully');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamToken not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update examToken', $e->getMessage(), 500);
        }
    }

    /**
     * Delete ExamToken
     */
    public function destroy(ExamToken $examToken)
    {
        try {
            $this->service->delete($examToken);
            return $this->successResponse('ExamToken deleted successfully', 200);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('ExamToken not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete examToken', $e->getMessage(), 500);
        }
    }

    public function generateExamTokenForStudent(GenerateTokenRequest $request, $userId)
    {
        try {
            $examTokenData = $this->service->generateExamTokenForStudent($userId, $request->expiration);

            $examTokenObject = (object) [
                'user_id' => $userId,
                'exam_token' => $examTokenData['exam_token'],
                'exam_token_expiration' => $examTokenData['expiration']
            ];

            return $this->successResponse(new ExamTokenResource($examTokenObject), 200, 'Token generated successfully for user');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('User not found', null, 404);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to generate token', $e->getMessage(), 500);
        }
    }


    public function generateExamTokenForAllStudent(GenerateTokenRequest $request)
    {
        try {
            $this->service->generateExamTokenForAllStudents($request->expiration);

            return $this->successResponse(null, 200, 'Tokens generated successfully for all students');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to generate tokens', $e->getMessage(), 500);
        }
    }
}
