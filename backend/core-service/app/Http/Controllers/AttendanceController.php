<?php

namespace App\Http\Controllers;

use App\Http\Requests\Attendance\BatchStoreAttendanceRequest;
use App\Http\Requests\Attendance\ExportsAttendanceRequest;
use App\Http\Requests\Attendance\StoreAttendanceRequest;
use App\Http\Requests\Attendance\UpdateAttendanceRequest;
use App\Http\Resources\AttendanceResource;
use App\Services\AttendanceService;
use Exception;
use Illuminate\Http\Request;

class AttendanceController extends Controller
{
    protected AttendanceService $service;

    public function __construct(AttendanceService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    public function index(Request $request)
    {
        try {
            $attendances = $this->service->index($request);
            return $this->successResponse(AttendanceResource::collection($attendances));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to retrieve attendances', $e->getMessage(), 500);
        }
    }

    public function store(StoreAttendanceRequest $request)
    {
        try {
            return $this->successResponse($this->service->store($request, $this->user), 201, 'Attendance created successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create attendance', $e->getMessage(), 500);
        }
    }

    public function show($id)
    {
        return $this->successResponse($this->service->show($id));
    }

    public function update(UpdateAttendanceRequest $request, $id)
    {
        try {
            return $this->successResponse($this->service->update($request, $id), 200, 'Attendance updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to update attendance', $e->getMessage(), 500);
        }
    }

    public function destroy($id)
    {
        try {
            $this->service->destroy($id);
            return $this->successResponse(null, 200, 'Attendance deleted successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to delete attendance', $e->getMessage(), 500);
        }
    }

    public function batchStore(BatchStoreAttendanceRequest $request)
    {
        try {
            $this->service->batchStore($request->validated());
            return $this->successResponse(true, 201, 'Attendances created or updated successfully');
        } catch (Exception $e) {
            return $this->errorResponse('Failed to create or update attendances', $e->getMessage(), 500);
        }
    }

    public function attendanceSummaryForStudents(Request $request)
    {
        return $this->successResponse($this->service->getAttendanceSummaryByRole('student'), 200, 'Attendance students summary fetched successfully');
    }

    public function attendanceSummaryForTeachers(Request $request)
    {
        return $this->successResponse($this->service->getAttendanceSummaryByRole('teacher'), 200, 'Attendance teacher summary fetched successfully');
    }

    public function exportPdf(ExportsAttendanceRequest $request)
    {
        try {
            return $this->service->exportPdf($request);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to Export attendance Teacher', $e->getMessage(), 500);
        }
    }

    public function getTeacherAttendances(Request $request)
    {
        try {
            $attendances = $this->service->getTeacherAttendances($request);
            return $this->successResponse(AttendanceResource::collection($attendances));
        } catch (Exception $e) {
            return $this->errorResponse('Failed to fetch teacher attendances', $e->getMessage(), 500);
        }
    }

    public function getScheduleAttendanceSummary(Request $request)
    {
        try {
            $attendances = $this->service->getScheduleAttendanceSummary($request);
            return $this->successResponse($attendances);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to fetch schedule attendance summary', $e->getMessage(), 500);
        }
    }
}
