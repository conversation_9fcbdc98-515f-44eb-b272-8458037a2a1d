<?php

namespace App\Http\Requests\PracticalTrainingData;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StorePracticalTrainingDataRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust if you implement authorization
    }

    public function rules(): array
    {
        return [
            'practical_training_id' => 'nullable|integer|exists:practical_trainings,id',

            'learning_objective'    => 'required|string|max:255',

            'final_score'           => [
                'required',
                'numeric',
                'between:0,100',
                'regex:/^\d{1,3}(\.\d{1,2})?$/',
            ],

            'description'           => 'required|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'practical_training_id.integer' => 'The practical training ID must be an integer.',
            'practical_training_id.exists'  => 'The selected practical training does not exist.',

            'learning_objective.required'   => 'The learning objective is required.',
            'learning_objective.string'     => 'The learning objective must be a valid string.',
            'learning_objective.max'        => 'The learning objective must not exceed 255 characters.',

            'final_score.required'          => 'The final score is required.',
            'final_score.numeric'           => 'The final score must be a number.',
            'final_score.between'           => 'The final score must be between 0 and 100.',
            'final_score.regex'             => 'The final score may have at most two decimal places.',

            'description.required'          => 'The description is required.',
            'description.string'            => 'The description must be a valid string.',
            'description.max'               => 'The description must not exceed 1000 characters.',
        ];
    }

    /**
     * Consistent JSON response for validation errors
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors'  => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
