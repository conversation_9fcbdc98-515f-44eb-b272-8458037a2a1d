<?php

namespace App\Http\Requests\Grade;

use App\Rules\MatchesParentTerm;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreGradeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $examId = $this->input('exam_id');
        $assignmentId = $this->input('assignment_id');
        return [
            'term_id'         => ['nullable', 'integer', 'exists:terms,id', new MatchesParentTerm($examId, $assignmentId)],
            'student_user_id' => 'required|integer|exists:users,id',
            'assignment_id'   => 'nullable|integer|exists:assignments,id',
            'exam_id'         => 'nullable|integer|exists:exams,id',
            'report_card_id'  => 'nullable|integer|exists:report_cards,id',
            'type'            => 'nullable|string|in:assignment,mid_exam,final_exam',
            'score'           => 'nullable|integer|min:0|max:100',
            'notes'           => 'nullable|string',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'term_id.integer' => 'The term ID must be an integer.',
            'term_id.exists' => 'The selected term does not exist.',
            'student_user_id.required' => 'The student user ID is required.',
            'student_user_id.integer' => 'The student user ID must be an integer.',
            'student_user_id.exists' => 'The selected student user does not exist.',
            'assignment_id.integer' => 'The assignment ID must be an integer.',
            'assignment_id.exists' => 'The selected assignment does not exist.',
            'exam_id.integer' => 'The exam ID must be an integer.',
            'exam_id.exists' => 'The selected exam does not exist.',
            'report_card_id.integer' => 'The report card ID must be an integer.',
            'report_card_id.exists' => 'The selected report card does not exist.',
            'type.required' => 'The grade type is required.',
            'type.string' => 'The grade type must be a string.',
            'type.in' => 'The grade type must be one of the following: assignment, mid_exam, final_exam.',
            'score.required' => 'The score is required.',
            'score.integer' => 'The score must be an integer.',
            'score.min' => 'The score must be at least 0.',
            'score.max' => 'The score cannot exceed 100.',
            'notes.string' => 'The notes must be a string.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
