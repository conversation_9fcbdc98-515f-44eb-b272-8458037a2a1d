<?php

namespace App\Http\Requests\Event;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;


class UpdateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

    public function rules(): array
    {
        return [
            'school_id' => 'nullable|exists:schools,id',
            'title' => 'sometimes|required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'thumbnail_file' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            'clear_thumbnail' => 'nullable|boolean',
            'content' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'event_date' => 'nullable|date',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
            'is_published' => 'nullable|boolean',
            'files' => 'nullable|array|max:10',
            'files.*' => 'file|mimes:jpeg,png,jpg,gif,svg,mp4,mov,avi,pdf,doc,docx,xls,xlsx|max:5120',
            'deleted_files' => 'nullable|array',
            'deleted_files.*' => [
                'integer',
                Rule::exists('event_files', 'id')->where(function ($query) {
                    $query->where('event_id', $this->event->id);
                }),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'thumbnail_file.image' => 'The thumbnail file must be an image.',
            'thumbnail_file.mimes' => 'The allowed thumbnail file formats are: jpeg, png, jpg, gif, svg.',
            'thumbnail_file.max' => 'The thumbnail file size must not exceed 5MB.',
            'files.*.mimes' => 'Only image (jpeg, png, jpg, gif, svg), video (mp4, mov, avi), or document (pdf, doc, docx, xls, xlsx) file formats are allowed for content files.',
            'files.*.max' => 'The content file size must not exceed 5MB.',
            'files.max' => 'The number of content files must not exceed 10.',
            'deleted_files.*.exists' => 'One of the files to be deleted is invalid or not associated with this event.',
            'end_time.after_or_equal' => 'The end time must be after or equal to the start time.',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_published' => filter_var($this->is_published, FILTER_VALIDATE_BOOLEAN),
            'clear_thumbnail' => filter_var($this->clear_thumbnail, FILTER_VALIDATE_BOOLEAN),

        ]);
    }
    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
