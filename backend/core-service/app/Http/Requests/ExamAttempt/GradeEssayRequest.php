<?php

namespace App\Http\Requests\ExamAttempt;

use App\Models\ExamAttemptAnswer;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class GradeEssayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'exam_attempt_answers_id' => 'required|exists:exam_attempt_answers,id',
            'points_awarded' => 'required|numeric|min:0',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            //
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $answerId = $this->input('exam_attempt_answers_id');
            $answer = ExamAttemptAnswer::with('examQuestion')->find($answerId);
            if (!$answer || !$answer->examQuestion) {
                $validator->errors()->add('exam_attempt_answers_id', 'Exam question not found for this answer.');
                return;
            }
            $maxPoints = $answer->examQuestion->points;
            $givenPoints = $this->input('points_awarded');

            if ($givenPoints > $maxPoints) {
                $validator->errors()->add('points_awarded', "Points awarded can't exceed the maximum of {$maxPoints}.");
            }
        });
    }
}
