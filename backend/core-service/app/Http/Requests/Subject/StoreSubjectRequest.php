<?php

namespace App\Http\Requests\Subject;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $schoolId = $this->input('school_id') ?? session('active_school_id');
                    if ($schoolId && \App\Models\Subject::where('name', $value)
                            ->where('school_id', $schoolId)
                            ->exists()) {
                        $fail('Mata pelajaran dengan nama ini sudah ada di sekolah ini.');
                    }
                }
            ],
            'description' => 'nullable|string|max:1000',
            'priority' => 'nullable|integer|min:1',
            'school_id' => 'nullable|integer|exists:schools,id',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'name.required' => 'Nama mata pelajaran wajib diisi.',
            'name.string' => 'Nama mata pelajaran harus berupa teks.',
            'name.max' => 'Nama mata pelajaran tidak boleh melebihi 255 karakter.',
            'description.max' => 'Deskripsi tidak boleh melebihi 1000 karakter.',
            'priority.integer' => 'Prioritas harus berupa angka.',
            'priority.min' => 'Prioritas minimal bernilai 1.',
            'school_id.exists' => 'Sekolah yang dipilih tidak valid.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
