<?php

namespace App\Http\Requests\PracticalTraining;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StorePracticalTrainingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; 
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'term_id'           => 'required|integer|exists:terms,id',
            'student_user_id'   => 'required|integer|exists:users,id',
            'program_of_study'  => 'nullable|string|max:255',
            'specialization'    => 'nullable|string|max:255',
            'place'             => 'required|string|max:255',
            'period_start'      => 'required|date',
            'period_end'        => 'required|date|after_or_equal:period_start',
            'instructor_name'   => 'required|string|max:255',
            'mentor_name'       => 'required|string|max:255',
            'sick'              => 'nullable|integer|min:0',
            'leave'             => 'nullable|integer|min:0',
            'present'           => 'nullable|integer|min:0',
            'alpha'             => 'nullable|integer|min:0',
            'class_teacher_note' => 'nullable|string|max:5000',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages(): array
    {
        return [
            'term_id.required'          => 'The academic term is required.',
            'term_id.exists'            => 'The selected academic term does not exist.',

            'student_user_id.required'  => 'The student is required.',
            'student_user_id.exists'    => 'The selected student does not exist.',

            'program_of_study.max'      => 'The program of study must not exceed 255 characters.',

            'specialization.max'        => 'The specialization must not exceed 255 characters.',

            'place.required'            => 'The training place is required.',
            'place.max'                 => 'The training place must not exceed 255 characters.',

            'period_start.required'     => 'The start period is required.',
            'period_start.date'         => 'The start period must be a valid date.',

            'period_end.required'       => 'The end period is required.',
            'period_end.date'           => 'The end period must be a valid date.',
            'period_end.after_or_equal' => 'The end period must be after or equal to the start period.',

            'instructor_name.required'  => 'The instructor name is required.',
            'instructor_name.max'       => 'The instructor name must not exceed 255 characters.',

            'mentor_name.required'      => 'The mentor name is required.',
            'mentor_name.max'           => 'The mentor name must not exceed 255 characters.',

            'sick.integer'              => 'The number of sick days must be an integer.',
            'sick.min'                  => 'The number of sick days cannot be negative.',

            'leave.integer'             => 'The number of leave days must be an integer.',
            'leave.min'                 => 'The number of leave days cannot be negative.',

            'present.integer'           => 'The number of present days must be an integer.',
            'present.min'               => 'The number of present days cannot be negative.',

            'alpha.integer'             => 'The number of alpha days must be an integer.',
            'alpha.min'                 => 'The number of alpha days cannot be negative.',

            'class_teacher_note.max'    => 'The class teacher note must not exceed 5000 characters.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
