<?php

namespace App\Http\Requests\ExamQuestion;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CopyExamQuestionsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'source_exam_id'  => ['required', 'integer', 'exists:exams,id'],
            'question_ids'    => ['required', 'array', 'min:1'],
            'question_ids.*'  => ['integer', 'exists:exam_questions,id'],
            'insert_mode'     => ['nullable', 'in:append,prepend'],
            'deep_copy_media' => ['nullable', 'boolean'],
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
