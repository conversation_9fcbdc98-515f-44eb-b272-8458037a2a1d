<?php

namespace App\Http\Requests\ExamQuestion;

use App\Enums\CognitiveLevel;
use App\Enums\QuestionType;
use App\Models\Exam;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;

class StoreMultipleExamQuestionsRequest extends FormRequest
{
    /**
     * Autorisasi:
     * - admin/superadmin => selalu boleh
     * - teacher          => hanya jika exam milik SCS miliknya
     */
    public function authorize(): bool
    {
        $user = $this->user();

        // 1) Admin / Superadmin => lewati
        if (method_exists($user, 'hasRole') && ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            return true;
        }

        // 2) Ambil exam dari route (support model atau id)
        $examParam = $this->route('exam');
        /** @var Exam|null $exam */
        $exam = $examParam instanceof Exam ? $examParam : Exam::query()->find($examParam);

        if (!$exam) {
            return false; // exam tidak ditemukan
        }

        // 3) Teacher: cek kepemilikan SCS
        if (method_exists($user, 'hasRole') && $user->hasRole('teacher')) {
            $teacherId = $this->resolveTeacherId($user);

            return DB::table('sub_classroom_subjects')
                ->whereNull('deleted_at')
                ->where('id', $exam->sub_classroom_subject_id)
                ->where('teacher_user_id', $teacherId)
                ->exists();
        }

        // Selain role di atas → tolak
        return false;
    }

    /**
     * Sisipkan exam_id & normalisasi struktur sebelum validasi
     */
    protected function prepareForValidation(): void
    {
        $examParam = $this->route('exam');
        $examId    = $examParam instanceof Exam ? $examParam->id : (int) $examParam;

        // Set exam_id global agar gampang di-rule
        $this->merge(['exam_id' => $examId]);

        // Pastikan tiap question juga memuat exam_id (kalau BE logic kamu memerlukannya)
        if ($this->has('questions') && is_array($this->questions)) {
            $qs = $this->questions;
            foreach ($qs as $i => $q) {
                $qs[$i]['exam_id'] = $examId;
            }
            $this->merge(['questions' => $qs]);
        }
    }

    /**
     * Rules dasar.
     * Catatan: aturan untuk answer_options akan dipertegas di withValidator()
     * hanya saat question_type = MULTIPLE_CHOICE.
     */
    public function rules(): array
    {
        return [
            'exam_id'    => ['required', 'integer', 'exists:exams,id'],
            'questions'  => ['required', 'array', 'min:1'],

            'questions.*.content'         => ['required', 'string'],
            'questions.*.answer_key_essay' => ['nullable', 'string'],
            'questions.*.question_type'   => ['required', 'in:' . implode(',', array_column(QuestionType::cases(), 'value'))],
            'questions.*.points'          => ['required', 'numeric', 'min:1', 'max:100'],
            'questions.*.order_index'     => ['required', 'integer', 'min:0'],

            'questions.*.media_questions' => ['nullable', 'file', 'mimes:jpg,jpeg,png,mp4,mp3,wav', 'max:20480'],

            'questions.*.kd_number'             => ['required', 'string'],
            'questions.*.learning_outcome'      => ['required', 'string'],
            'questions.*.competency_indicator'  => ['required', 'string'],
            'questions.*.level_kognitif'        => ['required', new Enum(CognitiveLevel::class)],

            'questions.*.answer_options'                     => ['nullable', 'array'],
            'questions.*.answer_options.*.content'           => ['required_with:questions.*.answer_options', 'string'],
            'questions.*.answer_options.*.is_correct'        => ['required_with:questions.*.answer_options', 'boolean'],
            'questions.*.answer_options.*.order_index'       => ['nullable', 'integer', 'min:0'],
            'questions.*.answer_options.*.media_answer_options' => ['nullable', 'file', 'mimes:jpg,jpeg,png,mp3,wav', 'max:10240'],
        ];
    }

    public function messages(): array
    {
        return [
            'exam_id.required'  => 'The exam ID is required.',
            'exam_id.exists'    => 'The specified exam does not exist.',
            'questions.required' => 'You must provide at least one question.',
            'questions.array'   => 'Questions must be provided as an array.',
            'questions.min'     => 'You must provide at least one question.',

            'questions.*.answer_options.*.content.required_with'   => 'Each answer option must have content.',
            'questions.*.answer_options.*.is_correct.required_with' => 'Each answer option must specify whether it is correct.',
        ];
    }

    public function attributes(): array
    {
        return [
            'exam_id'                                 => 'exam ID',
            'questions'                               => 'questions',
            'questions.*.content'                     => 'question content',
            'questions.*.answer_key_essay'           => 'answer key for essay questions',
            'questions.*.question_type'               => 'question type',
            'questions.*.points'                      => 'points',
            'questions.*.order_index'                 => 'order index',
            'questions.*.media_questions'             => 'media questions',
            'questions.*.kd_number'                   => 'KD number',
            'questions.*.learning_outcome'            => 'learning outcome',
            'questions.*.competency_indicator'        => 'competency indicator',
            'questions.*.level_kognitif'              => 'level kognitif',
            'questions.*.answer_options'              => 'answer options',
            'questions.*.answer_options.*.content'    => 'answer option content',
            'questions.*.answer_options.*.is_correct' => 'correct answer indicator',
            'questions.*.answer_options.*.order_index' => 'answer option order',
        ];
    }

    /**
     * Validasi lanjutan: jika tipe = multiple_choice
     *  - minimal 2 opsi
     *  - minimal ada 1 is_correct = true
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $questions = $this->input('questions', []);
            if (!is_array($questions)) {
                return;
            }

            foreach ($questions as $index => $question) {
                $type = $question['question_type'] ?? null;

                if ($type === QuestionType::MULTIPLE_CHOICE->value) {
                    $options = $question['answer_options'] ?? [];

                    if (!is_array($options) || count($options) < 2) {
                        $validator->errors()->add(
                            "questions.{$index}.answer_options",
                            'Multiple choice question must have at least 2 answer options.'
                        );
                        continue;
                    }

                    $hasCorrect = collect($options)->contains('is_correct', true);
                    if (!$hasCorrect) {
                        $validator->errors()->add(
                            "questions.{$index}.answer_options",
                            'At least one answer option must be marked as correct.'
                        );
                    }
                }
            }
        });
    }

    /**
     * JSON 422
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'The given data was invalid.',
            'errors'  => $validator->errors(),
        ], 422));
    }

    /**
     * JSON 403
     */
    protected function failedAuthorization()
    {
        throw new HttpResponseException(response()->json([
            'message' => 'You are not authorized to add questions to this exam.',
            'details' => 'Only admin/superadmin or the assigned teacher for this exam can add questions.',
            'success' => false,
        ], 403));
    }

    /**
     * Helper: menentukan id guru yang digunakan di SCS
     * (ubah jika teacher_user_id menyimpan id dari relasi detail())
     */
    private function resolveTeacherId($user): int
    {
        if (method_exists($user, 'detail') && $user->detail()) {
            return (int) $user->detail()->id;
        }
        return (int) $user->id;
    }
}
