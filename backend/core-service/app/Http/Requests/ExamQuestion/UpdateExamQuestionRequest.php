<?php

namespace App\Http\Requests\ExamQuestion;

use App\Models\Exam;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UpdateExamQuestionRequest extends FormRequest
{
    /**
     * Autorisasi:
     * - admin/superadmin => boleh
     * - teacher => hanya jika exam milik SCS miliknya
     */
    public function authorize(): bool
    {
        $user = $this->user();

        // 1) Admin / Superadmin: langsung boleh
        if (method_exists($user, 'hasRole') && ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            return true;
        }

        // 2) Ambil exam dari route (mendukung binding model atau id)
        $examParam = $this->route('exam');
        /** @var Exam|null $exam */
        $exam = $examParam instanceof Exam ? $examParam : Exam::query()->find($examParam);

        if (! $exam) {
            return false; // exam tidak ditemukan
        }

        // 3) Teacher: cek kepemilikan SCS
        if (method_exists($user, 'hasRole') && $user->hasRole('teacher')) {
            $teacherId = $this->resolveTeacherId($user);

            return DB::table('sub_classroom_subjects')
                ->whereNull('deleted_at')
                ->where('id', $exam->sub_classroom_subject_id)
                ->where('teacher_user_id', $teacherId)
                ->exists();
        }

        // Selain role di atas → tolak
        return false;
    }

    /**
     * Normalisasi data sebelum validasi (opsional, berguna kalau FE kirim camelCase).
     * Sekaligus injeksi exam_id dari route supaya rules bisa pakai jika perlu.
     */
    protected function prepareForValidation(): void
    {
        $examParam = $this->route('exam');
        $examId = $examParam instanceof Exam ? $examParam->id : (int) $examParam;

        $this->merge([
            'exam_id'        => $examId,
            'order_index'    => $this->input('orderIndex', $this->input('order_index')),
            'answer_options' => $this->input('answerOptions', $this->input('answer_options')),
        ]);
    }

    /**
     * Rules: hanya field yang dikirim yang divalidasi (update).
     */
    public function rules(): array
    {
        return [
            'exam_id' => ['required', 'integer', Rule::exists('exams', 'id')],

            'content'      => ['required', 'string'],
            'answer_key_essay' => ['nullable', 'string'], 
            'points'       => ['required', 'numeric', 'min:0', 'max:100'],
            'order_index'  => ['required', 'integer', 'min:0'],
            'image'        => ['nullable', 'string'],

            'answer_options'                   => ['required', 'array', 'min:2'],
            'answer_options.*.content'         => ['required', 'string'],
            'answer_options.*.is_correct'      => ['required', 'boolean'],
            'answer_options.*.order_index'     => ['nullable', 'integer', 'min:0'],
            'answer_options.*.image'           => ['nullable', 'string'],

            'kd_number'             => ['nullable', 'string', 'max:50'],
            'learning_outcome'      => ['nullable', 'string'],
            'competency_indicator'  => ['nullable', 'string'],
            'level_kognitif'        => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'answer_options.min'                => 'Every exam question must have at least 2 answer options.',
            'answer_options.*.content.required' => 'Each answer option must have content.',
            'answer_options.*.is_correct.required' => 'Each answer option must specify whether it is correct.',
        ];
    }

    public function attributes(): array
    {
        return [
            'content'                        => 'question content',
            'answer_options'                 => 'answer options',
            'answer_options.*.content'       => 'answer option content',
            'answer_options.*.is_correct'    => 'correct answer indicator',
            'answer_options.*.order_index'   => 'answer option order',
        ];
    }

    /**
     * Validasi tambahan: pastikan minimal ada satu opsi benar.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $options = collect($this->input('answer_options', []));
            $hasCorrect = $options->contains('is_correct', true);

            if (! $hasCorrect) {
                $validator->errors()->add('answer_options', 'At least one answer option must be marked as correct.');
            }
        });
    }

    /**
     * JSON 422 saat gagal validasi.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'The given data was invalid.',
            'errors'  => $validator->errors(),
        ], 422));
    }

    /**
     * JSON 403 saat tidak berwenang.
     */
    protected function failedAuthorization()
    {
        throw new HttpResponseException(response()->json([
            'message' => 'You are not authorized to update this question.',
            'details' => 'Only admin/superadmin or the assigned teacher for this exam can update its questions.',
            'success' => false,
        ], 403));
    }

    /**
     * Helper: resolve id guru yang dipakai di SCS.
     */
    private function resolveTeacherId($user): int
    {
        if (method_exists($user, 'detail') && $user->detail()) {
            return (int) $user->detail()->id;
        }
        return (int) $user->id;
    }
}
