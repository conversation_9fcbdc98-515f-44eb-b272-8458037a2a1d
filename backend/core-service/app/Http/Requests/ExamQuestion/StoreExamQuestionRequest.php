<?php

namespace App\Http\Requests\ExamQuestion;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;

class StoreExamQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $exam = $this->route('exam');
        $user = $this->user();

        if (!$user->hasRole('teacher')) {
            return false;
        }

        return DB::table('sub_classroom_subjects')
            ->where('id', $exam->sub_classroom_subject_id)
            ->where('teacher_user_id', $user->id)
            ->exists();
    }

    /**
     * Get the validator instance for the request.
     */
    protected function getValidatorInstance()
    {
        // Add exam_id to the request data before validation
        $this->merge(['exam_id' => $this->route('exam')->id]);

        return parent::getValidatorInstance();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => 'required|string',
            'answer_key_essay' => 'nullable|string',
            'points' => 'required|numeric|min:1|max:100',
            'order_index' => 'required|integer|min:0',
            'kd_number' => 'nullable|string',
            'learning_outcome'     => 'nullable|string',
            'competency_indicator' => 'nullable|string',
            'media_questions' => 'nullable|string',
            'answer_options' => 'required|array|min:2',
            'answer_options.*.content' => 'required|string',
            'answer_options.*.is_correct' => 'required|boolean',
            'answer_options.*.order_index' => 'integer|min:0',
            'answer_options.*.media_questions' => 'nullable|string',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'answer_options.min' => 'Every exam question must have at least 2 answer options.',
            'answer_options.*.content.required' => 'Each answer option must have content.',
            'answer_options.*.is_correct.required' => 'Each answer option must specify whether it is correct.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'content' => 'question content',
            'answer_key_essay' => 'answer key for essay questions',
            'question_type' => 'question type',
            'points' => 'points',
            'order_index' => 'order index',
            'media_questions' => 'media questions',
            'kd_number' => 'KD number',
            'learning_outcome' => 'learning outcome',
            'competency_indicator' => 'competency indicator',
            'level_kognitif' => 'level kognitif',
            'answer_options' => 'answer options',
            'answer_options.*.content' => 'answer option content',
            'answer_options.*.is_correct' => 'correct answer indicator',
            'answer_options.*.order_index' => 'answer option order',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if at least one answer option is marked as correct
            $hasCorrectOption = collect($this->answer_options)->contains('is_correct', true);

            if (!$hasCorrectOption) {
                $validator->errors()->add(
                    'answer_options',
                    'At least one answer option must be marked as correct.'
                );
            }
        });
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
