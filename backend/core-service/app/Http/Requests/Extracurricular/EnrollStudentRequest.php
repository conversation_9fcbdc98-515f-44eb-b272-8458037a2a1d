<?php
namespace App\Http\Requests\Extracurricular;

use Illuminate\Foundation\Http\FormRequest;

class EnrollStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'student_user_id' => 'required|integer|exists:users,id',
            'term_id'         => 'required|integer|exists:terms,id',
        ];
    }
}
