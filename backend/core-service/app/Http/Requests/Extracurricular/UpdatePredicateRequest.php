<?php

// app/Http/Requests/Extracurricular/UpdatePredicateRequest.php
namespace App\Http\Requests\Extracurricular;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePredicateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'predicate' => 'required|string|in:SB,B,C,K',
        ];
    }
}
