<?php

namespace App\Http\Requests\FinancialCore;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreFinancialCoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Prepare the data for validation by converting camelCase to snake_case.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'school_id' => $this->schoolId ?? $this->school_id,
            'student_user_id' => $this->studentUserId ?? $this->student_user_id,
            'parent_user_id' => $this->parentUserId ?? $this->parent_user_id,
            'reference_type' => $this->referenceType ?? $this->reference_type,
            'reference_id' => $this->referenceId ?? $this->reference_id,
            'debit_amount' => $this->debitAmount ?? $this->debit_amount,
            'credit_amount' => $this->creditAmount ?? $this->credit_amount,
            'transaction_date' => $this->transactionDate ?? $this->transaction_date,
            'transaction_time' => $this->transactionTime ?? $this->transaction_time,
            'transaction_timezone' => $this->transactionTimezone ?? $this->transaction_timezone,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'school_id' => 'nullable|exists:schools,id',
            'student_user_id' => 'nullable|exists:users,id',
            'parent_user_id' => 'nullable|exists:users,id',
            'reference_type' => 'required|string|in:tuition,canteen,cashless,others',
            'reference_id' => 'nullable|string|max:255',
            'debit_amount' => 'nullable|numeric|min:0',
            'credit_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'status' => 'nullable|string|in:pending,completed,failed',
            'transaction_date' => 'nullable|date',
            'transaction_time' => 'nullable',
            'transaction_timezone' => 'nullable|string|max:50',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $debitAmount = $this->input('debit_amount', 0);
            $creditAmount = $this->input('credit_amount', 0);
            
            // Either debit or credit amount must be greater than 0, but not both
            if ($debitAmount <= 0 && $creditAmount <= 0) {
                $validator->errors()->add('amount', 'Either debit amount or credit amount must be greater than 0.');
            }
            
            if ($debitAmount > 0 && $creditAmount > 0) {
                $validator->errors()->add('amount', 'Only one of debit amount or credit amount should be provided, not both.');
            }
            
            // Transaction time validation is handled in the service layer for better flexibility
        });
    }

    public function messages(): array
    {
        return [
            'reference_type.in' => 'Reference type must be one of: tuition, canteen, cashless, others',
            'debit_amount.min' => 'Debit amount cannot be negative',
            'credit_amount.min' => 'Credit amount cannot be negative',
            'amount' => 'Either debit amount or credit amount must be provided, but not both.',
        ];
    }



    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
