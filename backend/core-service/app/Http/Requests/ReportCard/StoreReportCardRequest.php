<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreReportCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'term_id'                  => 'required|integer|exists:terms,id',
            'student_user_id'          => 'required|integer|exists:users,id',
            'sub_classroom_subject_id' => 'required|integer|exists:sub_classroom_subjects,id',
            'final_score'              => 'nullable|numeric|min:0|max:100',
            'revision_score'           => 'nullable|numeric|min:0|max:100',
            'grade'                    => 'nullable|string|max:5',
            'notes'                    => 'nullable|string',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'term_id.required' => 'The term ID is required.',
            'term_id.integer' => 'The term ID must be an integer.',
            'term_id.exists' => 'The selected term does not exist.',
            'student_user_id.required' => 'The student user ID is required.',
            'student_user_id.integer' => 'The student user ID must be an integer.',
            'student_user_id.exists' => 'The selected student user does not exist.',
            'sub_classroom_subject_id.required' => 'The sub-classroom-subject ID is required.',
            'sub_classroom_subject_id.integer' => 'The sub-classroom-subject ID must be an integer.',
            'sub_classroom_subject_id.exists' => 'The selected sub-classroom-subject does not exist.',
            'final_score.numeric' => 'The final score must be a number.',
            'final_score.min' => 'The final score must be at least 0.',
            'final_score.max' => 'The final score may not be greater than 100.',
            'revision_score.numeric' => 'The revision score must be a number.',
            'revision_score.min' => 'The revision score must be at least 0.',
            'revision_score.max' => 'The revision score may not be greater than 100.',
            'grade.string' => 'The grade must be a string.',
            'grade.max' => 'The grade may not be greater than 5 characters.',
            'notes.string' => 'The notes must be a string.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
