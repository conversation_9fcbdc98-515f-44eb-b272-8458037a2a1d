<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
class UpsertAssignmentGradesSmartRequest extends UpsertAssignmentGradesRequest
{
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'term_id'                  => ['required', 'integer', 'exists:terms,id'],
            'student_user_id'          => ['required', 'integer', 'exists:users,id'],
            'sub_classroom_subject_id' => ['required', 'integer', 'exists:sub_classroom_subjects,id'],
            'notes'                    => ['nullable', 'string'],
        ]);
    }

    public function messages()
    {
        return [
            'term_id.required' => 'The term ID is required.',
            'term_id.integer' => 'The term ID must be an integer.',
            'term_id.exists' => 'The selected term does not exist.',
            'student_user_id.required' => 'The student user ID is required.',
            'student_user_id.integer' => 'The student user ID must be an integer.',
            'student_user_id.exists' => 'The selected student user does not exist.',
            'sub_classroom_subject_id.required' => 'The sub-classroom-subject ID is required.',
            'sub_classroom_subject_id.integer' => 'The sub-classroom-subject ID must be an integer.',
            'sub_classroom_subject_id.exists' => 'The selected sub-classroom-subject does not exist.',
            'notes.string' => 'The notes must be a string.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
