<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class RevisionScoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        //             'revision_score' => 'nullable|numeric|min:0|max:100',

        return [
            'revision_score'           => 'nullable|numeric|min:0|max:100',
            'term_id' => ['nullable', 'integer', 'exists:terms,id'],
            'student_user_id' => ['nullable', 'integer', 'exists:users,id'],
            'sub_classroom_subject_id' => ['nullable', 'integer', 'exists:sub_classroom_subjects,id'],
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'revision_score.numeric' => 'The revision score must be a number.',
            'revision_score.min' => 'The revision score must be at least 0.',
            'revision_score.max' => 'The revision score may not be greater than 100.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
