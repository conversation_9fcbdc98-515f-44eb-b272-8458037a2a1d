<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpsertAssignmentGradesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'assignment_grades'   => ['required', 'array'],
            'assignment_grades.*' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'notes'               => ['nullable', 'string'],
        ];
    }

    public function messages()
    {
        return [
            'assignment_grades.required' => 'The assignment grades are required.',
            'assignment_grades.array' => 'The assignment grades must be an array.',
            'assignment_grades.*.numeric' => 'Each assignment grade must be a number.',
            'assignment_grades.*.min' => 'Each assignment grade must be at least 0.',
            'assignment_grades.*.max' => 'Each assignment grade may not be greater than 100.',
            'notes.string' => 'The notes must be a string.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
