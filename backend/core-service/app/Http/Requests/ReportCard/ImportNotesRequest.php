<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Foundation\Http\FormRequest;

class ImportNotesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'term_id' => 'required|integer|exists:terms,id',
            'sub_classroom_subject_id' => 'required|integer|exists:sub_classroom_subjects,id',
            'note_type' => 'required|string|in:mid,final',
            'file' => 'required|file|mimes:xlsx,csv,txt',
        ];
    }
}
