<?php

namespace App\Http\Requests\ReportCard;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateReportCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Update authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'term_id' => 'sometimes|integer|exists:terms,id',
            'student_user_id' => 'sometimes|integer|exists:users,id',
            'sub_classroom_subject_id' => 'sometimes|integer|exists:sub_classroom_subjects,id',
            'final_score' => 'sometimes|integer|min:0|max:100',
            'revision_score' => 'sometimes|integer|min:0|max:100',
            'grade' => 'sometimes|string|max:255',
            'notes' => 'sometimes|string',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'term_id.integer' => 'The term ID must be an integer.',
            'term_id.exists' => 'The selected term does not exist.',
            'student_user_id.integer' => 'The student user ID must be an integer.',
            'student_user_id.exists' => 'The selected student user does not exist.',
            'sub_classroom_subject_id.integer' => 'The sub-classroom-subject ID must be an integer.',
            'sub_classroom_subject_id.exists' => 'The selected sub-classroom-subject does not exist.',
            'final_score.integer' => 'The final score must be an integer.',
            'final_score.min' => 'The final score must be at least 0.',
            'final_score.max' => 'The final score cannot exceed 100.',
            'revision_score.integer' => 'The revision score must be an integer.',
            'revision_score.min' => 'The revision score must be at least 0.',
            'revision_score.max' => 'The revision score cannot exceed 100.',
            'grade.string' => 'The grade must be a string.',
            'grade.max' => 'The grade must not exceed 255 characters.',
            'notes.string' => 'The notes must be a string.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
