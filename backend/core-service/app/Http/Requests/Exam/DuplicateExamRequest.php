<?php

namespace App\Http\Requests\Exam;


use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class DuplicateExamRequest extends FormRequest
{
    public function authorize(): bool
    {
        $user = $this->user();
        return $user && ($user->isAdminOrSuperUser());
    }

    public function rules(): array
    {
        return [
            'title' => 'nullable |strin |max:255',
            'sub_classroom_subject_id' => 'nullable |integer |exists:sub_classroom_subjects|id',
            'shift_days' => 'nullable|integer',
            'is_published' => 'nullable|boolean',
            'is_shuffled' => 'nullable|boolean',
            'passing_score' => 'nullable|numeric',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'sub_classroom_subject_id' => $this->filled('sub_classroom_subject_id') ? (int) $this->input('sub_classroom_subject_id') : null,
            'shift_days'              => $this->filled('shift_days') ? (int) $this->input('shift_days') : null,
            'is_published'            => $this->filled('is_published') ? filter_var($this->input('is_published'), FILTER_VALIDATE_BOOL, FILTER_NULL_ON_FAILURE) : null,
            'is_shuffled'             => $this->filled('is_shuffled') ? filter_var($this->input('is_shuffled'), FILTER_VALIDATE_BOOL, FILTER_NULL_ON_FAILURE) : null,
        ]);
    }

    public function messages(): array
    {
        return [
            'sub_classroom_subject_id.exists' => 'The selected subject does not exist or has been removed.',
        ];
    }

    public function attributes(): array
    {
        return [
            'sub_classroom_subject_id' => 'Sub Classroom Subject',
            'shift_days' => 'Shift Days',
        ];
    }

    protected function failedValidationn(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
