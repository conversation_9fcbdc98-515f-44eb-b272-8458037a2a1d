<?php

namespace App\Http\Requests\Exam;

use App\Enums\ExamType;
use App\Models\Exam;
use App\Rules\TermContainsDateRange;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class UpdateExamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        if (method_exists($user, 'hasRole') && ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            return true;
        }

        $examParam = $this->route('exam');
        /** @var Exam|null $exam */
        $exam = $examParam instanceof Exam ? $examParam : Exam::query()->find($examParam);

        if (!$exam) {
            return false;
        }

        if (!(method_exists($user, 'hasRole') && $user->hasRole('teacher'))) {
            return false;
        }

        $teacherId = method_exists($user, 'detail') && $user->detail()
            ? (int) $user->detail()->id
            : (int) $user->id;

        $ownsCurrent = DB::table('sub_classroom_subjects')
            ->whereNull('deleted_at')
            ->where('id', $exam->sub_classroom_subject_id)
            ->where('teacher_user_id', $teacherId)
            ->exists();

        if (!$ownsCurrent) {
            return false;
        }

        if ($this->filled('sub_classroom_subject_id')) {
            $targetScsId = (int) $this->input('sub_classroom_subject_id');

            $ownsTarget = DB::table('sub_classroom_subjects')
                ->whereNull('deleted_at')
                ->where('id', $targetScsId)
                ->where('teacher_user_id', $teacherId)
                ->exists();

            if (!$ownsTarget) {
                return false;
            }
        }

        return true;
    }

    /**
     * Normalize camelCase → snake_case so rules can validate properly.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'exams_type'               => $this->input('examsType', $this->input('exams_type')),
            'sub_classroom_subject_id' => $this->input('subClassroomSubjectId', $this->input('sub_classroom_subject_id')),
            'start_datetime'           => $this->input('startDatetime', $this->input('start_datetime')),
            'end_datetime'             => $this->input('endDatetime', $this->input('end_datetime')),
            'passing_score'            => $this->input('passingScore', $this->input('passing_score')),
            'is_shuffled'              => $this->has('isShuffled') ? (bool) $this->input('isShuffled') : $this->input('is_shuffled'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * Hanya field yang dikirim (sometimes) yang divalidasi.
     */
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'description' => ['sometimes', 'nullable', 'string'],
            'exams_type' => ['required', 'in:' . implode(',', array_column(ExamType::cases(), 'value'))],
            'sub_classroom_subject_id' => [
                'sometimes',
                'required',
                'integer',
                Rule::exists('sub_classroom_subjects', 'id')->whereNull('deleted_at'),
            ],

            'start_datetime' => [
                'sometimes',
                'required',
                'date_format:Y-m-d H:i:s',
                'after_or_equal:' . now()->format('Y-m-d H:i:s'),
            ],
            'end_datetime' => [
                'sometimes',
                'required',
                'date_format:Y-m-d H:i:s',
                'after:start_datetime',
            ],

            'passing_score' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:100',
                'regex:/^\d+(\.\d{1,2})?$/',
            ],

            'is_shuffled' => ['sometimes', 'boolean'],
            'term_id'                  => ['sometimes', 'integer', 'exists:terms,id', new TermContainsDateRange('start_datetime', 'end_datetime')],

        ];
    }

    public function messages(): array
    {
        return [
            'term_id.exists' => 'The selected term does not exist or has been removed.',
            'exams_type' => 'exam type must be filled in',
            'start_datetime.after_or_equal' => 'The exam cannot start in the past.',
            'end_datetime.after'            => 'The exam end time must be after the start time.',
            'passing_score.regex'           => 'The passing score can have up to 2 decimal places.',
            'passing_score.max'             => 'The passing score cannot exceed 100.',
            'sub_classroom_subject_id.exists' => 'The selected subject does not exist or has been removed.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'The given data was invalid.',
            'errors'  => $validator->errors(),
        ], 422));
    }

    public function failedAuthorization()
    {
        throw new HttpResponseException(
            response()->json([
                'message' => 'You are not authorized to update this exam.',
                'details' => 'Only admin/superadmin or the assigned teacher can update this exam. If changing the class/subject, the target must also belong to you.',
                'success' => false,
            ], 403)
        );
    }
}
