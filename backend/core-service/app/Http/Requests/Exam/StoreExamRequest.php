<?php

namespace App\Http\Requests\Exam;

use App\Enums\ExamType;
use App\Rules\TermContainsDateRange;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class StoreExamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        if ($user->isAdminOrSuperUser()) {
            return true;
        }

        if (method_exists($user, 'hasRole') && $user->hasRole('teacher')) {
            $subClassroomSubjectId = (int) $this->input('sub_classroom_subject_id');

            return DB::table('sub_classroom_subjects')
                ->whereNull('deleted_at')
                ->where('id', $subClassroomSubjectId)
                ->where('teacher_user_id', $user->id)
                ->exists();
        }

        return false;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'exams_type' => ['required', 'in:' . implode(',', array_column(ExamType::cases(), 'value'))],
            'sub_classroom_subject_id' => [
                'required',
                'integer',
                Rule::exists('sub_classroom_subjects', 'id')->whereNull('deleted_at'),
            ],
            'start_datetime' => [
                'required',
                'date_format:Y-m-d H:i:s',
                'after_or_equal:' . now()->format('Y-m-d H:i:s'),
            ],
            'end_datetime' => [
                'required',
                'date_format:Y-m-d H:i:s',
                'after:start_datetime',
            ],
            'passing_score' => [
                'nullable',
                'numeric',
                'min:0',
                'max:100',
                'regex:/^\d+(\.\d{1,2})?$/'
            ],
            'is_shuffled' => ['boolean'],
            'term_id'                  => ['nullable', 'integer', 'exists:terms,id', new TermContainsDateRange('start_datetime', 'end_datetime')],

        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages()
    {
        return [
            'term_id.exists' => 'The selected term does not exist or has been removed.',
            'exams_type' => 'exam type must be filled in',
            'start_datetime.after_or_equal' => 'The exam cannot start in the past.',
            'end_datetime.after' => 'The exam end time must be after the start time.',
            'passing_score.regex' => 'The passing score can have up to 2 decimal places.',
            'passing_score.max' => 'The passing score cannot exceed 100.',
            'sub_classroom_subject_id.exists' => 'The selected subject does not exist or has been removed.',
        ];
    }

    /**
     * Custom error messages for validation errors.
     */
    protected function failedValidation(Validator $validator)
    {
        $response = response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422);

        throw new HttpResponseException($response);
    }
}
