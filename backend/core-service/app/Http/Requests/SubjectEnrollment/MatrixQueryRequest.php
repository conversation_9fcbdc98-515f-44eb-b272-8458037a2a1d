<?php

namespace App\Http\Requests\SubjectEnrollment;

use Illuminate\Foundation\Http\FormRequest;

class MatrixQueryRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        $term = $this->query('term_id', $this->input('term_id'));
        $subc = $this->query('sub_classroom_id', $this->input('sub_classroom_id'));

        if (is_array($term)) {
            $this->merge(['term_id' => head($term)]);
        }
        if (is_array($subc)) {
            $this->merge(['sub_classroom_id' => head($subc)]);
        }
    }

    public function rules(): array
    {
        return [
            'term_id'           => ['required', 'integer', 'exists:terms,id'],
            'sub_classroom_id'  => ['required', 'integer', 'exists:sub_classrooms,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'term_id.required'          => 'term_id is required.',
            'term_id.integer'           => 'term_id must be an integer.',
            'term_id.exists'            => 'term_id not found.',
            'sub_classroom_id.required' => 'sub_classroom_id is required.',
            'sub_classroom_id.integer'  => 'sub_classroom_id must be an integer.',
            'sub_classroom_id.exists'   => 'sub_classroom_id not found.',
        ];
    }
}
