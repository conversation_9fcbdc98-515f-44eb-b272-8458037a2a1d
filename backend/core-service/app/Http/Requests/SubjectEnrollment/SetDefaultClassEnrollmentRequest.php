<?php

namespace App\Http\Requests\SubjectEnrollment;

use Illuminate\Foundation\Http\FormRequest;

class SetDefaultClassEnrollmentRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'term_id' => 'required|integer|exists:terms,id',
            'default' => 'required|boolean',
            'note' => 'nullable|string|max:255',
        ];
    }
}
