<?php

namespace App\Http\Requests\SubjectEnrollment;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSingleEnrollmentRequest extends FormRequest
{
    public function authorize(): bool { return true; }

    protected function prepareForValidation(): void
    {
        $term   = $this->input('term_id', $this->input('termId'));
        $subj   = $this->input('sub_classroom_subject_id', $this->input('subClassroomSubjectId'));
        $user   = $this->input('user_id',
                    $this->input('userId',
                        $this->input('student_user_id', $this->input('studentUserId'))
                    ));

        $enroll = $this->input('is_enrolled', $this->input('isEnrolled'));

        $this->merge([
            'term_id'                  => is_numeric($term) ? (int) $term : 0,
            'sub_classroom_subject_id' => is_numeric($subj) ? (int) $subj : 0,
            'user_id'                  => is_numeric($user) ? (int) $user : 0,
            'is_enrolled'              => filter_var($enroll, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? (bool)$enroll,
        ]);
    }

    public function rules(): array
    {
        return [
            'term_id'                  => ['required','integer','exists:terms,id'],
            'sub_classroom_subject_id' => ['required','integer','exists:sub_classroom_subjects,id'],
            'user_id'                  => ['required','integer','exists:users,id'],
            'is_enrolled'              => ['required','boolean'],
        ];
    }
}
