<?php

namespace App\Http\Requests\SubjectEnrollment;

use Illuminate\Foundation\Http\FormRequest;

class ToggleAllRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'term_id' => 'required|integer|exists:terms,id',
            'is_enrolled' => 'required|boolean',
            'note' => 'nullable|string|max:255',
        ];
    }
}
