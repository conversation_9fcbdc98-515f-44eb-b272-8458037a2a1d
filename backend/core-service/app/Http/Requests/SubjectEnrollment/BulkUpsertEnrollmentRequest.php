<?php

namespace App\Http\Requests\Enrollment;

use Illuminate\Foundation\Http\FormRequest;

class BulkUpsertEnrollmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'term_id' => 'required|integer|exists:terms,id',
            'sub_classroom_id' => 'required|integer|exists:sub_classrooms,id',
            'rows' => 'required|array|min:1',
            'rows.*.sub_classroom_subject_id' => 'required|integer|exists:sub_classroom_subjects,id',
            'rows.*.student_user_id' => 'required|integer|exists:users,id',
            'rows.*.is_enrolled' => 'required|boolean',
            'rows.*.note' => 'nullable|string|max:255',
        ];
    }
}
