<?php

namespace App\Http\Requests\Assignment;

use App\Rules\TermContainsDate;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'school_id'        => 'nullable|integer|exists:schools,id',
            // 'academic_year_id' => 'nullable|integer|exists:academic_years,id', // sementara tidak dipakai logika
            // 'term_id'          => ['nullable','integer','exists:terms,id', new TermContainsDate('due_date')],
            'subject_id' => 'required|integer|exists:subjects,id',
            'sub_classroom_id' => 'required|integer|exists:sub_classrooms,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'due_date' => 'required|date',
            'file' => 'nullable|file|max:10248',
        ];
    }

    public function messages()
    {
        return [
            'school_id.required' => 'The school ID is required.',
            'school_id.exists' => 'The selected school does not exist.',
            'academic_year_id.required' => 'The academic year ID is required.',
            'academic_year_id.exists' => 'The selected academic year does not exist.',
            'term_id.required' => 'The term ID is required.',
            'term_id.exists' => 'The selected term does not exist.',
            'subject_id.required' => 'The subject ID is required.',
            'subject_id.exists' => 'The selected subject does not exist.',
            'sub_classroom_id.required' => 'The sub-classroom ID is required.',
            'sub_classroom_id.exists' => 'The selected sub-classroom does not exist.',
            'title.required' => 'The title is required.',
            'title.max' => 'The title may not be greater than 255 characters.',
            'description.max' => 'The description may not be greater than 500 characters.',
            'due_date.required' => 'The due date is required.',
            'due_date.date' => 'The due date is not a valid date.',
            'file.file' => 'The file must be a file.',
            'file.max' => 'The file may not be greater than 10248 kilobytes.',
        ];
    }

    /**
     * Handle failed validation.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422));
    }
}
