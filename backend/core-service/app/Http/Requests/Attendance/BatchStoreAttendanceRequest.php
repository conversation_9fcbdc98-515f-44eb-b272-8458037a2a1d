<?php

namespace App\Http\Requests\Attendance;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class BatchStoreAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasRole('teacher');
    }

    public function rules(): array
    {
        return [
            'schedule_id' => 'required|exists:schedules,id',
            'students' => 'required|array',
            'students.*.user_id' => 'required|integer|exists:users,id',
            'students.*.is_present' => 'required|boolean',
            'students.*.notes' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'schedule_id.required' => 'Schedule ID is required.',
            'schedule_id.exists' => 'Schedule ID must exist in the schedules table.',
            'students.required' => 'Students array is required.',
            'students.array' => 'Students must be an array.',
            'students.*.user_id.required' => 'User ID is required.',
            'students.*.user_id.integer' => 'User ID must be an integer.',
            'students.*.user_id.exists' => 'User ID must exist in the users table.',
            'students.*.is_present.required' => 'is_present is required.',
            'students.*.is_present.boolean' => 'is_present must be a boolean.',
            'students.*.notes.string' => 'Note must be a string.',
            'students.*.notes.max' => 'Note may not be greater than 255 characters.',
        ];
    }

    /**
     * Handle failed validation.
     *
     * @param Validator $validator
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'The given data was invalid.',
            'errors' => $validator->errors(),
        ], 422));
    }
}
