<?php

namespace App\Enums;

enum FinancialCoreReferenceType: string
{
    case TUITION = 'tuition';
    case CANTEEN = 'canteen';
    case CASHLESS = 'cashless';
    case OTHERS = 'others';

    public function label(): string
    {
        return match ($this) {
            self::TUITION => 'Tuition Payment',
            self::CANTEEN => 'Canteen Transaction',
            self::CASHLESS => 'NFC Card Transaction',
            self::OTHERS => 'Other Transaction',
        };
    }
}