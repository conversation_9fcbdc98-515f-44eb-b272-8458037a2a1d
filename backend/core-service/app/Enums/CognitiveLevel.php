<?php

namespace App\Enums;

enum CognitiveLevel: string
{
    case REMEMBERINGC1 = 'rememberingC1';
    case UNDERSTANDINGC2 = 'understandingC2';
    case APPLYINGC3 = 'applyingC3';
    case ANALYZINGC4 = 'analyzingC4';
    case EVALUATINGC5 = 'evaluatingC5';
    case CREATINGC6 = 'creatingC6';


    public function label(): string
    {
        return match ($this) {
            self::REMEMBERINGC1 => 'Mengingat - c1',
            self::UNDERSTANDINGC2 => 'Memahami - c2',
            self::APPLYINGC3 => 'Menerapkan - c3',
            self::ANALYZINGC4 => 'Menganalisis - c4',
            self::EVALUATINGC5 => 'Mengevaluasi - c5',
            self::CREATINGC6 => 'Mengkreasi - c6',

        };
    }
}
