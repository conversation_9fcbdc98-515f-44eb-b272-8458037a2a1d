<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class TuitionPayment extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The student that the tuition payment belongs to.
     *
     * @return BelongsTo
     */
    public function tuitionInvoices()
    {
        return $this->belongsToMany(TuitionInvoice::class, 'tuition_invoice_payments', 'tuition_payment_id', 'tuition_invoice_id');
    }

    /**
     * The user that the tuition payment belongs to.
     *
     * @return BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id', 'id');
    }
}
