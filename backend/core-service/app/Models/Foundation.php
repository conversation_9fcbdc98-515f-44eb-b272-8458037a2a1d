<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Foundation extends Model
{
    use SoftDeletes, Auditable;

    protected $fillable = [
        'name',
        'address',
        'registration_number',
        'year_founded',
        'phone_number',
        'email',
        'website',
        'logo',
    ];

    public function schools()
    {
        return $this->hasMany(School::class, 'foundation_id');
    }

    public function admins()
    {
        return $this->belongsToMany(User::class, 'foundation_admins', 'foundation_id', 'admin_user_id');
    }

    public function schoolCount()
    {
        return $this->schools->count();
    }

    public function subClassroomCount()
    {
        return $this->schools->map(function ($item, $key) {
            return $item->subClassroomCount();
        })->sum();
    }

    public function studentCount()
    {
        return $this->schools->map(function ($item, $key) {
            return $item->studentCount();
        })->sum();
    }

    public function teacherCount()
    {
        return $this->schools->map(function ($item, $key) {
            return $item->teacherCount();
        })->sum();
    }
}
