<?php

namespace App\Models;

use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class ClassAttendance extends Model
{
    use HasFactory, SoftDeletes, Multischoolable;


    protected $fillable = [
        'term_id',
        'student_user_id',
        'sick',
        'leave',
        'present',
        'alpha',
        'created_by',
        'updated_by',
        'deleted_by',
    ];


    protected $casts = [
        'sick' => 'int',
        'leave' => 'int',
        'present' => 'int',
        'alpha' => 'int'
    ];


    public function term()
    {
        return $this->belongsTo(Term::class);
    }
    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }
}
