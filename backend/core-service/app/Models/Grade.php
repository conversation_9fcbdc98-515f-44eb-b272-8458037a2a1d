<?php

namespace App\Models;

use App\Enums\GradeType;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Grade extends Model
{
    use Multischoolable;

    protected $casts = [
        'score' => 'decimal:2',
        'type' => GradeType::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'school_id',
        'term_id',
        'student_user_id',
        'assignment_id',
        'report_card_id',
        'exam_id',
        'type',
        'score',
        'notes',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }

    public function assignment()
    {
        return $this->belongsTo(Assignment::class, 'assignment_id');
    }

    public function reportCard()
    {
        return $this->belongsTo(ReportCard::class, 'report_card_id');
    }

    public function academicYear()
    {
        return $this->hasOneThrough(
            AcademicYear::class,
            Term::class,
            'id',
            'id',
            'term_id',
            'academic_year_id'
        );
    }
    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id');
    }
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }
}
