<?php

namespace App\Models;

use App\Enums\ExamType;
use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Exam extends Model
{
    use HasFactory, SoftDeletes, Auditable, Multischoolable;

    protected $fillable = [
        'title',
        'description',
        'exams_type',
        'sub_classroom_subject_id',
        'start_datetime',
        'end_datetime',
        'passing_score',
        'is_published',
        'is_shuffled',
        'created_by',
        'updated_by',

        'school_id',
        'term_id',
    ];

    protected $casts = [
        'exams_type'   => ExamType::class,
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'passing_score' => 'decimal:2',
        'is_published' => 'boolean',
        'is_shuffled' => 'boolean',
        'deleted_at' => 'datetime',
        'term_id' => 'integer',
    ];

    protected $attributes = [
        'is_published' => false,
        'is_shuffled' => false,
    ];

    // Relationships
    public function subClassroomSubject()
    {
        return $this->belongsTo(SubClassroomSubject::class, 'sub_classroom_subject_id');
    }

    public function questions(): HasMany
    {
        return $this->hasMany(ExamQuestion::class, 'exam_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function attempts(): HasMany
    {
        return $this->hasMany(ExamAttempt::class);
    }

    public function students()
    {
        return $this->hasManyThrough(User::class, ExamAttempt::class, 'exam_id', 'id', 'id', 'user_id');
    }

    public function academicYear()
    {
        return $this->hasOneThrough(
            AcademicYear::class,
            Term::class,
            'id',
            'id',
            'term_id',
            'academic_year_id'
        );
    }
    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class);
    }

    /** scope filter umum */
    public function scopeInTerm($q, $termId)
    {
        return $q->where('term_id', $termId);
    }

    /**
     * Scope filter tahun ajaran (opsional).
     */
    public function scopeYear($query, ?int $academicYearId)
    {
        return $academicYearId ? $query->where('academic_year_id', $academicYearId) : $query;
    }

    /**
     * Scope filter semester (opsional).
     */
    public function scopeSemester($query, ?string $semester)
    {
        return $semester ? $query->where('semester', $semester) : $query;
    }

    public function scopeFilterPeriod($query, ?int $academicYearId, ?string $semester)
    {
        if ($academicYearId) {
            $query->where('exams.academic_year_id', $academicYearId);
        }
        if ($semester) {
            $query->where('exams.semester', $semester);
        }
    }
}
