<?php

namespace App\Models;

use App\Enums\TuitionFeeType;
use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TuitionFee extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'type' => TuitionFeeType::class,
    ];

    public function isMonthly()
    {
        return $this->frequency === 'monthly';
    }

    public function isAnnual()
    {
        return $this->frequency === 'annual';
    }
}
