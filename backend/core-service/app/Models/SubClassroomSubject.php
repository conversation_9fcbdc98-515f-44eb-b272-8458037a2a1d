<?php

namespace App\Models;

use App\Scopes\SchoolScope;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Relations\Pivot; // <-- Praktik terbaik: extends Pivot
use Illuminate\Database\Eloquent\SoftDeletes;

class SubClassroomSubject extends Pivot
{
    // Menggunakan SoftDeletes dan Auditable di pivot model.
    use SoftDeletes, Multischoolable;

    // Definisikan tabel secara eksplisit karena kita extends Pivot.
    protected $table = 'sub_classroom_subjects';

    protected $guarded = ['id'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(new SchoolScope);

        /**
         * Event ini berjalan setiap kali relasi baru dibuat via sync() atau attach(),
         * dan akan secara otomatis mengisi school_id dari SubClassroom terkait.
         */
        static::creating(function (self $pivot) {
            if (is_null($pivot->school_id)) {
                $subClassroom = SubClassroom::find($pivot->sub_classroom_id);
                if ($subClassroom) {
                    // Sumber school_id sekarang sangat jelas, yaitu dari sub-kelasnya.
                    $pivot->school_id = $subClassroom->school_id;
                }
            }
        });
    }

    /**
     * Mendapatkan sub-kelas terkait.
     */
    public function subClassroom()
    {
        return $this->belongsTo(SubClassroom::class)->withTrashed();
    }

    /**
     * Mendapatkan mata pelajaran terkait.
     */
    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Mendapatkan guru pengampu mata pelajaran ini.
     */
    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_user_id', 'id');
    }

    public function subjectEnrollments()
    {
        return $this->hasMany(SubjectEnrollment::class);
    }
}
