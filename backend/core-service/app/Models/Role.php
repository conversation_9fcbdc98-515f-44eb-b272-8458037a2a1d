<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use Auditable;

    public const FOUNDATION_ADMIN = 'foundation_admin';
    public const SCHOOL_ADMIN = 'school_admin';
    public const TEACHER = 'teacher';
    public const HOMEROOM_TEACHER = 'homeroom_teacher';
    public const ROOM_GUARDIAN = 'room_guardian';
    public const STUDENT = 'student';
    public const PARENT = 'parent';
    public const CANTEEN_ADMIN = 'canteen_admin';
    public const CASHIER = 'cashier';
    public const HEADMASTER = 'headmaster';

    protected $fillable = ['name', 'created_by', 'updated_by'];

    public static function entries()
    {
        return [
            self::FOUNDATION_ADMIN,
            self::SCHOOL_ADMIN,
            self::TEACHER,
            self::STUDENT,
            self::PARENT,
            self::CANTEEN_ADMIN,
            self::CASHIER,
            self::HOMEROOM_TEACHER,
            self::RO<PERSON>_GUAR<PERSON>AN,
            self::HEADMASTER,
        ];
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_school_roles', 'role_id', 'user_id')
            ->withPivot('school_id')
            ->withTimestamps();
    }
}
