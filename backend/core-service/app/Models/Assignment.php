<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Assignment
 *
 * Represents an assignment in the system.
 *
 * @package App\Models
 * @property int $id
 * @property int $subject_id
 * @property int $sub_classroom_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Assignment extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'due_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the subject associated with the assignment.
     *
     * @return BelongsTo
     */
    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the sub-classroom associated with the assignment.
     *
     * @return BelongsTo
     */
    public function subClassroom()
    {
        return $this->belongsTo(SubClassroom::class);
    }


    /**
     * Get the submissions associated with the assignment.
     *
     * This method defines a one-to-many relationship between the Assignment
     * model and the AssignmentSubmission model.
     *
     * @return HasMany
     */
    public function submissions()
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    /**
     * Retrieve the teacher associated with the assignment's subject and sub-classroom.
     *
     * @return Teacher|null
     */
    public function getTeacher()
    {
        return SubClassroomSubject::where('sub_classroom_id', $this->sub_classroom_id)
            ->where('subject_id', $this->subject_id)
            ->first()
            ->teacher;
    }

    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class);
    }
}
