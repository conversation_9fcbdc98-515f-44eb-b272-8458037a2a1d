<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PracticalTrainingData extends Model
{
    use SoftDeletes, Auditable;

    protected $table = 'practical_training_data';
    protected $guarded = ['id'];

    protected $casts = [
        'practical_training_id' => 'integer',
        'final_score' => 'decimal:2',
        'created_by'       => 'integer',
        'updated_by'       => 'integer',
        'deleted_by'       => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function training()
    {
        return $this->belongsTo(PracticalTraining::class, 'practical_training_id');
    }
}
