<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubjectEnrollment extends Model
{
    protected $fillable = [
        'term_id',
        'sub_classroom_subject_id',
        'student_user_id',
        'is_enrolled',
        'enrolled_at',
        'unenrolled_at',
        'note',
    ];

    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class);
    }
    public function subClassroomSubject(): BelongsTo
    {
        return $this->belongsTo(SubClassroomSubject::class);
    }
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }
}
