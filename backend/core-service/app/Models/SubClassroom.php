<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class SubClassroom extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Mendapatkan Classroom induk.
     */
    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class)->withTrashed();
    }

    /**
     * Mendapatkan semua siswa di sub-kelas ini.
     */
    public function students(): HasMany
    {
        return $this->hasMany(User::class)->whereHas('roles', function ($query) {
            $query->where('name', Role::STUDENT);
        })->orderBy('name');
    }

    /**
     * Mendapatkan guru wali kelas (homeroom teacher).
     * Diubah menjadi relasi HasOne agar efisien dan bisa di-eager load.
     */
    public function teacher(): HasOne
    {
        return $this->hasOne(User::class)->whereHas('roles', function ($query) {
            $query->where('name', Role::TEACHER);
        });
    }

    /**
     * Relasi ke model pivot SubClassroomSubject.
     */
    public function subClassroomSubjects(): HasMany
    {
        return $this->hasMany(SubClassroomSubject::class);
    }

    /**
     * Mendapatkan semua mata pelajaran yang diajarkan di sub-kelas ini.
     */
    public function subjects(): BelongsToMany
    {
        return $this->belongsToMany(Subject::class, 'sub_classroom_subjects')
            ->withPivot('teacher_user_id')
            ->withTimestamps()
            ->using(SubClassroomSubject::class); // <-- Ini sudah benar!
    }

    /**
     * Accessor untuk mendapatkan nama lengkap sub-kelas.
     * Cara penggunaan: $subClassroom->full_name
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->classroom?->name . ' - ' . $this->sequence,
        );
    }
}
