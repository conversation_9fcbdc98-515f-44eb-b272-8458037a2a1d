<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * School Model
 *
 * Represents a school entity within the TIAS system.
 * Schools belong to foundations and have specific levels (TK, SD, SMP, SMA).
 *
 * @property int $id
 * @property int $school_level_id
 * @property string $name
 * @property string|null $address
 * @property string|null $registration_number
 * @property int|null $year_founded
 * @property string|null $phone_number
 * @property string|null $email
 * @property string|null $website
 * @property string|null $logo
 * @property int $foundation_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class School extends Model
{
    use SoftDeletes, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_level_id',
        'name',
        'address',
        'subdistrict',
        'district',
        'city',
        'province',
        'registration_number',
        'year_founded',
        'phone_number',
        'email',
        'website',
        'logo',
        'foundation_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'year_founded' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the school level that this school belongs to.
     *
     * @return BelongsTo<SchoolLevel, School>
     */
    public function schoolLevel(): BelongsTo
    {
        return $this->belongsTo(SchoolLevel::class);
    }

    /**
     * Get the foundation that owns this school.
     *
     * @return BelongsTo<Foundation, School>
     */
    public function foundation(): BelongsTo
    {
        return $this->belongsTo(Foundation::class);
    }

    /**
     * Get all subjects belonging to this school.
     *
     * @return HasMany<Subject>
     */
    public function subjects(): HasMany
    {
        return $this->hasMany(Subject::class);
    }

    /**
     * Get all programs belonging to this school.
     *
     * @return HasMany<Program>
     */
    public function programs(): HasMany
    {
        return $this->hasMany(Program::class);
    }

    /**
     * Get all extracurricular activities belonging to this school.
     *
     * @return HasMany<Extracurricular>
     */
    public function extracurriculars(): HasMany
    {
        return $this->hasMany(Extracurricular::class);
    }

    /**
     * Get all academic years belonging to this school.
     *
     * @return HasMany<AcademicYear>
     */
    public function academicYears(): HasMany
    {
        return $this->hasMany(AcademicYear::class);
    }

    /**
     * Get all classrooms belonging to this school.
     *
     * @return HasMany<Classroom>
     */
    public function classrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    /**
     * Get all schedule periods belonging to this school.
     *
     * @return HasMany<SchedulePeriod>
     */
    public function schedulePeriods(): HasMany
    {
        return $this->hasMany(SchedulePeriod::class);
    }

    /**
     * Get all sub-classrooms belonging to this school.
     *
     * @return HasMany<SubClassroom>
     */
    public function subClassrooms(): HasMany
    {
        return $this->hasMany(SubClassroom::class);
    }

    /**
     * Get the count of sub-classrooms in this school.
     *
     * @return int
     */
    public function subClassroomCount()
    {
        return $this->subClassrooms()->count();
    }

    /**
     * Get all users associated with this school through user_school_roles.
     * This is the base relationship that will be used by other relationships.
     *
     * @return BelongsToMany<User>
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_school_roles')
            ->select('users.*')
            ->distinct();
    }

    /**
     * Get all users with 'school_admin' role in this school.
     * This is a much better approach than the previous admins() method.
     *
     * @return BelongsToMany<User>
     */
    public function admins(): BelongsToMany
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', Role::SCHOOL_ADMIN);
        });
    }

    /**
     * Get all teachers in this school.
     *
     * @return BelongsToMany<User>
     */
    public function teachers(): BelongsToMany
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', Role::TEACHER);
        });
    }

    /**
     * Get the count of teachers in this school.
     *
     * @return int
     */
    public function teacherCount(): int
    {
        return $this->teachers()->count();
    }

    /**
     * Get all students in this school.
     *
     * @return BelongsToMany<User>
     */
    public function students(): BelongsToMany
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', Role::STUDENT);
        });
    }

    /**
     * Get the count of students in this school.
     *
     * @return int
     */
    public function studentCount(): int
    {
        return $this->students()->count();
    }

    /**
     * Get all parents in this school.
     *
     * @return BelongsToMany<User>
     */
    public function parents(): BelongsToMany
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', Role::PARENT);
        });
    }

    public function facilities(): HasMany
    {
        return $this->hasMany(Facility::class, 'school_id', 'id');
    }




    // ========================================
    // METHODS
    // ========================================

    /**
     * Assign a user as School Admin.
     *
     * @param User $user The user to assign as admin
     * @return void
     */
    public function assignAdmin(User $user): void
    {
        $this->assignUserRole($user, Role::SCHOOL_ADMIN);
    }

    /**
     * Generic helper method to assign a role to a user in this school.
     *
     * @param User $user The user to assign the role to
     * @param string $roleName Role name from Role model constants
     * @return void
     */
    public function assignUserRole(User $user, string $roleName): void
    {
        $role = Role::firstWhere('name', $roleName);

        if ($role) {
            $this->users()->attach($user->id, ['role_id' => $role->id]);
        }
    }

    /**
     * Assign a user as Headmaster.
     *
     * @param User $user The user to assign as headmaster
     * @return void
     */
    public function assignHeadmaster(User $user): void
    {
        $this->assignUserRole($user, Role::HEADMASTER);
    }

    /**
     * Get the headmaster of this school.
     *
     * @return User|null
     */
    public function headmaster()
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', Role::HEADMASTER);
        })->first();
    }
}
