<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Term extends Model
{
    use SoftDeletes, Auditable;

    protected $fillable = [
        'academic_year_id',
        'name',
        'order',
        'start_date',
        'end_date',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function scopeOfCurrentYear($q, ?int $schoolId = null)
    {
        return $q->whereHas('academicYear', fn($y) => $y->when($schoolId, fn($yy) => $yy->where('school_id', $schoolId))
            ->where('is_current', true));
    }

    public function scopeOfPreviousYear($q, ?int $schoolId = null)
    {
        $prevYear = AcademicYear::previous($schoolId)->first();
        return $q->when($prevYear, fn($qq) => $qq->where('academic_year_id', $prevYear->id), fn($qq) => $qq->whereRaw('1=0'));
    }

    public function exams(): HasMany
    {
        return $this->hasMany(Exam::class);
    }
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }
    public function reportCards(): HasMany
    {
        return $this->hasMany(ReportCard::class);
    }
    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class);
    }

    public function scopeContainingDate($q, $date)
    {
        return $q->whereDate('start_date', '<=', $date)->whereDate('end_date', '>=', $date);
    }

    public function scopeSemester($q, int $order)
    {
        return $q->where('order', $order);
    }

    public function subjectEnrollments()
    {
        return $this->hasMany(SubjectEnrollment::class);
    }

    public function scopeForSchool($q, ?int $schoolId)
    {
        return $q->when(
            $schoolId,
            fn($qq) =>
            $qq->whereHas('academicYear', fn($ay) => $ay->where('school_id', $schoolId))
        );
    }
}
