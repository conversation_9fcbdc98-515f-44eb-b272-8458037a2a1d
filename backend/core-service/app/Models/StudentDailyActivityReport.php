<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentDailyActivityReport extends Model
{
    use Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the student associated with the activity report.
     *
     * @return BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the activity associated with the activity report.
     *
     * @return BelongsTo
     */
    public function activity()
    {
        return $this->belongsTo(StudentDailyActivity::class);
    }
}
