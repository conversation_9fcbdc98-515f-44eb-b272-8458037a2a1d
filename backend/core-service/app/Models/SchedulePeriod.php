<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SchedulePeriod extends Model
{
    use Auditable, Multischoolable;

    protected $fillable = [
        'period',
        'start_time',
        'end_time',
        'school_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    public function schedule()
    {
        return $this->hasMany(Schedule::class);
    }
}
