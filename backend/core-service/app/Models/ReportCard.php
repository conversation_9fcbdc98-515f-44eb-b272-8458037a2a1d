<?php

namespace App\Models;

use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReportCard extends Model
{
    use Multischoolable;

    protected $fillable = [
        'term_id',
        'student_user_id',
        'sub_classroom_subject_id',
        'semester',
        'final_score',
        'revision_score',
        'grade',

        'mid_term_note',
        'mid_exported_at',
        'mid_locked_score',

        'final_term_note',
        'final_exported_at',
        'final_locked_score',

        'class_teacher_note',

        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'final_score' => 'float',
        'revision_score' => 'float',
        'is_enrolled' => 'boolean',
    ];

    protected $appends = ['display_score', 'is_mid_locked', 'is_final_locked'];


    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }

    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class);
    }
    public function academicYear()
    {
        return $this->hasOneThrough(
            AcademicYear::class,
            Term::class,
            'id',
            'id',
            'term_id',
            'academic_year_id'
        );
    }

    public function subClassroomSubject()
    {
        return $this->belongsTo(SubClassroomSubject::class, 'sub_classroom_subject_id');
    }

    public function grades()
    {
        return $this->hasMany(Grade::class, 'report_card_id');
    }

    public function scopeSubClassroom($query, $subClassroomId)
    {
        return $query->whereHas('subClassroomSubject', function ($q) use ($subClassroomId) {
            $q->where('sub_classroom_id', $subClassroomId);
        });
    }

    public function scopeSubject($query, $subjectId)
    {
        return $query->whereHas('subClassroomSubject', function ($q) use ($subjectId) {
            $q->where('subject_id', $subjectId);
        });
    }

    // Helpers
    public function scopeForPartition($q, int $ayId, int $studentId, int $scsId, string $semester)
    {
        return $q->where([
            'academic_year_id' => $ayId,
            'student_user_id' => $studentId,
            'sub_classroom_subject_id' => $scsId,
            'semester' => $semester,
        ]);
    }

    public function getIsMidLockedAttribute(): bool
    {
        return !empty($this->mid_exported_at);
    }

    public function getIsFinalLockedAttribute(): bool
    {
        return !empty($this->final_exported_at);
    }

    public function getDisplayScoreAttribute(): ?float
    {
        if ($this->is_final_locked) {
            return $this->final_locked_score;
        }
        if ($this->is_mid_locked) {
            return $this->mid_locked_score;
        }
        return $this->revision_score ?? $this->final_score ?? null;
    }
}
