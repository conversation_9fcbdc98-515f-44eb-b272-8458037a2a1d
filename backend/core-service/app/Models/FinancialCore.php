<?php

namespace App\Models;

use App\Enums\FinancialCoreReferenceType;
use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * FinancialCore Model
 *
 * Central financial record for all transactions in the system.
 * Records debit and credit entries.
 *
 * @property int $id
 * @property int|null $school_id
 * @property int|null $student_user_id
 * @property int|null $parent_user_id
 * @property string $transaction_number
 * @property string $reference_type
 * @property string|null $reference_id
 * @property float $debit_amount
 * @property float $credit_amount
 * @property string|null $notes
 * @property string $status
 * @property \Carbon\Carbon $transaction_date
 * @property string|null $transaction_time
 * @property string $transaction_timezone
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class FinancialCore extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'student_user_id',
        'parent_user_id',
        'transaction_number',
        'reference_type',
        'reference_id',
        'debit_amount',
        'credit_amount',
        'notes',
        'status',
        'transaction_date',
        'transaction_time',
        'transaction_timezone',
    ];

    /**
     * The attributes that should have default values.
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'debit_amount' => 0.00,
        'credit_amount' => 0.00,
        'status' => 'completed',
        'transaction_timezone' => 'Asia/Jakarta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reference_type' => FinancialCoreReferenceType::class,
        'debit_amount' => 'decimal:2',
        'credit_amount' => 'decimal:2',
        'transaction_date' => 'datetime:Y-m-d H:i:s.u',
        'transaction_time' => 'string',
        'transaction_timezone' => 'string',
        'created_at' => 'datetime:Y-m-d H:i:s.u',
        'updated_at' => 'datetime:Y-m-d H:i:s.u',
        'deleted_at' => 'datetime:Y-m-d H:i:s.u',
    ];

    /**
     * Get the student user associated with the financial record.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }

    /**
     * Get the parent user associated with the financial record.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_user_id');
    }

    /**
     * Get the school associated with the financial record.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Check if this is a debit transaction.
     */
    public function isDebit(): bool
    {
        return $this->debit_amount > 0;
    }

    /**
     * Check if this is a credit transaction.
     */
    public function isCredit(): bool
    {
        return $this->credit_amount > 0;
    }

    /**
     * Get the net amount (credit - debit).
     */
    public function getNetAmountAttribute(): int
    {
        return $this->credit_amount - $this->debit_amount;
    }

    /**
     * Get debit amount as formatted number.
     */
    public function getFormattedDebitAttribute(): float
    {
        return (float) $this->debit_amount;
    }

    /**
     * Get credit amount as formatted number.
     */
    public function getFormattedCreditAttribute(): float
    {
        return (float) $this->credit_amount;
    }




}
