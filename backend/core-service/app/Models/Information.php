<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Information extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $table = 'informations';
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The school that the information belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function targets()
    {
        return $this->hasMany(InformationTarget::class);
    }

    /**
     * The reads associated with the information.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reads()
    {
        return $this->hasMany(InformationRead::class);
    }

    /**
     * Check if the information has been read by a specific user.
     *
     * @param User $user
     */
    public function isReadByUser(User $user)
    {
        return $this->reads()->where('user_id', $user->id)->exists();
    }
    /**
     * Get the read timestamp for a specific user.
     *
     * @param User $user
     * @return \Illuminate\Support\Carbon|null
     */
    public function getReadAt(User $user)
    {
        $read = $this->reads()->where('user_id', $user->id)->first();
        return $read ? $read->read_at : null;
    }

    public function scopeVisibleFor($query, $user)
    {
        if (!$user) {
            return $query->published();
        }

        if ($user->is_superadmin) {
            return $query;
        }

        $roles = $user->roles->pluck('name')->toArray();

        if (
            in_array(Role::FOUNDATION_ADMIN, $roles) ||
            in_array(Role::SCHOOL_ADMIN, $roles)
        ) {
            return $query;
        }

        // sisanya (teacher, student, parent, cashier, dll) → hanya event publish
        return $query->published();
    }
}
