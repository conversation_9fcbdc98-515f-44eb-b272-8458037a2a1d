<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ExamAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'exam_id',
        'start_datetime',
        'submit_datetime',
        'score',
        'graded_at',
        'correct_count',
        'status',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'submit_datetime' => 'datetime',
        'graded_at' => 'datetime',
    ];
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function answers(): HasMany
    {
        return $this->hasMany(ExamAttemptAnswer::class, 'exam_attempt_id');
    }

    public function getQuestionsWithStatusAttribute()
    {
        // Ensure relations are loaded
        if (
            !$this->relationLoaded('exam') ||
            !$this->exam->relationLoaded('questions') ||
            !$this->relationLoaded('answers')
        ) {
            return null;
        }

        $questionsWithAnswers = $this->exam->questions->map(function ($question) {
            $answer = $this->answers->firstWhere('exam_question_id', $question->id);

            return [
                'question_id' => $question->id,
                'answer_id' => $answer?->id,
                'content' => $question->content,
                'answer_key_essay' => $question->answer_key_essay,
                'media_questions_url' => $question->media_questions ? asset('storage/' . $question->media_questions) : null,
                'order_index' => $answer?->order_index ?? $question->order_index ?? null,
                'is_answered' => $answer && ($answer->selected_option_id !== null || $answer->essay_answer !== null),
                'selected_option_id' => $answer?->selected_option_id,
                'essay_answer' => $answer?->essay_answer,
                'media_answer_url' => $answer?->media_answer ? asset('storage/' . $answer->media_answer) : null,

            ];
        });

        return $questionsWithAnswers
            ->sortBy('order_index')
            ->values();
    }
}
