<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcademicYear extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $fillable = [
        'name',
        'start_date',
        'end_date',
        'is_current',
        'school_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function terms()
    {
        return $this->hasMany(Term::class);
    }

    public function scopeForSchool($q, ?int $schoolId)
    {
        return $schoolId ? $q->where('school_id', $schoolId) : $q;
    }

    public function scopeCurrent($q)
    {
        return $q->where('is_current', true);
    }

    /** Tahun ajaran sebelum current (berdasarkan tanggal) */
    public function scopePrevious($q, ?int $schoolId = null)
    {
        $current = static::forSchool($schoolId)->current()->first();
        if (!$current) {
            $today = now()->toDateString();
            $current = static::forSchool($schoolId)
                ->whereDate('start_date', '<=', $today)
                ->whereDate('end_date', '>=', $today)
                ->orderByDesc('end_date')
                ->first();
        }

        return static::query()
            ->forSchool($schoolId)
            ->whereDate('end_date', '<', optional($current)->start_date ?? now()->toDateString())
            ->orderByDesc('end_date');
    }
}
