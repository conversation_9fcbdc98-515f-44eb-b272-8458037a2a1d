<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExtracurricularStudent extends Model
{
    use SoftDeletes, Auditable;

    protected $table = 'extracurricular_students';
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function extracurricular()
    {
        return $this->belongsTo(Extracurricular::class);
    }
    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }
    public function term()
    {
        return $this->belongsTo(Term::class);
    }
}
