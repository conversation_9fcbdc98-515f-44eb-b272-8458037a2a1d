<?php

namespace App\Models;

use App\Enums\ProgramSourceType;
use App\Enums\ProgramType;
use App\Enums\SemesterType;
use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Program extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',

        'type' => ProgramType::class,
        'semester' => SemesterType::class,
        'source_type' => ProgramSourceType::class,
    ];

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    public function parent()
    {
        return $this->belongsTo($this::class, 'parent_id');
    }

    public function subTopics()
    {
        return $this->hasMany($this::class, 'parent_id');
    }

    protected static function booted()
    {
        static::saving(function ($program) {
            if ($program->start_date && $program->end_date) {
                $start = Carbon::parse($program->start_date);
                $end = Carbon::parse($program->end_date);

                // Perhitungan dibulatkan ke atas
                $program->duration_weeks = ceil($start->diffInDays($end) / 7);
            }
        });
    }

    public function lessonPlan()
    {
        return $this->hasOne(LessonPlan::class);
    }
}
