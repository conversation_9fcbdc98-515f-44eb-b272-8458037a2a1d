<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class PracticalTraining extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    protected $casts = [
        'term_id'          => 'integer',
        'student_user_id'  => 'integer',
        'sick'             => 'integer',
        'leave'            => 'integer',
        'present'          => 'integer',
        'alpha'            => 'integer',
        'created_by'       => 'integer',
        'updated_by'       => 'integer',
        'deleted_by'       => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function term()
    {
        return $this->belongsTo(Term::class);
    }

    public function student()
    {
        return $this->belongsTo(User::class, 'student_user_id');
    }

    public function data()
    {
        return $this->hasMany(PracticalTrainingData::class, 'practical_training_id');
    }

    public function getPeriodRangeTextAttribute(): string
    {
        $start = (string) ($this->period_start ?? '');
        $end   = (string) ($this->period_end ?? '');
        return trim($start) && trim($end) ? "{$start} — {$end}" : trim($start . ' ' . $end);
    }
}
