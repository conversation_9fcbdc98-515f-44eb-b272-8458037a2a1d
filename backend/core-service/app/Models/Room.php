<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Room extends Model
{
    use Auditable, Multischoolable, SoftDeletes;

    protected $fillable = [
        'school_id',
        'dormitory_id',
        'guardian_user_id',
        'name',
    ];

    public function dormitory()
    {
        return $this->belongsTo(Dormitory::class);
    }

    public function guardian()
    {
        return $this->belongsTo(User::class, 'guardian_user_id');
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }
}
