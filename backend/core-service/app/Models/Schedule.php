<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Schedule extends Model
{
    use Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The schedule belongs to a classroom.
     *
     * @return BelongsTo
     */
    public function subClassroomSubject()
    {
        return $this->belongsTo(SubClassroomSubject::class)->withTrashed();
    }

    /**
     * The schedule belongs to a classroom.
     *
     * @return BelongsTo
     */
    public function schedulePeriod()
    {
        return $this->belongsTo(SchedulePeriod::class);
    }

    /**
     * The schedule belongs to a classroom.
     *
     * @return BelongsTo
     */
    public function getLatestAttendance(int $userId, ?string $date = null)
    {
        return $this->attendances()->where('user_id', $userId)
            ->where('checked_in_at', '>=', now()->startOfDay())
            ->where('checked_in_at', '<=', now()->endOfDay())
            ->when($date, function ($query) use ($date) {
                $query->whereDate('checked_in_at', $date);
            })
            ->latest()->first();
    }

    /**
     * The schedule belongs to a classroom.
     *
     * @return BelongsTo
     */
    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }
    /**
     * The schedule belongs to a classroom.
     *
     * @return BelongsTo
     */
    public function scheduleActivities()
    {
        return $this->hasMany(ScheduleActivity::class);
    }
}
