<?php

namespace App\Models;

use App\Http\Resources\UserResource;
use Database\Factories\UserFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;
use App\Notifications\ResetPasswordNotificationCustom;
use App\Traits\Auditable;
use Illuminate\Support\Facades\Log;

/**
 * The User model represents a user in the application.
 * It extends the Authenticatable class and implements the JWTSubject interface
 * to support JSON Web Token (JWT) authentication.
 */
class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<UserFactory> */
    use HasFactory, Notifiable, SoftDeletes, Auditable;

    /**
     * The attributes that are guarded from mass assignment.
     *
     * @var array<string> The guarded attributes.
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string> The attributes to hide.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the identifier that will be stored in the JWT.
     *
     * @return mixed The primary key of the user.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key-value array, containing any custom claims.
     *
     * @return array<string, mixed> An array of custom claims for the JWT.
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Retrieve the detailed information of the user based on their role.
     *
     * @return mixed The detailed information of the user.
     */
    public function detail()
    {
        return $this;
    }

    /**
     * Get the profile resource of the user.
     *
     * @return UserResource
     * The profile resource of the user.
     */
    public function profile()
    {
        return new UserResource($this);
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function sendPasswordResetNotification($token)
    {
        $url = config('app.frontend_url') . '/reset-password?token=' . $token . '&email=' . $this->email;

        $this->notify(new ResetPasswordNotificationCustom($url));
    }

    /**
     * Get the sub-classroom associated with the student.
     *
     * @return BelongsTo
     */
    public function subClassroom()
    {
        return $this->belongsTo(SubClassroom::class);
    }

    /**
     * Get the activity reports associated with the student.
     *
     * @return HasMany
     */
    public function extracurriculars()
    {
        return $this->belongsToMany(Extracurricular::class, 'extracurricular_students', 'user_id', 'extracurricular_id');
    }

    /**
     * Accessor for the sub-classroom name.
     *
     * Combines the classroom name and sequence of the sub-classroom.
     *
     * @return string|null
     */
    public function getSubClassroomName()
    {
        if ($this->subClassroom) {
            return $this->subClassroom->classroom->name . ' - ' . $this->subClassroom->sequence;
        }
        return null;
    }

    /**
     * Get all assignments for the student's sub-classroom.
     *
     * @return Collection
     */
    public function getAssignments()
    {
        return Assignment::where('sub_classroom_id', $this->sub_classroom_id)->get();
    }

    /**
     * Get all daily activity reports for the student.
     *
     * @return Collection
     */
    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function parent()
    {
        return $this->belongsToMany(User::class, 'student_parents', 'student_user_id', 'parent_user_id');
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function children()
    {
        // filter by active_school_id
        return $this->belongsToMany(User::class, 'student_parents', 'parent_user_id', 'student_user_id')->whereHas('schools', function ($query) {
            $query->where('school_id', session('active_school_id'));
        });
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function canteenAdmin()
    {
        return $this->hasOne(CanteenAdmin::class);
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function cashier()
    {
        return $this->hasOne(Cashier::class);
    }

    /**
     * Get the user associated with the student.
     *
     * @return BelongsTo
     */
    public function assignRole(string $role)
    {
        $roleModel = Role::where('name', $role)->first();

        // Check if user already has the role
        if (!$roleModel) {
            throw new \InvalidArgumentException("Role '{$role}' not found.");
        }

        if ($roleModel) {
            $this->roles()->attach($roleModel->id);
        }
    }

    /**
     * Assign a school to the user.
     *
     * @param School $school The school to assign.
     * @return void
     */
    public function assignSchoolRole(School $school, string $role)
    {
        $roleModel = Role::where('name', $role)->first();

        // Check if role exists
        if (!$roleModel) {
            throw new \InvalidArgumentException("Role '{$role}' not found.");
        }

        // Check if user already has the role
        if ($this->hasRole($role) && $this->schools->contains($school)) {
            return;
        }

        if ($roleModel) {
            $this->roles()->attach($roleModel->id, ['school_id' => $school->id]);
            
            // Log for debugging
            \Log::info("Assigned role {$role} to user {$this->id} for school {$school->id}");
        }
    }

    /**
     * Assign a parent to the user.
     *
     * @param User $parent The parent user to assign.
     * @return void
     */
    public function assignParent(User $parent)
    {
        $this->parent()->attach($parent->id);
    }

    /**
     * Remove a role from the user.
     *
     * @param string $role The name of the role to remove.
     * @return void
     */
    public function removeRole(string $role)
    {
        $roleModel = Role::where('name', $role)->first();
        if ($roleModel) {
            $this->roles()->detach($roleModel->id);
        }
    }

    public function isAdminOrSuperUser(): bool
    {
        return method_exists($this, 'hasRole')
            ? ($this->hasRole('school_admin') || $this->hasRole('superadmin') || $this->hasRole('foundation_admin'))
            : false;
    }

    /**
     * Check if the this has a specific role.
     *
     * @param string $role The name of the role to check.
     * @return bool True if the user has the role, false otherwise.
     */
    public function hasRole(string $role): bool
    {
        return $this->roles()->where('name', $role)->exists();
    }

    /**
     * Get the roles associated with the user.
     *
     * @return BelongsToMany
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_school_roles', 'user_id', 'role_id')
            ->select('roles.*')
            ->distinct();
    }

    /**
     * Get the schools associated with the user.
     *
     * @return BelongsToMany
     */
    public function schools()
    {
        return $this->belongsToMany(School::class, 'user_school_roles', 'user_id', 'school_id')
            ->select('schools.*')
            ->distinct();
    }

    /**
     * Get the foundations associated with the user.
     *
     * @return BelongsToMany
     */
    public function foundations()
    {
        return $this->belongsToMany(Foundation::class, 'foundation_admins', 'admin_user_id', 'foundation_id')
            ->withTimestamps()
            ->distinct();
    }

    /**
     * Get the roles associated with the user in a specific school.
     *
     * @return BelongsToMany
     */
    public function getRoleInSchool(School $school)
    {
        return $this->roles()->wherePivot('school_id', $school->id)->get();
    }

    /**
     * Scope a query to only include active students.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActiveStudents($query)
    {
        return $query->whereHas('roles', function ($query) {
            $query->where('name', Role::STUDENT);
        })->where('is_active', true);
    }

    /**
     * Scope a query to only include active teachers.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActiveTeachers($query)
    {
        return $query->whereHas('roles', function ($query) {
            $query->where('name', Role::TEACHER);
        })->where('is_active', true);
    }

    /**
     * Scope a query to get users by their role.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $role The role to filter by.
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGetByRole($query, $role)
    {
        return $query->whereHas('roles', function ($query) use ($role) {
            $query->where('name', $role);
        });
    }

    /**
     * Get the subjects associated with the teacher.
     *
     * @return BelongsToMany
     */
    public function getSubjects()
    {
        return $this->belongsToMany(Subject::class, 'sub_classroom_subjects', 'teacher_user_id', 'subject_id');
    }

    /**
     * Scope a query to check if the user has a specific role.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $role The role to check.
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHasRole($query, $role)
    {
        return $query->whereHas('roles', function ($query) use ($role) {
            $query->where('name', $role);
        });
    }

    /**
     * Get the NFC cards associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function nfcCards()
    {
        return $this->hasMany(NfcCard::class);
    }

    /**
     * Get the information reads associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function informationReads()
    {
        return $this->hasMany(InformationRead::class);
    }

    /**
     * Get the default password for admin view.
     * Only accessible by admin users.
     *
     * @return string|null
     */
    public function getDefaultPasswordForAdmin()
    {
        return $this->default_password;
    }

    /**
     * Clear the default password after user changes their password.
     *
     * @return void
     */
    public function clearDefaultPassword()
    {
        $this->default_password = null;
        $this->save();
    }

    public function subjectEnrollments()
    {
        return $this->hasMany(SubjectEnrollment::class, 'student_user_id');
    }
}
