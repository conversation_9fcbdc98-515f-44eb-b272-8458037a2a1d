<?php

namespace App\Models;

use App\Enums\CognitiveLevel;
use App\Enums\QuestionType;
use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamQuestion extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'exam_id',
        'content',
        'answer_key_essay',
        'points',
        'question_type',
        'order_index',
        'media_questions',
        'kd_number',
        'learning_outcome',
        'competency_indicator',
        'level_kognitif'
    ];

    protected $casts = [
        'points' => 'decimal:2',
        'question_type'   => QuestionType::class,
        'level_kognitif'  => CognitiveLevel::class,
        'order_index' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function exam()
    {
        return $this->belongsTo(Exam::class, 'exam_id');
    }

    public function answerOptions(): HasMany
    {
        return $this->hasMany(ExamQuestionHasAnswerOption::class, 'exam_question_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function deleter()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order_index');
    }

    public function hasImage()
    {
        return !is_null($this->image);
    }
}
