<?php

namespace App\Models;

use App\Enums\PaymentStatus;
use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class TuitionInvoice extends Model
{
    use SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'status' => PaymentStatus::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the student associated with the tuition invoice.
     *
     * @return BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the tuition fee associated with the tuition invoice.
     *
     * @return BelongsTo
     */
    public function tuitionFee()
    {
        return $this->belongsTo(TuitionFee::class);
    }

    /**
     * Get the tuition payments associated with the tuition invoice.
     *
     * @return HasMany
     */
    public function tuitionPayments()
    {
        return $this->belongsToMany(TuitionPayment::class, 'tuition_invoice_payments', 'tuition_invoice_id', 'tuition_payment_id');
    }
}
