<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Multischoolable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Event extends Model
{
    use HasFactory, SoftDeletes, Auditable, Multischoolable;

    protected $guarded = ['id'];

    protected $casts = [
        'event_date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_published' => 'boolean',
    ];

    public function files(): HasMany
    {
        return $this->hasMany(EventFile::class)->withTrashed();
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeVisibleFor($query, $user)
    {

        if ($user->is_superadmin) {
            return $query;
        }

        $roles = $user->roles->pluck('name')->toArray();

        if (
            in_array(Role::FOUNDATION_ADMIN, $roles) ||
            in_array(Role::SCHOOL_ADMIN, $roles)
        ) {
            return $query;
        }

        return $query->published();
    }
}
