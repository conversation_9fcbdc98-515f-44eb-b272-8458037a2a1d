<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubClassroomSubjectHasStudent extends Model
{
    use SoftDeletes, Auditable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sub_classroom_subject_has_students';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'term_id',
        'sub_classroom_subject_id',
        'user_id',
        'is_enrolled',
        'enrolled_at',
        'unenrolled_at',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'is_enrolled' => 'boolean',
        'enrolled_at' => 'datetime',
        'unenrolled_at' => 'datetime',
    ];

    /**
     * Get the sub_classroom_subject that owns the enrollment.
     *
     * @return BelongsTo
     */
    public function subClassroomSubject(): BelongsTo
    {
        return $this->belongsTo(SubClassroomSubject::class, 'sub_classroom_subject_id');
    }

    /**
     * Get the student user that owns the enrollment.
     *
     * @return BelongsTo
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id');
    }
}
