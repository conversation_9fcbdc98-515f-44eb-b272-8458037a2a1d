<?php

namespace App\Providers;

use App\Repositories\AcademicYear\AcademicYearRepository;
use App\Repositories\AcademicYear\AcademicYearRepositoryImpl;
use App\Repositories\Article\ArticleRepository;
use App\Repositories\Article\ArticleRepositoryImpl;
use App\Repositories\Assignment\AssignmentRepository;
use App\Repositories\Assignment\AssignmentRepositoryImpl;
use App\Repositories\AssignmentSubmission\AssignmentSubmissionRepository;
use App\Repositories\AssignmentSubmission\AssignmentSubmissionRepositoryImpl;
use App\Repositories\Canteen\CanteenRepository;
use App\Repositories\Canteen\CanteenRepositoryImpl;
use App\Repositories\CanteenAdmin\CanteenAdminRepository;
use App\Repositories\CanteenAdmin\CanteenAdminRepositoryImpl;
use App\Repositories\Cashier\CashierRepository;
use App\Repositories\Cashier\CashierRepositoryImpl;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\Category\CategoryRepositoryImpl;
use App\Repositories\ClassEnrollment\ClassEnrollmentRepository;
use App\Repositories\ClassEnrollment\ClassEnrollmentRepositoryImpl;
use App\Repositories\Classroom\ClassroomRepository;
use App\Repositories\Classroom\ClassroomRepositoryImpl;
use App\Repositories\Document\DocumentRepository;
use App\Repositories\Document\DocumentRepositoryImpl;
use App\Repositories\Event\EventRepository;
use App\Repositories\Event\EventRepositoryImpl;
use App\Repositories\Exam\ExamRepository;
use App\Repositories\Exam\ExamRepositoryImpl;
use App\Repositories\ExamAttempt\ExamAttemptRepository;
use App\Repositories\ExamAttempt\ExamAttemptRepositoryImpl;
use App\Repositories\ExamQuestion\ExamQuestionRepository;
use App\Repositories\ExamQuestion\ExamQuestionRepositoryImpl;
use App\Repositories\ExamToken\ExamTokenRepository;
use App\Repositories\ExamToken\ExamTokenRepositoryImpl;
use App\Repositories\Facility\FacilityRepository;
use App\Repositories\Facility\FacilityRepositoryImpl;
use App\Repositories\Grade\GradeRepository;
use App\Repositories\Grade\GradeRepositoryImpl;
use App\Repositories\Information\InformationRepository;
use App\Repositories\Information\InformationRepositoryImpl;
use App\Repositories\LessonPlan\LessonPlanRepository;
use App\Repositories\LessonPlan\LessonPlanRepositoryImpl;
use App\Repositories\Notification\NotificationRepository;
use App\Repositories\Notification\NotificationRepositoryImpl;
use App\Repositories\Organization\OrganizationRepository;
use App\Repositories\Organization\OrganizationRepositoryImpl;
use App\Repositories\Position\PositionRepository;
use App\Repositories\Position\PositionRepositoryImpl;
use App\Repositories\Product\ProductRepository;
use App\Repositories\Product\ProductRepositoryImpl;
use App\Repositories\Program\ProgramRepository;
use App\Repositories\Program\ProgramRepositoryImpl;
use App\Repositories\Purchase\PurchaseRepository;
use App\Repositories\Purchase\PurchaseRepositoryImpl;
use App\Repositories\PurchaseLog\PurchaseLogRepository;
use App\Repositories\PurchaseLog\PurchaseLogRepositoryImpl;
use App\Repositories\Schedule\ScheduleRepository;
use App\Repositories\Schedule\ScheduleRepositoryImpl;
use App\Repositories\ScheduleActivity\ScheduleActivityRepository;
use App\Repositories\ScheduleActivity\ScheduleActivityRepositoryImpl;
use App\Repositories\SchedulePeriod\SchedulePeriodRepository;
use App\Repositories\SchedulePeriod\SchedulePeriodRepositoryImpl;
use App\Repositories\StudentActivityReport\StudentActivityReportRepository;
use App\Repositories\StudentActivityReport\StudentActivityReportRepositoryImpl;
use App\Repositories\StudentDailyActivity\StudentDailyActivityRepository;
use App\Repositories\StudentDailyActivity\StudentDailyActivityRepositoryImpl;
use App\Repositories\StudentDailyActivityReport\StudentDailyActivityReportRepository;
use App\Repositories\StudentDailyActivityReport\StudentDailyActivityReportRepositoryImpl;
use App\Repositories\SubClassroom\SubClassroomRepository;
use App\Repositories\SubClassroom\SubClassroomRepositoryImpl;
use App\Repositories\Subject\SubjectRepository;
use App\Repositories\Subject\SubjectRepositoryImpl;
use App\Repositories\SubjectEnrollment\SubjectEnrollmentRepository;
use App\Repositories\SubjectEnrollment\SubjectEnrollmentRepositoryImpl;
use App\Repositories\Tag\TagRepository;
use App\Repositories\Tag\TagRepositoryImpl;
use App\Repositories\Term\TermRepository;
use App\Repositories\Term\TermRepositoryImpl;
use App\Repositories\Transaction\TransactionRepository;
use App\Repositories\Transaction\TransactionRepositoryImpl;
use App\Repositories\TransactionItem\TransactionItemRepository;
use App\Repositories\TransactionItem\TransactionItemRepositoryImpl;
use App\Repositories\TuitionFee\TuitionFeeRepository;
use App\Repositories\TuitionFee\TuitionFeeRepositoryImpl;
use App\Repositories\TuitionInvoice\TuitionInvoiceRepository;
use App\Repositories\TuitionInvoice\TuitionInvoiceRepositoryImpl;
use App\Repositories\TuitionPayment\TuitionPaymentRepository;
use App\Repositories\TuitionPayment\TuitionPaymentRepositoryImpl;
use Carbon\Carbon;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(ArticleRepository::class, ArticleRepositoryImpl::class);
        $this->app->bind(ClassroomRepository::class, ClassroomRepositoryImpl::class);
        $this->app->bind(SchedulePeriodRepository::class, SchedulePeriodRepositoryImpl::class);
        $this->app->bind(AcademicYearRepository::class, AcademicYearRepositoryImpl::class);
        $this->app->bind(SubjectRepository::class, SubjectRepositoryImpl::class);
        $this->app->bind(AssignmentRepository::class, AssignmentRepositoryImpl::class);
        $this->app->bind(NotificationRepository::class, NotificationRepositoryImpl::class);
        $this->app->bind(TagRepository::class, TagRepositoryImpl::class);
        $this->app->bind(AssignmentSubmissionRepository::class, AssignmentSubmissionRepositoryImpl::class);
        $this->app->bind(OrganizationRepository::class, OrganizationRepositoryImpl::class);
        $this->app->bind(StudentDailyActivityRepository::class, StudentDailyActivityRepositoryImpl::class);
        $this->app->bind(InformationRepository::class, InformationRepositoryImpl::class);
        $this->app->bind(PositionRepository::class, PositionRepositoryImpl::class);
        $this->app->bind(StudentDailyActivityReportRepository::class, StudentDailyActivityReportRepositoryImpl::class);
        $this->app->bind(CategoryRepository::class, CategoryRepositoryImpl::class);
        $this->app->bind(ScheduleRepository::class, ScheduleRepositoryImpl::class);
        $this->app->bind(SubClassroomRepository::class, SubClassroomRepositoryImpl::class);
        $this->app->bind(TuitionFeeRepository::class, TuitionFeeRepositoryImpl::class);
        $this->app->bind(TuitionInvoiceRepository::class, TuitionInvoiceRepositoryImpl::class);
        $this->app->bind(TuitionPaymentRepository::class, TuitionPaymentRepositoryImpl::class);
        $this->app->bind(StudentActivityReportRepository::class, StudentActivityReportRepositoryImpl::class);
        $this->app->bind(FacilityRepository::class, FacilityRepositoryImpl::class);
        $this->app->bind(CanteenRepository::class, CanteenRepositoryImpl::class);
        $this->app->bind(CanteenAdminRepository::class, CanteenAdminRepositoryImpl::class);
        $this->app->bind(CashierRepository::class, CashierRepositoryImpl::class);
        $this->app->bind(ProductRepository::class, ProductRepositoryImpl::class);
        $this->app->bind(PurchaseRepository::class, PurchaseRepositoryImpl::class);
        $this->app->bind(PurchaseLogRepository::class, PurchaseLogRepositoryImpl::class);
        $this->app->bind(TransactionRepository::class, TransactionRepositoryImpl::class);
        $this->app->bind(TransactionItemRepository::class, TransactionItemRepositoryImpl::class);
        $this->app->bind(ScheduleActivityRepository::class, ScheduleActivityRepositoryImpl::class);
        $this->app->bind(EventRepository::class, EventRepositoryImpl::class);
        $this->app->bind(DocumentRepository::class, DocumentRepositoryImpl::class);
        $this->app->bind(ProgramRepository::class, ProgramRepositoryImpl::class);
        $this->app->bind(LessonPlanRepository::class, LessonPlanRepositoryImpl::class);
        $this->app->bind(ExamRepository::class, ExamRepositoryImpl::class);
        $this->app->bind(ExamQuestionRepository::class, ExamQuestionRepositoryImpl::class);
        $this->app->bind(ExamAttemptRepository::class, ExamAttemptRepositoryImpl::class);
        $this->app->bind(GradeRepository::class, GradeRepositoryImpl::class);
        $this->app->bind(ExamTokenRepository::class, ExamTokenRepositoryImpl::class);
        $this->app->bind(TermRepository::class, TermRepositoryImpl::class);
        $this->app->bind(SubjectEnrollmentRepository::class, SubjectEnrollmentRepositoryImpl::class);
        $this->app->bind(ClassEnrollmentRepository::class, ClassEnrollmentRepositoryImpl::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('production')) {
            URL::forceScheme('https');
        }
        date_default_timezone_set('Asia/Jakarta');
        Carbon::setLocale('id');

        // Configure Scramble for JWT Bearer Authentication
        Scramble::afterOpenApiGenerated(function (OpenApi $openApi) {
            $openApi->secure(
                SecurityScheme::http('bearer', 'JWT')
            );
        });
    }
}
