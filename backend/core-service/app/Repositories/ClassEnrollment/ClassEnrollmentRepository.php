<?php

namespace App\Repositories\ClassEnrollment;

use App\Models\SubClassroom;

interface ClassEnrollmentRepository
{
    public function summary(int $termId, SubClassroom $subClassroom): array;
    public function syncMatrix(int $termId, SubClassroom $subClassroom): int;
    public function setDefault(int $termId, SubClassroom $subClassroom, bool $isEnrolled, ?string $note = null): int;
    public function enrollAllForSubject(int $termId, SubClassroom $subClassroom, int $subClassroomSubjectId, bool $isEnrolled, ?string $note = null): int;
    public function enrollAllForStudent(int $termId, SubClassroom $subClassroom, int $studentUserId, bool $isEnrolled, ?string $note = null): int;
}
