<?php
// app/Repositories/ClassEnrollment/ClassEnrollmentRepositoryImpl.php

namespace App\Repositories\ClassEnrollment;

use App\Models\SubClassroom;
use App\Models\SubClassroomSubject;
use App\Models\SubClassroomSubjectHasStudent;
use App\Models\User;
use Carbon\Carbon;

class ClassEnrollmentRepositoryImpl implements ClassEnrollmentRepository
{
    public function summary(int $termId, SubClassroom $subClassroom): array
    {
        $students = User::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $subjects = SubClassroomSubject::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $total = $students->count() * $subjects->count();

        $counts = SubClassroomSubjectHasStudent::query()
            ->selectRaw('COUNT(*) FILTER (WHERE is_enrolled = true) as total_enrolled, COUNT(*) as total_rows')
            ->where('term_id', $termId)
            ->whereIn('sub_classroom_subject_id', $subjects)
            ->whereIn('user_id', $students)
            ->first();

        return [
            'total_combinations' => $total,
            'existing_rows'      => (int)($counts->total_rows ?? 0),
            'enrolled_checked'   => (int)($counts->total_enrolled ?? 0),
        ];
    }

    public function syncMatrix(int $termId, SubClassroom $subClassroom): int
    {
        $now = Carbon::now();

        $students = User::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $subjects = SubClassroomSubject::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $payloads = [];
        foreach ($students as $stu) foreach ($subjects as $subj) {
            $payloads[] = [
                'term_id' => $termId,
                'sub_classroom_subject_id' => $subj,
                'user_id' => $stu,
                'is_enrolled' => false,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        SubClassroomSubjectHasStudent::upsert(
            $payloads,
            ['term_id', 'sub_classroom_subject_id', 'user_id'],
            [] // biarkan yang sudah ada tetap (tidak overwrite)
        );

        return count($payloads);
    }

    public function setDefault(int $termId, SubClassroom $subClassroom, bool $isEnrolled, ?string $note = null): int
    {
        $now = Carbon::now();

        $students = User::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $subjects = SubClassroomSubject::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $payloads = [];
        foreach ($students as $stu) foreach ($subjects as $subj) {
            $payloads[] = [
                'term_id' => $termId,
                'sub_classroom_subject_id' => $subj,
                'user_id' => $stu,
                'is_enrolled' => $isEnrolled,
                'enrolled_at' => $isEnrolled ? $now : null,
                'unenrolled_at' => $isEnrolled ? null : $now,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        SubClassroomSubjectHasStudent::upsert(
            $payloads,
            ['term_id', 'sub_classroom_subject_id', 'user_id'],
            ['is_enrolled', 'enrolled_at', 'unenrolled_at', 'updated_at']
        );

        return count($payloads);
    }

    public function enrollAllForSubject(int $termId, SubClassroom $subClassroom, int $subClassroomSubjectId, bool $isEnrolled, ?string $note = null): int
    {
        $now = Carbon::now();
        $students = User::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $payloads = [];
        foreach ($students as $stu) {
            $payloads[] = [
                'term_id' => $termId,
                'sub_classroom_subject_id' => $subClassroomSubjectId,
                'user_id' => $stu,
                'is_enrolled' => $isEnrolled,
                'enrolled_at' => $isEnrolled ? $now : null,
                'unenrolled_at' => $isEnrolled ? null : $now,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        SubClassroomSubjectHasStudent::upsert(
            $payloads,
            ['term_id', 'sub_classroom_subject_id', 'user_id'],
            ['is_enrolled', 'enrolled_at', 'unenrolled_at', 'updated_at']
        );

        return count($payloads);
    }

    public function enrollAllForStudent(int $termId, SubClassroom $subClassroom, int $studentUserId, bool $isEnrolled, ?string $note = null): int
    {
        $isMember = User::query()
            ->where('id', $studentUserId)
            ->where('sub_classroom_id', $subClassroom->id)
            ->exists();
        if (!$isMember) {
            abort(404, 'Student not in class');
        }

        $now = Carbon::now();
        $subjects = SubClassroomSubject::query()
            ->where('sub_classroom_id', $subClassroom->id)
            ->pluck('id');

        $payloads = [];
        foreach ($subjects as $subj) {
            $payloads[] = [
                'term_id' => $termId,
                'sub_classroom_subject_id' => $subj,
                'user_id' => $studentUserId,
                'is_enrolled' => $isEnrolled,
                'enrolled_at' => $isEnrolled ? $now : null,
                'unenrolled_at' => $isEnrolled ? null : $now,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        SubClassroomSubjectHasStudent::upsert(
            $payloads,
            ['term_id', 'sub_classroom_subject_id', 'user_id'],
            ['is_enrolled', 'enrolled_at', 'unenrolled_at', 'updated_at']
        );

        return count($payloads);
    }
}
