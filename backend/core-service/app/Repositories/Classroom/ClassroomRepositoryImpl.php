<?php

namespace App\Repositories\Classroom;

use App\Models\Classroom;
use App\Models\SubClassroom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClassroomRepositoryImpl implements ClassroomRepository
{
    public function paginate(Request $request)
    {
        return Classroom::with('subClassrooms')->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(array $data): Classroom
    {
        return DB::transaction(function () use ($data) {
            $classroom = Classroom::create(['name' => $data['name']]);
            $activeSchoolId = session('active_school_id');

            $subClassrooms = [];
            for ($i = 1; $i <= $data['sub_classroom_count']; $i++) {
                $subClassrooms[] = [
                    'school_id' => $activeSchoolId,
                    'classroom_id' => $classroom->id,
                    'sequence' => $i,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            SubClassroom::insert($subClassrooms);

            if (isset($data['subject_ids'])) {
                $this->assignSubjects($classroom, $data['subject_ids']);
            }

            return $classroom->load('subClassrooms');
        });
    }

    private function assignSubjects(Classroom $classroom, array $subjectIds): void
    {
        $classroom->subClassrooms->each(fn($sub) => $sub->subjects()->sync($subjectIds));
    }

    public function show(Classroom $classroom): Classroom
    {
        return $classroom->load([
            'subClassrooms' => fn($q) => $q->with(['subjects', 'teacher']),
        ]);
    }

    public function update(Classroom $classroom, array $data): Classroom
    {
        return DB::transaction(function () use ($classroom, $data) {
            if (isset($data['name'])) {
                $classroom->update(['name' => $data['name']]);
            }

            if (isset($data['sub_classroom_count'])) {
                $currentSubClassrooms = $classroom->subClassrooms()->orderBy('sequence')->get();
                $currentCount = $currentSubClassrooms->count();
                $newCount = $data['sub_classroom_count'];

                if ($newCount > $currentCount) {
                    for ($i = $currentCount + 1; $i <= $newCount; $i++) {
                        SubClassroom::create([
                            'classroom_id' => $classroom->id,
                            'sequence' => (string)$i,
                        ]);
                    }
                } elseif ($newCount < $currentCount) {
                    $toRemove = $currentSubClassrooms->slice($newCount);
                    SubClassroom::whereIn('id', $toRemove->pluck('id'))->delete();
                }

                $classroom->subClassrooms()
                    ->orderBy('id')
                    ->get()
                    ->each(fn($s, $i) => $s->update(['sequence' => (string)($i + 1)]));
            }

            if (isset($data['subject_ids'])) {
                $this->assignSubjects($classroom, $data['subject_ids']);
            }

            return $classroom->load('subClassrooms');
        });
    }

    public function delete(Classroom $classroom): void
    {
        DB::transaction(function () use ($classroom) {
            SubClassroom::whereNotIn('classroom_id', Classroom::pluck('id'))->each(function ($sub) {
                $sub->subClassroomSubjects()->delete();
                $sub->delete();
            });

            $classroom->subClassrooms()->delete();

            $classroom->delete();
        });
    }
}
