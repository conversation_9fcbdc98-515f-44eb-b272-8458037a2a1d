<?php

namespace App\Repositories\Exam;

use App\Enums\CognitiveLevel;
use App\Enums\QuestionType;
use App\Events\ExamResultUpdated;
use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\Exam\StoreExamRequest;
use App\Http\Requests\Exam\UpdateExamRequest;
use App\Jobs\SyncAttemptToReportCardJob;
use App\Models\AcademicYear;
use App\Models\ExamAttemptAnswer;
use App\Models\ExamQuestion;
use App\Models\SubClassroomSubject;
use App\Models\Term;
use App\Services\AcademicPeriodResolver;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Filesystem\FilesystemAdapter;

class ExamRepositoryImpl implements ExamRepository
{
    protected function mediaDisk(): FilesystemAdapter
    {
        $disk = config('filesystems.default_media', env('MEDIA_DISK', config('filesystems.default', 'gcs')));
        return Storage::disk($disk);
    }

    public function paginateAll(Request $request): LengthAwarePaginator
    {
        $query = Exam::query()
            ->when($request->filled('search'), function ($q) use ($request) {
                $term = trim((string) $request->search);
                if ($term !== '') {
                    $q->where('title', 'ILIKE', '%' . $term . '%');
                }
            })
            ->with([
                'subClassroomSubject.subClassroom.classroom',
                'subClassroomSubject.subject',
                'subClassroomSubject.teacher',
            ])
            ->withCount('questions')
            ->orderBy('exams.title', 'asc');

        $limit = max(1, min((int) ($request->integer('limit') ?? config('app.pagination.max', 15)), 200));
        return $query->paginate($limit);
    }

    public function paginateForTeacher(Request $request): LengthAwarePaginator
    {
        if (!$request->filled('user_id')) {
            throw new \InvalidArgumentException('User ID is required');
        }

        $query = Exam::query()
            ->with([
                'subClassroomSubject.subClassroom.classroom',
                'subClassroomSubject.subject',
                'subClassroomSubject.teacher',
            ])
            ->select('exams.*')
            ->join('sub_classroom_subjects as scs', function ($join) {
                $join->on('scs.id', '=', 'exams.sub_classroom_subject_id')
                    ->on('scs.teacher_user_id', '=', 'exams.created_by');
            })
            ->where('scs.teacher_user_id', (int) $request->user_id)
            ->withCount('questions')
            ->orderBy('exams.start_datetime');

        $query->when($request->filled('term_id'), fn($qq) => $qq->where('exams.term_id', (int) $request->term_id));
        $query->when($request->filled('sub_classroom_subject_id'), fn($qq) => $qq->where('exams.sub_classroom_subject_id', (int) $request->sub_classroom_subject_id));
        $query->when($request->filled('is_published'), fn($qq) => $qq->where('exams.is_published', filter_var($request->is_published, FILTER_VALIDATE_BOOLEAN)));
        $query->when($request->filled('title'), fn($q) => $q->where('exams.title', 'ILIKE', '%' . trim($request->title) . '%'));

        $limit = max(1, min((int) ($request->integer('limit') ?? config('app.pagination.max', 15)), 200));
        return $query->paginate($limit);
    }

    public function paginateForStudent(Request $request): LengthAwarePaginator
    {
        if (!$request->filled('user_id')) {
            throw new \InvalidArgumentException('User ID is required');
        }

        $studentUserId = (int) $request->user_id;
        $termId = $request->filled('term_id') ? (int) $request->term_id : null;

        $query = Exam::query()
            ->select('exams.*')
            ->with([
                'subClassroomSubject.subClassroom.classroom',
                'subClassroomSubject.subject',
                'subClassroomSubject.teacher',
                'attempts' => fn($q) => $q->where('user_id', $studentUserId)->latest()->limit(1),
            ])
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'exams.sub_classroom_subject_id')
            ->leftJoin('sub_classroom_subject_has_students as scshs', function ($join) use ($studentUserId) {
                $join->on('scs.id', '=', 'scshs.sub_classroom_subject_id')
                    ->where('scshs.user_id', '=', $studentUserId)
                    ->whereColumn('scshs.term_id', 'exams.term_id');
            })
            ->selectRaw('COALESCE(scshs.is_enrolled, false) as is_enrolled')
            ->where('exams.is_published', true)
            ->whereRaw('COALESCE(scshs.is_enrolled, false) = true')
            ->withCount('questions')
            ->orderBy('exams.start_datetime');

        $query->when($termId, fn($qq) => $qq->where('exams.term_id', $termId));
        $query->when($request->filled('sub_classroom_subject_id'), fn($qq) => $qq->where('exams.sub_classroom_subject_id', (int) $request->sub_classroom_subject_id));
        $query->when($request->filled('title'), fn($q) => $q->where('exams.title', 'ILIKE', '%' . trim($request->title) . '%'));

        $limit = max(1, min((int) ($request->integer('limit') ?? config('app.pagination.max', 15)), 200));
        return $query->paginate($limit);
    }

    public function store(StoreExamRequest $request): Exam
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();

            if (empty($validated['term_id'])) {
                $period = app(AcademicPeriodResolver::class)
                    ->resolveForDate($validated['start_datetime'] ?? now(), session('active_school_id'));
                $validated['term_id'] = $period['term_id'];
            }

            $this->assertDatetimeWithinTerm(
                (int) $validated['term_id'],
                $validated['start_datetime'] ?? null,
                $validated['end_datetime']   ?? null
            );

            return Exam::create($validated);
        });
    }

    public function showForTeacher(Exam $model): Exam
    {
        return $model;
    }

    public function showForStudent(Exam $model, int $studentId): Exam
    {
        $query = Exam::query()
            ->select('exams.*')
            ->with([
                'subClassroomSubject.subClassroom.classroom',
                'subClassroomSubject.subject',
                'subClassroomSubject.teacher',
                'attempts' => fn($q) => $q->where('user_id', $studentId)->latest()->limit(1),
            ])
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'exams.sub_classroom_subject_id')
            ->join('sub_classroom_subject_has_students as scshs', function ($join) use ($studentId) {
                $join->on('scs.id', '=', 'scshs.sub_classroom_subject_id')
                    ->where('scshs.user_id', '=', $studentId)
                    ->whereColumn('scshs.term_id', 'exams.term_id');
            })
            ->where('exams.is_published', true)
            ->where('exams.id', $model->id)
            ->withCount('questions')
            ->orderBy('exams.start_datetime');

        return $query->firstOrFail();
    }

    public function showWithAllRelations(int $examId): Exam
    {
        return Exam::with([
            'subClassroomSubject.subClassroom.classroom',
            'subClassroomSubject.subject',
            'subClassroomSubject.teacher',
            'questions.answerOptions',
        ])->findOrFail($examId);
    }

    public function update(Exam $model, UpdateExamRequest $request): Exam
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();

            $termId = (int) ($validated['term_id'] ?? $model->term_id);
            $start  = $validated['start_datetime'] ?? $model->start_datetime;
            $end    = $validated['end_datetime']   ?? $model->end_datetime;

            if (empty($termId)) {
                $period = app(AcademicPeriodResolver::class)
                    ->resolveForDate($start ?? now(), session('active_school_id'));
                $termId = (int) $period['term_id'];
                $validated['term_id'] = $termId;
            }

            $this->assertDatetimeWithinTerm($termId, $start, $end);

            $model->update($validated);
            return $model;
        });
    }

    public function delete(Exam $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    public function publish(Exam $model): void
    {
        $model->is_published = true;
        $model->save();
    }

    public function unpublish(Exam $model): void
    {
        $model->is_published = false;
        $model->save();
    }

    public function grade(Exam $exam): void
    {
        $attempts = ExamAttempt::where('exam_id', $exam->id)
            ->with(['answers', 'answers.examQuestion', 'answers.selectedOption'])
            ->get();

        foreach ($attempts as $attempt) {
            $score = 0;
            $correctCount = 0;
            foreach ($attempt->answers as $answer) {
                if ($answer->selectedOption && $answer->selectedOption->is_correct) {
                    $score += (int) $answer->examQuestion->points;
                    $correctCount++;
                }
            }

            $attempt->update([
                'score' => $score,
                'correct_count' => $correctCount,
                'status' => 'graded',
                'graded_at' => now()
            ]);
        }
    }

    public function paginateResult(Request $request): LengthAwarePaginator
    {
        $examId = (int) $request->exam_id;
        $exam   = Exam::findOrFail($examId);

        $subClassroomSubjectId = (int) $exam->sub_classroom_subject_id;
        $termId                = (int) $exam->term_id;

        $latestAttemptsSub = DB::table('exam_attempts as ea')
            ->select('ea.*')
            ->where('ea.exam_id', $examId)
            ->whereRaw('ea.id = (
                SELECT MAX(id) FROM exam_attempts
                WHERE exam_id = ? AND user_id = ea.user_id
            )', [$examId]);

        $q = DB::table('sub_classroom_subject_has_students as scshs')
            ->join('users as u', 'u.id', '=', 'scshs.user_id')
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'scshs.sub_classroom_subject_id')
            ->leftJoinSub($latestAttemptsSub, 'la', fn($join) => $join->on('la.user_id', '=', 'u.id'))
            ->where('scshs.sub_classroom_subject_id', $subClassroomSubjectId)
            ->where('scshs.term_id', $termId)
            ->whereNull('u.deleted_at')
            ->whereColumn('u.sub_classroom_id', 'scs.sub_classroom_id')
            ->whereExists(function ($sub) {
                $sub->from('user_school_roles as usr')
                    ->join('roles as r', 'r.id', '=', 'usr.role_id')
                    ->whereColumn('usr.user_id', 'u.id')
                    ->whereColumn('usr.school_id', 'scs.school_id')
                    ->where('r.name', 'student');
            })
            ->select([
                'u.id   as student_id',
                'u.name as student_name',
                'u.email as student_email',
                'la.id as attempt_id',
                'la.start_datetime',
                'la.submit_datetime',
                'la.score',
                'la.correct_count',
                DB::raw("
            COALESCE(
                la.status,
                CASE
                    WHEN la.id IS NULL THEN 'ready'
                    WHEN la.submit_datetime IS NOT NULL THEN 'completed'
                    ELSE 'in_progress'
                END
            ) as status
        "),
            ])
            ->orderBy('status')
            ->orderBy('u.name');

        $limit = max(10, min((int) ($request->integer('limit') ?? config('app.pagination.max', 15)), 200));
        $studentsWithAttempts = $q->paginate($limit);

        foreach ($studentsWithAttempts as $student) {
            if ($student->attempt_id) {
                $answers = ExamAttemptAnswer::where('exam_attempt_id', $student->attempt_id)
                    ->with(['examQuestion', 'selectedOption'])
                    ->get();

                $score = 0;
                $correctCount = 0;
                $allGraded = true;

                foreach ($answers as $answer) {
                    if ($answer->selectedOption) {
                        if ($answer->selectedOption->is_correct) {
                            $score += $answer->examQuestion->points;
                            $correctCount++;
                        }
                    } elseif ($answer->essay_answer || $answer->media_answer !== null) {
                        if (is_null($answer->points_awarded)) {
                            $allGraded = false;
                        } else {
                            $score += $answer->points_awarded;
                        }
                    } else {
                        $allGraded = false;
                    }
                }

                $attempt = ExamAttempt::find($student->attempt_id);
                if ($attempt) {
                    $prevStatus = $attempt->status ?? null;
                    $prevScore  = $attempt->score ?? null;

                    if ($allGraded) {
                        $attempt->status    = 'graded';
                        $attempt->graded_at = now();
                    } else {
                        $attempt->graded_at = null;
                    }

                    $attempt->score         = $score;
                    $attempt->correct_count = $correctCount;
                    $attempt->save();

                    if (
                        ($allGraded && $prevStatus !== 'graded') ||
                        ($allGraded && isset($prevScore) && $prevScore != $score)
                    ) {
                        SyncAttemptToReportCardJob::dispatchSync($attempt->id);
                    }
                }
            }
        }

        return $studentsWithAttempts;
    }

    public function showById(int $examId): Exam
    {
        return Exam::where('id', $examId)->firstOrFail();
    }

    public function countStudentExam(Exam $exam): array
    {
        $examId = (int) $exam->id;
        $subClassroomSubjectId = (int) $exam->sub_classroom_subject_id;
        $termId = (int) $exam->term_id;

        $eligibleStudentsSub = DB::table('sub_classroom_subject_has_students as scshs')
            ->join('users as u', 'u.id', '=', 'scshs.user_id')
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'scshs.sub_classroom_subject_id')
            ->where('scshs.sub_classroom_subject_id', $subClassroomSubjectId)
            ->where('scshs.term_id', $termId)
            ->whereNull('u.deleted_at')
            ->whereColumn('u.sub_classroom_id', 'scs.sub_classroom_id')
            ->whereExists(function ($q) {
                $q->from('user_school_roles as usr')
                    ->join('roles as r', 'r.id', '=', 'usr.role_id')
                    ->whereColumn('usr.user_id', 'u.id')
                    ->whereColumn('usr.school_id', 'scs.school_id')
                    ->where('r.name', 'student');
            })
            ->distinct()
            ->select('u.id as user_id');

        $latestAttemptsSub = DB::table('exam_attempts as ea')
            ->select('ea.user_id', 'ea.status')
            ->selectRaw('ROW_NUMBER() OVER (PARTITION BY ea.user_id ORDER BY ea.id DESC) as rn')
            ->where('ea.exam_id', $examId);

        $row = DB::query()
            ->fromSub($eligibleStudentsSub, 'es')
            ->leftJoinSub($latestAttemptsSub, 'la', function ($join) {
                $join->on('la.user_id', '=', 'es.user_id')
                    ->where('la.rn', '=', 1);
            })
            ->selectRaw("
            SUM(CASE WHEN la.user_id IS NULL THEN 1 ELSE 0 END) AS not_started,
            SUM(CASE WHEN la.status IN ('graded','completed') THEN 1 ELSE 0 END) AS completed,
            SUM(CASE WHEN la.status = 'in_progress' THEN 1 ELSE 0 END) AS in_progress
        ")
            ->first();

        return [
            'not_started' => (int) ($row->not_started ?? 0),
            'completed'   => (int) ($row->completed ?? 0),
            'in_progress' => (int) ($row->in_progress ?? 0),
        ];
    }

    public function duplicateExamDeep(Exam $exam, array $payload, int $actorUserId): Exam
    {
        $exam->load(['questions.answerOptions']);

        $disk = $this->mediaDisk();

        $isUrl = static fn(?string $p) =>
        is_string($p) && (str_starts_with($p, 'http://') || str_starts_with($p, 'https://'));

        $copyMedia = function (?string $oldPath, string $targetDir) use ($disk, $isUrl) {
            if (empty($oldPath) || $isUrl($oldPath)) {
                return $oldPath;
            }

            $old = ltrim($oldPath, '/');
            if (!$disk->exists($old)) {
                return $oldPath;
            }

            $dir  = trim($targetDir, '/');
            $ext  = pathinfo($old, PATHINFO_EXTENSION);
            $name = pathinfo($old, PATHINFO_FILENAME);

            $newName = Str::uuid()->toString() . '_' . $name . ($ext ? ".{$ext}" : '');
            $newPath = "{$dir}/{$newName}";

            try {
                $disk->makeDirectory($dir);
            } catch (\Throwable $e) {
                // ignore
            }

            $disk->copy($old, $newPath);

            return $newPath;
        };

        return DB::transaction(function () use ($exam, $payload, $actorUserId, $copyMedia) {
            $newExam = $exam->replicate();

            $newExam->title = $payload['title'] ?? ($exam->title . ' (Copy)');

            $newExam->sub_classroom_subject_id = null;
            $newExam->start_datetime = null;
            $newExam->end_datetime   = null;

            if (array_key_exists('passing_score', $payload)) {
                $newExam->passing_score = $payload['passing_score'];
            }

            if (array_key_exists('is_shuffled', $payload)) {
                $newExam->is_shuffled = (bool) $payload['is_shuffled'];
            }

            $newExam->is_published = isset($payload['is_published']) ? (bool) $payload['is_published'] : false;

            if (array_key_exists('term_id', $payload) && $payload['term_id']) {
                $newExam->term_id = (int) $payload['term_id'];
            } else {
                $resolved = app(AcademicPeriodResolver::class)
                    ->resolveForDate(now(), session('active_school_id'));
                $newExam->term_id = (int) $resolved['term_id'];
            }

            if ($newExam->start_datetime && $newExam->end_datetime) {
                try {
                    $this->assertDatetimeWithinTerm((int) $newExam->term_id, $newExam->start_datetime, $newExam->end_datetime);
                } catch (\Throwable $e) {
                    $resolved = app(AcademicPeriodResolver::class)
                        ->resolveForDate($newExam->start_datetime ?? now(), session('active_school_id'));
                    $newExam->term_id = (int) $resolved['term_id'];
                    $this->assertDatetimeWithinTerm((int) $newExam->term_id, $newExam->start_datetime, $newExam->end_datetime);
                }
            }

            $newExam->created_by = $actorUserId;
            $newExam->updated_by = $actorUserId;
            $newExam->deleted_by = null;

            $newExam->exists = false;
            $newExam->save();

            foreach ($exam->questions as $question) {
                $newQuestion = $question->replicate();
                $newQuestion->exam_id     = $newExam->id;
                $newQuestion->created_by  = $actorUserId;
                $newQuestion->updated_by  = $actorUserId;
                $newQuestion->deleted_by  = null;

                if (!empty($question->media_questions)) {
                    $newQuestion->media_questions = $copyMedia($question->media_questions, 'exam/questions');
                }

                $newQuestion->exists = false;
                $newQuestion->save();

                foreach ($question->answerOptions as $opt) {
                    $newOpt = $opt->replicate();
                    $newOpt->exam_question_id = $newQuestion->id;
                    $newOpt->created_by       = $actorUserId;
                    $newOpt->updated_by       = $actorUserId;
                    $newOpt->deleted_by       = null;

                    if (!empty($opt->media_answer_options)) {
                        $newOpt->media_answer_options = $copyMedia($opt->media_answer_options, 'exam/answer_options');
                    }

                    $newOpt->exists = false;
                    $newOpt->save();
                }
            }

            return $newExam->load([
                'questions.answerOptions',
                'subClassroomSubject.subject',
            ]);
        });
    }

    public function getUnifiedRowsForExport(int $examId): Collection
    {
        $questions = ExamQuestion::query()
            ->with(['answerOptions' => fn($q) => $q->orderBy('order_index')])
            ->where('exam_id', $examId)
            ->orderBy('order_index')
            ->get();

        $no = 1;

        return $questions->map(function ($q) use (&$no) {
            $qtVal = $q->question_type instanceof QuestionType ? $q->question_type->value : (string)$q->question_type;
            $isPG  = ($qtVal === QuestionType::MULTIPLE_CHOICE->value);

            $letters = [1 => 'A', 2 => 'B', 3 => 'C', 4 => 'D', 5 => 'E'];

            $optMap     = ['A' => '', 'B' => '', 'C' => '', 'D' => '', 'E' => ''];
            $keyLetter  = '';

            if ($isPG) {
                foreach ($q->answerOptions as $o) {
                    $ord = (int)($o->order_index ?? 0);
                    $L   = $letters[$ord] ?? null;
                    if ($L) {
                        $optMap[$L] = strip_tags((string)($o->content ?? ''));
                        if (!empty($o->is_correct)) $keyLetter = $L;
                    }
                }
                if ($keyLetter === '') {
                    $pos = $q->answerOptions->values()->search(fn($o) => (bool)($o->is_correct ?? false));
                    if ($pos !== false) $keyLetter = chr(ord('A') + (int)$pos);
                }
            }

            $lkLabel = '';
            if ($q->level_kognitif instanceof CognitiveLevel) {
                $lkLabel = $q->level_kognitif->label();
            } elseif (is_string($q->level_kognitif) && $q->level_kognitif !== '') {
                $lkLabel = CognitiveLevel::tryFrom($q->level_kognitif)?->label() ?? $q->level_kognitif;
            }

            return [
                'no'                   => $no++,
                'type'                 => $isPG ? 'PG' : 'Essay',
                'code'                 => $q->kd_number ?? '',
                'indicator'            => $q->competency_indicator ?? '',
                'question'             => strip_tags((string)($q->content ?? '')),
                'a'                    => $optMap['A'],
                'b'                    => $optMap['B'],
                'c'                    => $optMap['C'],
                'd'                    => $optMap['D'],
                'e'                    => $optMap['E'],
                'answer_key_option'    => $keyLetter,
                'answer_key_essay'     => strip_tags((string)($q->answer_key_essay ?? '')),
                'cognitive_level'      => $lkLabel,
                'learning_outcome'     => $q->learning_outcome ?? '',
            ];
        });
    }

    private function assertDatetimeWithinTerm(int $termId, $start = null, $end = null): void
    {
        if (!$start && !$end) return;

        $term = Term::findOrFail($termId);
        $s = $start ? Carbon::parse($start) : null;
        $e = $end   ? Carbon::parse($end)   : null;

        $min = $term->start_date->startOfDay();
        $max = $term->end_date->endOfDay();

        if ($s && ($s->lt($min) || $s->gt($max))) {
            throw new \InvalidArgumentException('start_datetime is outside the term range.');
        }
        if ($e && ($e->lt($min) || $e->gt($max))) {
            throw new \InvalidArgumentException('end_datetime is outside the term range.');
        }
        if ($s && $e && $e->lt($s)) {
            throw new \InvalidArgumentException('end_datetime must be >= start_datetime.');
        }
    }
}
