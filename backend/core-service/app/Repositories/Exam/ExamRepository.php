<?php

namespace App\Repositories\Exam;

use App\Http\Requests\Exam\PublishExamRequest;
use App\Models\Exam;
use Illuminate\Http\Request;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\Exam\StoreExamRequest;
use App\Http\Requests\Exam\UpdateExamRequest;
use Illuminate\Support\Collection;

interface ExamRepository
{
    public function paginateAll(Request $request): LengthAwarePaginator;
    public function paginateForTeacher(Request $request): LengthAwarePaginator;
    public function paginateForStudent(Request $request): LengthAwarePaginator;
    public function paginateResult(Request $request): LengthAwarePaginator;

    public function store(StoreExamRequest $request): Exam;
    public function showForTeacher(Exam $model): Exam;
    public function showForStudent(Exam $model, int $studentId): Exam;
    public function showWithAllRelations(int $examId): Exam;
    public function showById(int $examId): Exam;

    public function update(Exam $model, UpdateExamRequest $request): Exam;
    public function delete(Exam $model): void;
    public function publish(Exam $model): void;
    public function unpublish(Exam $model): void;
    public function grade(Exam $exam): void;
    public function countStudentExam(Exam $exam);

    public function duplicateExamDeep(Exam $exam, array $payload, int $actorUserId): Exam;
    public function getUnifiedRowsForExport(int $examId): Collection;
}
