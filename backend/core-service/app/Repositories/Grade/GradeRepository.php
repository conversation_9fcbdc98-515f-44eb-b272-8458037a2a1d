<?php

namespace App\Repositories\Grade;

use App\Http\Requests\Grade\StoreGradeRequest;
use App\Models\Grade;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface GradeRepository
{
    public function paginate(array $query): LengthAwarePaginator;
    public function store(StoreGradeRequest $req): Grade;
    public function show(Grade $model): Grade;
    public function update(Grade $model, array $data): Grade;
    public function delete(Grade $model): void;
}
