<?php

namespace App\Repositories\Grade;

use App\Http\Requests\Grade\StoreGradeRequest;
use App\Models\Assignment;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\ReportCard;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class GradeRepositoryImpl implements GradeRepository
{
    public function paginate(array $query): LengthAwarePaginator
    {
        return Grade::query()
            ->when(isset($query['report_card_id']), function ($q) use ($query) {
                $q->where('report_card_id', $query['report_card_id']);
            })
            ->when(isset($query['student_user_id']), function ($q) use ($query) {
                $q->where('student_user_id', $query['student_user_id']);
            })
            ->when(isset($query['type']), function ($q) use ($query) {
                $q->where('type', $query['type']);
            })
            ->paginate($query['limit'] ?? config('app.pagination.max'));
    }

    public function store(StoreGradeRequest $req): Grade
    {
        return DB::transaction(function () use ($req) {
            $data = $req->validated();

            // Auto isi term_id jika kosong
            if (empty($data['term_id'])) {
                if (!empty($data['exam_id'])) {
                    $data['term_id'] = Exam::whereKey($data['exam_id'])->value('term_id');
                } elseif (!empty($data['assignment_id'])) {
                    $data['term_id'] = Assignment::whereKey($data['assignment_id'])->value('term_id');
                }
            }

            $grade = Grade::create($data);

            // Optional: auto link/update ke report card per Mapel
            // Asumsi: you have logic mapel via sub_classroom_subject_id on exam/assignment.
            // $this->syncReportCard($grade);
            return $grade;
        });
    }

    public function show(Grade $model): Grade
    {
        return $model->load('subClassroomSubject');
    }

    public function update(Grade $model, array $data): Grade
    {
        return DB::transaction(function () use ($model, $data) {
            $model->update($data);
            return $model;
        });
    }

    public function delete(Grade $model): void
    {
        DB::transaction(fn() => $model->delete());
    }
    protected function syncReportCard(Grade $grade): void
    {
        // You can define it yourself: final_score = combination of assignment/mid/final, etc.
        // Minimal example: if type mid_exam/final_exam, recap to report card student-subject-term
        $studentId = $grade->student_user_id;
        $termId    = $grade->term_id;

        // Find sub_classroom_subject_id from parent source of value
        $subClassroomSubjectId = null;

        if ($grade->exam_id) {
            $subClassroomSubjectId = \App\Models\Exam::whereKey($grade->exam_id)->value('sub_classroom_subject_id');
        } elseif ($grade->assignment_id) {
            // If assignments store subject_id + sub_classroom_id, you can map to sub_classroom_subjects here
            $subClassroomSubjectId = \App\Models\SubClassroomSubject::where([
                'sub_classroom_id' => Assignment::whereKey($grade->assignment_id)->value('sub_classroom_id'),
                'subject_id'       => Assignment::whereKey($grade->assignment_id)->value('subject_id'),
            ])->value('id');
        }

        if (!$subClassroomSubjectId) return;

        /** @var ReportCard $reportCard */
        $reportCard = ReportCard::firstOrCreate([
            'term_id'                  => $termId,
            'student_user_id'          => $studentId,
            'sub_classroom_subject_id' => $subClassroomSubjectId,
        ]);

        // Example weight (please adjust)
        // mid_exam 40%, final_exam 60%, assignment average optional
        $mid  = Grade::where(['student_user_id' => $studentId, 'term_id' => $termId, 'type' => 'mid_exam'])
            ->whereNotNull('score')->avg('score') ?: 0;
        $fin  = Grade::where(['student_user_id' => $studentId, 'term_id' => $termId, 'type' => 'final_exam'])
            ->whereNotNull('score')->avg('score') ?: 0;

        $assignAvg = Grade::where(['student_user_id' => $studentId, 'term_id' => $termId, 'type' => 'assignment'])
            ->whereNotNull('score')->avg('score') ?: 0;

        $final = 0.4 * $mid + 0.6 * $fin;
        // jika ingin campur assignment, mis: final = 0.25*assign + 0.35*mid + 0.40*final
        // $final = 0.25*$assignAvg + 0.35*$mid + 0.40*$fin;

        $reportCard->final_score = round($final, 2);
        $reportCard->save();
    }
}
