<?php

namespace App\Repositories\TuitionPayment;

use App\Enums\PaymentStatus;
use App\Http\Requests\TuitionPayment\StoreTuitionPaymentRequest;
use App\Http\Requests\TuitionPayment\UpdateTuitionPaymentRequest;
use App\Models\TuitionInvoice;
use App\Models\TuitionPayment;
use App\Services\FinancialCoreService;
use App\Enums\FinancialCoreReferenceType;
use App\Enums\FinancialCoreStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TuitionPaymentRepositoryImpl implements TuitionPaymentRepository
{
    protected FinancialCoreService $financialCoreService;

    public function __construct(FinancialCoreService $financialCoreService)
    {
        $this->financialCoreService = $financialCoreService;
    }

    private function generatePaymentNumber()
    {
        $time = time();
        $time = time();
        $lastPayment = TuitionPayment::orderBy('id', 'desc')->first();
        if (!$lastPayment) {
            return 'PAY-' . $time . '-0001';
            return 'PAY-' . $time . '-0001';
        }
        $lastId = (int) substr($lastPayment->payment_number, 15);
        return 'PAY-' . $time . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
    }

    public function paginate(Request $request)
    {
        $status = $request->get('status');
        $studentUserId = $request->get('student_user_id');
        return TuitionPayment::with(['student', 'student.subClassroom', 'tuitionInvoices'])
            ->when($studentUserId, function ($query) use ($studentUserId) {
                return $query->where('student_user_id', $studentUserId);
            })
            ->when($status, function ($query) use ($status) {
                return $query->whereHas('tuitionInvoices', function ($q) use ($status) {
                    $q->where('status', PaymentStatus::from($status));
                });
            })
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreTuitionPaymentRequest $request): TuitionPayment
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();
            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('tuition-payment-photos', 'public');
            }

            $payment = TuitionPayment::create([
                'payment_number' => $this->generatePaymentNumber(),
                'student_user_id' => $validated['student_user_id'],
                'payment_method' => $validated['payment_method'],
                'status' => $request->user()->hasRole('school_admin') ? PaymentStatus::COMPLETED : PaymentStatus::PENDING,
                'amount' => $validated['amount'],
                'notes' => $validated['notes'] ?? null,
                'photo' => $validated['photo'] ?? null,
            ]);

            $payment->tuitionInvoices()->attach($validated['tuition_invoice_ids']);

            foreach ($validated['tuition_invoice_ids'] as $invoiceId) {
                // Ensure the invoice exists and is not already paid
                $invoice = TuitionInvoice::findOrFail($invoiceId);

                if ($invoice->status === PaymentStatus::COMPLETED) {
                    throw new \Exception("Invoice {$invoice->id} is already paid.");
                }

                // Update the invoice status to pending
                $invoice->update([
                    'status' => $request->user()->hasRole('school_admin') ? PaymentStatus::COMPLETED : PaymentStatus::PENDING,
                ]);
            }

            // If admin creates payment, automatically record to FinancialCore
            if ($request->user()->hasRole('school_admin') && $payment->status === PaymentStatus::COMPLETED) {
                $this->financialCoreService->createFromTuitionPayment($payment->load('tuitionInvoices'));
            }

            return $payment;
        });
    }

    public function show(TuitionPayment $model): TuitionPayment
    {
        return $model;
    }

    public function update(TuitionPayment $model, UpdateTuitionPaymentRequest $request): TuitionPayment
    {
        return DB::transaction(function () use ($model, $request) {
            $model->update($request->validated());
            return $model;
        });
    }

    public function delete(TuitionPayment $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    public function approve(TuitionPayment $model): TuitionPayment
    {
        return DB::transaction(function () use ($model) {
            foreach ($model->tuitionInvoices as $invoice) {
                // Ensure the invoice is not already approved
                if ($invoice->status === PaymentStatus::COMPLETED) {
                    throw new \Exception("Invoice {$invoice->id} is already approved.");
                }

                // Update the invoice status to completed
                $invoice->update(['status' => PaymentStatus::COMPLETED]);
            }

            // Update the payment status to completed
            $model->update(['status' => PaymentStatus::COMPLETED]);

            // Record to FinancialCore
            $this->financialCoreService->createFromTuitionPayment($model);

            return $model;
        });
    }

    public function reject(TuitionPayment $model): TuitionPayment
    {
        return DB::transaction(function () use ($model) {
            foreach ($model->tuitionInvoices as $invoice) {
                // Ensure the invoice is not already approved
                if ($invoice->status === PaymentStatus::COMPLETED) {
                    throw new \Exception("Invoice {$invoice->id} is already approved.");
                }

                // Update the invoice status to completed
                $invoice->update(['status' => PaymentStatus::UNPAID]);
            }

            // Update the payment status to completed
            $model->update(['status' => PaymentStatus::FAILED]);

            return $model;
        });
    }
}
