<?php

namespace App\Repositories\ExamQuestion;

use App\Http\Requests\ExamQuestion\StoreMultipleExamQuestionsRequest;
use App\Models\Exam;
use App\Models\ExamQuestion;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\ExamQuestion\StoreExamQuestionRequest;
use App\Http\Requests\ExamQuestion\UpdateExamQuestionRequest;

interface ExamQuestionRepository
{
    public function paginate(Request $request): LengthAwarePaginator;
    public function store(StoreExamQuestionRequest $request): ExamQuestion;
    public function storeMultiple(StoreMultipleExamQuestionsRequest $request): void;
    public function show(ExamQuestion $examQuestion, Exam $exam): ExamQuestion;
    public function update(ExamQuestion $model, UpdateExamQuestionRequest $request): ExamQuestion;
    public function delete(ExamQuestion $model): void;

    public function listSourceExams(?int $termId,?int $subClassroomSubjectId, ?string $q): array;
    public function listQuestionsOfSource(Exam $sourceExam);
    public function copyFromExam(Exam $targetExam, int $sourceExamId, array $questionIds, string $mode, bool $deepCopyMedia): array;
}
