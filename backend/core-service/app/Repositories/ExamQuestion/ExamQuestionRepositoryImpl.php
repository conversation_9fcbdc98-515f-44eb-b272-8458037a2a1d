<?php

namespace App\Repositories\ExamQuestion;

use App\Enums\QuestionType;
use App\Models\Exam;
use App\Models\ExamQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\ExamQuestion\StoreExamQuestionRequest;
use App\Http\Requests\ExamQuestion\UpdateExamQuestionRequest;
use App\Http\Requests\ExamQuestion\StoreMultipleExamQuestionsRequest;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Mews\Purifier\Facades\Purifier;
use Illuminate\Support\Str;

class ExamQuestionRepositoryImpl implements ExamQuestionRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        $query = ExamQuestion::query();

        $request->validate(['exam_id' => 'required|exists:exams,id']);
        $query->where('exam_id', (int) $request->exam_id);

        if ($request->filled('search')) {
            $term = trim((string) $request->input('search'));
            if ($term !== '') {
                $query->where('content', 'like', '%' . $term . '%');
            }
        }

        $limit = (int) $request->integer('limit', (int) config('app.pagination.max'));
        $limit = max(1, min($limit, 200));

        $p = $query->with(['answerOptions' => fn($q) => $q->orderBy('order_index', 'asc')])
            ->orderBy('order_index', 'asc')
            ->paginate($limit);

        $p->getCollection()->transform(fn(ExamQuestion $q) => $this->addMediaUrlsToQuestion($q));
        return $p;
    }

    public function store(StoreExamQuestionRequest $request): ExamQuestion
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();
            $validated['exam_id'] = (int)$request->input('exam_id');

            unset($validated['media_questions']);

            // sanitize quill
            $validated['content'] = $this->cleanQuillHtml((string)($validated['content'] ?? ''));
            $validated['answer_key_essay'] = $this->cleanQuillHtml((string)($validated['answer_key_essay'] ?? ''));

            // upload media pertanyaan (opsional)
            if ($request->hasFile('media_questions')) {
                $path = $this->uploadMedia($request->file('media_questions'), 'exam/questions', true);
                if ($path) $validated['media_questions'] = $path;
            }

            $answerOptionsData = $validated['answer_options'] ?? [];
            unset($validated['answer_options']);

            $created = ExamQuestion::create($validated);

            foreach ($answerOptionsData as $idx => $option) {
                unset($option['media_answer_options']); // cegah /tmp/php*

                $option['content'] = $this->cleanQuillHtml((string)($option['content'] ?? ''));
                $mediaOptionFile = $request->file("answer_options.$idx.media_answer_options");
                if ($mediaOptionFile instanceof UploadedFile) {
                    $optPath = $this->uploadMedia($mediaOptionFile, 'exam/answer_options', true);
                    if ($optPath) $option['media_answer_options'] = $optPath;
                }

                $created->answerOptions()->create($option + [
                    'created_by' => $request->user()->id
                ]);
            }

            return $this->addMediaUrlsToQuestion(
                $created->load(['answerOptions' => fn($q) => $q->orderBy('order_index')])
            );
        });
    }

    protected function mediaDisk(): FilesystemAdapter
    {
        $disk = config('filesystems.default_media', env('MEDIA_DISK', 'gcs'));
        return Storage::disk($disk);
    }

    protected function uploadMedia(?UploadedFile $file, string $folder, bool $public = false): ?string
    {
        if (!$file) return null;
        if (!$file->isValid()) {
            Log::error('Upload media invalid file', ['error' => $file->getError(), 'original' => $file->getClientOriginalName()]);
            return null;
        }

        $disk = $this->mediaDisk();

        $folder   = ltrim(rtrim($folder, '/'), '/');
        $ext      = $file->getClientOriginalExtension() ?: $file->extension() ?: 'bin';
        $basename = (string) Str::uuid();
        $filename = "{$basename}.{$ext}";
        $path     = "{$folder}/{$filename}";

        $mime = $file->getMimeType() ?: $file->getClientMimeType();
        if (!$mime && function_exists('finfo_open')) {
            $f = finfo_open(FILEINFO_MIME_TYPE);
            if ($f) {
                $mime = finfo_file($f, $file->getRealPath()) ?: null;
                finfo_close($f);
            }
        }
        $mime = $mime ?: 'application/octet-stream';

        $config = [
            'visibility' => $public ? 'public' : 'private',
            'metadata'   => [
                'contentType'  => $mime,
                'cacheControl' => 'public,max-age=86400',
            ],
        ];

        try {
            $ok = $disk->putFileAs($folder, $file, $filename, $config);

            if (!$ok) {
                $stream = fopen($file->getRealPath(), 'r');
                if ($stream !== false) {
                    if (ftell($stream) !== 0) rewind($stream);
                    $ok = $disk->writeStream($path, $stream, $config);
                    fclose($stream);
                }
            }

            if (!$ok) {
                $contents = @file_get_contents($file->getRealPath());
                if ($contents !== false) {
                    $ok = $disk->put($path, $contents, $config);
                }
            }

            if (!$ok) {
                Log::error('Upload media gagal: write false', ['path' => $path, 'config' => $config]);
                return null;
            }

            Log::info('Upload media success', ['path' => $path, 'mime' => $mime, 'visibility' => $config['visibility']]);
            return $path;
        } catch (\Throwable $e) {
            Log::error('Upload media exception', ['err' => $e->getMessage(), 'path' => $path]);
            return null;
        }
    }


    protected function addMediaUrlsToQuestion(ExamQuestion $q): ExamQuestion
    {
        $disk = $this->mediaDisk();

        $q->setAttribute('media_questions_url', $q->media_questions
            ? $disk->url($q->media_questions)
            : null);

        if ($q->relationLoaded('answerOptions')) {
            $q->answerOptions->transform(function ($opt) use ($disk) {
                $opt->setAttribute('media_answer_options_url', $opt->media_answer_options
                    ? $disk->url($opt->media_answer_options)
                    : null);
                return $opt;
            });
        }

        return $q;
    }

    public function storeMultiple(StoreMultipleExamQuestionsRequest $request): void
    {
        DB::transaction(function () use ($request) {
            $validated = $request->validated();
            $examId = (int) $validated['exam_id'];

            $existing = ExamQuestion::where('exam_id', $examId)
                ->ordered()
                ->with('answerOptions')
                ->get()
                ->values();

            ExamQuestion::where('exam_id', $examId)
                ->select('id')
                ->chunkById(200, function ($rows) {
                    foreach ($rows as $row) {
                        $q = ExamQuestion::find($row->id);
                        if (!$q) continue;
                        $q->answerOptions()->delete();
                        $q->delete();
                    }
                });

            foreach ($validated['questions'] as $index => $questionData) {
                $questionData['exam_id'] = $examId;

                unset($questionData['media_questions']);

                $mediaFile = $request->file("questions.$index.media_questions");
                $mediaPath = $this->uploadMedia($mediaFile, 'exam/questions', true);
                if ($mediaPath) {
                    $questionData['media_questions'] = $mediaPath;
                } else {
                    $prev = $existing[$index] ?? null;
                    if ($prev && !empty($prev->media_questions)) {
                        $questionData['media_questions'] = $prev->media_questions;
                    }
                }

                $questionData['content'] = $this->cleanQuillHtml((string)($questionData['content'] ?? ''));
                $questionData['answer_key_essay'] = $this->cleanQuillHtml((string)($questionData['answer_key_essay'] ?? ''));

                $answerOptions = [];
                if (($questionData['question_type'] ?? null) === QuestionType::MULTIPLE_CHOICE->value
                    || ($questionData['question_type'] ?? null) === 'multiple_choice'
                ) {
                    $answerOptions = $questionData['answer_options'] ?? [];
                }
                unset($questionData['answer_options']);

                $question = ExamQuestion::create($questionData);

                foreach ($answerOptions as $optIndex => $optionData) {
                    unset($optionData['media_answer_options']);

                    $mediaOptionFile = $request->file("questions.$index.answer_options.$optIndex.media_answer_options");
                    $mediaOptionPath = $this->uploadMedia($mediaOptionFile, 'exam/answer_options', true);
                    if ($mediaOptionPath) {
                        $optionData['media_answer_options'] = $mediaOptionPath;
                    }

                    $optionData['content'] = $this->cleanQuillHtml((string)($optionData['content'] ?? ''));

                    $question->answerOptions()->create($optionData + [
                        'created_by' => $request->user()->id,
                    ]);
                }
            }
        });
    }

    private function cleanQuillHtml(string $html): string
    {
        $normalized = $this->normalizeQuillFormula($html);
        return Purifier::clean($normalized, 'quill');
    }

    private function normalizeQuillFormula(string $html): string
    {
        if ($html === '') return $html;

        $decoded = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        $decoded = preg_replace_callback(
            '/<span([^>]*\bclass="[^"]*\bql-formula\b[^"]*"[^>]*)>(.*?)<\/span>/is',
            function ($m) {
                $attrs = $m[1];
                $inner = trim(strip_tags($m[2] ?? ''));
                if (preg_match('/\bdata-value=/i', $attrs) || $inner === '') {
                    return "<span{$attrs}>{$m[2]}</span>";
                }
                $attrsNew = rtrim($attrs) . ' data-value="' . e($inner) . '"';
                return "<span{$attrsNew}>{$m[2]}</span>";
            },
            $decoded
        );

        $out = $decoded;

        $out = preg_replace_callback('/\$\$([\s\S]+?)\$\$/u', function ($m) {
            $tex = trim($m[1]);
            return '<span class="ql-formula block-math" data-value="' . e($tex) . '">' . e($tex) . '</span>';
        }, $out);

        $out = preg_replace_callback('/\$([^$]+?)\$/u', function ($m) {
            $tex = trim($m[1]);
            return '<span class="ql-formula" data-value="' . e($tex) . '">' . e($tex) . '</span>';
        }, $out);

        $out = preg_replace_callback('/\\\\\[([\s\S]+?)\\\\\]/u', function ($m) {
            $tex = trim($m[1]);
            return '<span class="ql-formula block-math" data-value="' . e($tex) . '">' . e($tex) . '</span>';
        }, $out);

        $out = preg_replace_callback('/\\\\\(([\s\S]+?)\\\\\)/u', function ($m) {
            $tex = trim($m[1]);
            return '<span class="ql-formula" data-value="' . e($tex) . '">' . e($tex) . '</span>';
        }, $out);

        return $out;
    }

    public function show(ExamQuestion $examQuestion, Exam $exam): ExamQuestion
    {
        $examQuestion = $exam->questions()
            ->where('id', $examQuestion->id)
            ->with(['answerOptions' => fn($q) => $q->orderBy('order_index', 'asc')])
            ->firstOrFail();

        return $this->addMediaUrlsToQuestion($examQuestion);
    }

    public function update(ExamQuestion $model, UpdateExamQuestionRequest $request): ExamQuestion
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();
            $answerOptionsData = $validated['answer_options'] ?? [];
            unset($validated['answer_options'], $validated['media_questions']);

            if (array_key_exists('content', $validated)) {
                $validated['content'] = $this->cleanQuillHtml((string) $validated['content']);
            }
            if (array_key_exists('answer_key_essay', $validated)) {
                $validated['answer_key_essay'] = $this->cleanQuillHtml((string) $validated['answer_key_essay']);
            }


            if ($request->hasFile('media_questions')) {
                if ($path = $this->uploadMedia($request->file('media_questions'), 'exam/questions', true)) {
                    $validated['media_questions'] = $path;
                }
            }

            if ($request->boolean('remove_media_questions')) {
                $validated['media_questions'] = null;
            }

            if (!empty($validated)) {
                $model->update($validated);
            }

            if (!empty($answerOptionsData)) {
                $model->answerOptions()->delete();

                foreach ($answerOptionsData as $idx => $optionData) {
                    unset($optionData['media_answer_options']);

                    $optionData['content'] = $this->cleanQuillHtml((string) ($optionData['content'] ?? ''));

                    $mediaOptionFile = $request->file("answer_options.$idx.media_answer_options");
                    if ($mediaOptionFile instanceof UploadedFile) {
                        if ($optPath = $this->uploadMedia($mediaOptionFile, 'exam/answer_options', true)) {
                            $optionData['media_answer_options'] = $optPath;
                        }
                    }
                    if (!empty($optionData['remove_media_answer_options'])) {
                        $optionData['media_answer_options'] = null;
                    }

                    $model->answerOptions()->create($optionData + [
                        'created_by' => $request->user()->id
                    ]);
                }
            }

            $model->load(['answerOptions' => fn($q) => $q->orderBy('order_index', 'asc')]);
            return $this->addMediaUrlsToQuestion($model);
        });
    }

    public function delete(ExamQuestion $model): void
    {
        DB::transaction(function () use ($model) {
            $model->answerOptions()->delete();
            $model->delete();
        });
    }

    public function listSourceExams(?int $termId, ?int $subClassroomSubjectId, ?string $q): array
    {
        $user = auth()->user();
        $isAdmin = method_exists($user, 'hasAnyRole')
            ? $user->hasAnyRole(['superadmin', 'foundation_admin', 'school_admin'])
            : (($user->is_superadmin ?? false) || ($user->is_admin ?? false) || ($user->is_school_admin ?? false));

        $query = Exam::query()
            ->select(['id', 'title', 'term_id', 'sub_classroom_subject_id', 'created_by'])
            ->with(['term:id,name,order'])
            ->orderByDesc('id')
            ->when($termId, fn($q2) => $q2->where('term_id', $termId))
            ->when($subClassroomSubjectId, fn($q2) => $q2->where('sub_classroom_subject_id', $subClassroomSubjectId))
            ->when($q, fn($q2) => $q2->where('title', 'ILIKE', "%{$q}%"));

        if (!$isAdmin) {
            $query->where(function ($w) use ($user) {
                $w->where('created_by', $user->id)
                    ->orWhereHas('subClassroomSubject', function ($qq) use ($user) {
                        $qq->where(function ($x) use ($user) {
                            $x->where('teacher_user_id', $user->id)
                                ->orWhere('teacher_user_id', $user->id);
                        });
                    });
            });
        }

        return $query->limit(200)->get()->map(fn($e) => [
            'id'    => $e->id,
            'title' => $e->title,
            'term'  => $e->term?->name,
        ])->all();
    }

    public function listQuestionsOfSource(Exam $sourceExam): LengthAwarePaginator
    {
        $perPage = (int) request()->integer('per_page', 200);
        $perPage = max(1, min($perPage, 1000)); // hard cap biar aman
        $page    = (int) request()->integer('page', 1);

        return $sourceExam->questions()
            ->with(['answerOptions' => fn($q) => $q->orderBy('order_index')])
            ->orderBy('order_index')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param array<int> $questionIds
     */
    public function copyFromExam(Exam $targetExam, int $sourceExamId, array $questionIds, string $mode, bool $deepCopyMedia): array
    {
        return DB::transaction(function () use ($targetExam, $sourceExamId, $questionIds, $mode, $deepCopyMedia) {
            $sourceQuestions = ExamQuestion::query()
                ->where('exam_id', $sourceExamId)
                ->whereIn('id', $questionIds)
                ->with('answerOptions')
                ->orderBy('order_index')
                ->get();

            $startOrder = (int) ($targetExam->questions()->max('order_index') ?? 0);
            $currentOrder = $mode === 'prepend' ? 0 : $startOrder;

            $created = [];
            foreach ($sourceQuestions as $idx => $sq) {
                $newOrder = $mode === 'prepend' ? ($idx + 1) : (++$currentOrder);

                // media: default referensi sama path (no duplication)
                $mediaQuestionPath = $sq->media_questions;

                if ($deepCopyMedia && $sq->media_questions) {
                    $mediaQuestionPath = $this->duplicateMedia($sq->media_questions, 'exam/questions');
                }

                $newQ = $targetExam->questions()->create([
                    'question_type'        => $sq->question_type,
                    'content'              => $sq->content,
                    'answer_key_essay'     => $sq->answer_key_essay,
                    'points'               => $sq->points,
                    'order_index'          => $newOrder,
                    'kd_number'            => $sq->kd_number,
                    'learning_outcome'     => $sq->learning_outcome,
                    'competency_indicator' => $sq->competency_indicator,
                    'level_kognitif'       => $sq->level_kognitif,
                    'media_questions'      => $mediaQuestionPath,
                ]);

                foreach ($sq->answerOptions as $opt) {
                    $mediaOptPath = $opt->media_answer_options;
                    if ($deepCopyMedia && $opt->media_answer_options) {
                        $mediaOptPath = $this->duplicateMedia($opt->media_answer_options, 'exam/answer_options');
                    }

                    $newQ->answerOptions()->create([
                        'content'               => $opt->content,
                        'is_correct'            => $opt->is_correct,
                        'order_index'           => $opt->order_index,
                        'media_answer_options'  => $mediaOptPath,
                        'created_by'            => auth()->id(),
                    ]);
                }

                $created[] = $newQ->id;
            }

            // jika prepend, rapikan kembali order_index agar berurutan dari 1..n
            if ($mode === 'prepend') {
                $all = $targetExam->questions()->orderBy('order_index')->get(['id']);
                foreach ($all as $i => $row) {
                    $targetExam->questions()->where('id', $row->id)->update(['order_index' => $i + 1]);
                }
            }

            return [
                'copied' => count($created),
                'question_ids' => $created,
            ];
        });
    }

    private function duplicateMedia(string $srcPath, string $folder): ?string
    {
        $disk = Storage::disk(config('filesystems.default_media', env('MEDIA_DISK', 'gcs')));
        if (!$disk->exists($srcPath)) return $srcPath; // fallback referensi lama

        $ext = pathinfo($srcPath, PATHINFO_EXTENSION) ?: 'bin';
        $dst = trim($folder, '/') . '/' . Str::uuid() . '.' . $ext;

        try {
            // gunakan copy bila driver support; fallback stream
            if (method_exists($disk, 'copy') && $disk->copy($srcPath, $dst)) {
                return $dst;
            }
            $stream = $disk->readStream($srcPath);
            if ($stream === false) return $srcPath;
            $ok = $disk->writeStream($dst, $stream, [
                'visibility' => 'public',
                'metadata'   => ['cacheControl' => 'public,max-age=86400'],
            ]);
            if (is_resource($stream)) fclose($stream);
            return $ok ? $dst : $srcPath;
        } catch (\Throwable $e) {
            Log::warning('duplicateMedia failed', ['err' => $e->getMessage(), 'src' => $srcPath]);
            return $srcPath;
        }
    }
}
