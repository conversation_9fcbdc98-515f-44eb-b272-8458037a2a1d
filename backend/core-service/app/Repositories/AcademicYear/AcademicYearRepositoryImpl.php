<?php

namespace App\Repositories\AcademicYear;

use App\Models\AcademicYear;
use App\Models\Term;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class AcademicYearRepositoryImpl implements AcademicYearRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return AcademicYear::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function show(AcademicYear $academicYear): AcademicYear
    {
        return $academicYear;
    }
    public function store(array $data): AcademicYear
    {
        return DB::transaction(function () use ($data) {
            $year = AcademicYear::create($data);

            $existingTerms = Term::where('academic_year_id', $year->id)->count();

            if ($existingTerms === 0) {
                $start = Carbon::parse($year->start_date);
                $end = Carbon::parse($year->end_date);

                $mid = $start->copy()->addDays(floor($start->diffInDays($end) / 2));

                Term::create([
                    'academic_year_id' => $year->id,
                    'name' => 'Semester I',
                    'order' => 1,
                    'start_date' => $start,
                    'end_date' => $mid,
                ]);

                Term::create([
                    'academic_year_id' => $year->id,
                    'name' => 'Semester II',
                    'order' => 2,
                    'start_date' => $mid->copy()->addDay(),
                    'end_date' => $end,
                ]);
            }

            return $year;
        });
    }


    public function update(AcademicYear $academicYear, array $data): AcademicYear
    {
        return DB::transaction(function () use ($academicYear, $data) {
            $academicYear->update($data);
            return $academicYear;
        });
    }

    public function delete(AcademicYear $academicYear): void
    {
        DB::transaction(function () use ($academicYear) {
            $academicYear->terms()->delete();
            $academicYear->delete();
        });
    }
}
