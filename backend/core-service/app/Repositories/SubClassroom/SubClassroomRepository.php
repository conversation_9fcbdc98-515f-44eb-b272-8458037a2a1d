<?php

namespace App\Repositories\SubClassroom;

use App\Models\SubClassroom;
use Illuminate\Http\Request;

interface SubClassroomRepository
{
    public function paginate(Request $request);

    public function store(array $data): SubClassroom;

    public function show(SubClassroom $subClassroom): SubClassroom;

    public function update(SubClassroom $subClassroom, array $data): SubClassroom;

    public function delete(SubClassroom $subClassroom): void;

    public function assignStudents(SubClassroom $subClassroom, array $studentUserIds): array;

    public function getStudents(SubClassroom $subClassroom, Request $request);

    public function getSubjects(SubClassroom $subClassroom, Request $request);

    public function assignSubjectTeacher(SubClassroom $subClassroom, int $subjectId, int $teacherUserId);

    public function getAssignments(SubClassroom $subClassroom, Request $request);

    public function getAssignedSubClassrooms(int $teacherUserId, Request $request);

    public function assignTeacher(SubClassroom $subClassroom, int $teacherUserId);

    public function assignSubject(int $subClassroomId, array $subjectIds);

    public function syncStudents(?int $schoolId);
}
