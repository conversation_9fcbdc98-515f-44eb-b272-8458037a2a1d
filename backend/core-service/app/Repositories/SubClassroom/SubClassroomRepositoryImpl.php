<?php

namespace App\Repositories\SubClassroom;

use App\Jobs\CleanupSubjectEnrollmentsForRemovedStudent;
use App\Jobs\EnsureSubjectEnrollmentsForNewStudent;
use App\Models\Assignment;
use App\Models\Role;
use App\Models\School;
use App\Models\SubClassroom;
use App\Models\SubClassroomSubject;
use App\Models\Term;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SubClassroomRepositoryImpl implements SubClassroomRepository
{
    public function paginate(Request $request)
    {
        return SubClassroom::with(['classroom', 'teacher'])
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(array $data): SubClassroom
    {
        return DB::transaction(fn() => SubClassroom::create($data)->load('classroom'));
    }

    public function show(SubClassroom $subClassroom): SubClassroom
    {
        return $subClassroom->load('classroom');
    }

    public function delete(SubClassroom $subClassroom): void
    {
        DB::transaction(fn() => $subClassroom->delete());
    }

    public function assignStudents(SubClassroom $subClassroom, array $studentUserIds): array
    {
        $incoming = collect($studentUserIds ?? []);

        // 1) jika kosong: kosongkan semua siswa kelas tsb
        if ($incoming->isEmpty()) {
            $currentIds = User::where('sub_classroom_id', $subClassroom->id)
                ->whereHas('roles', fn($q) => $q->where('name', 'student'))
                ->pluck('id');

            DB::transaction(function () use ($subClassroom) {
                User::where('sub_classroom_id', $subClassroom->id)
                    ->whereHas('roles', fn($q) => $q->where('name', 'student'))
                    ->update(['sub_classroom_id' => null]);
            });

            // cleanup enrollments untuk semua yang dilepas
            foreach ($currentIds as $uid) {
                dispatch(new CleanupSubjectEnrollmentsForRemovedStudent($subClassroom->id, (int)$uid));
            }

            return ['success' => true, 'attached' => [], 'detached' => $currentIds->values()];
        }

        // 2) validasi konflik: siswa sudah di kelas lain
        $conflicting = User::whereIn('id', $incoming)
            ->whereNotNull('sub_classroom_id')
            ->where('sub_classroom_id', '!=', $subClassroom->id)
            ->whereHas('roles', fn($q) => $q->where('name', 'student'))
            ->pluck('id');

        if ($conflicting->isNotEmpty()) {
            return ['conflict' => $conflicting];
        }

        // 3) hitung diff attach/detach
        $currentIds = User::where('sub_classroom_id', $subClassroom->id)
            ->whereHas('roles', fn($q) => $q->where('name', 'student'))
            ->pluck('id');

        $toAttach = $incoming->diff($currentIds)->values();
        $toDetach = $currentIds->diff($incoming)->values();

        // 4) apply changes
        DB::transaction(function () use ($subClassroom, $incoming, $toAttach, $toDetach) {
            // set kelas utk yg dipilih
            if ($incoming->isNotEmpty()) {
                User::whereIn('id', $incoming)->update(['sub_classroom_id' => $subClassroom->id]);
            }
            // kosongkan yg tidak lagi dipilih
            if ($toDetach->isNotEmpty()) {
                User::whereIn('id', $toDetach)->update(['sub_classroom_id' => null]);
            }
        });

        // 5) jobs untuk enrollments
        foreach ($toAttach as $uid) {
            dispatch(new EnsureSubjectEnrollmentsForNewStudent($subClassroom->id, (int)$uid));
        }
        foreach ($toDetach as $uid) {
            dispatch(new CleanupSubjectEnrollmentsForRemovedStudent($subClassroom->id, (int)$uid));
        }

        return ['success' => true, 'attached' => $toAttach, 'detached' => $toDetach];
    }

    // public function assignStudents(SubClassroom $subClassroom, array $studentUserIds): array
    // {
    //     if (empty($studentUserIds)) {
    //         // Unassign all students from the sub-classroom
    //         User::where('sub_classroom_id', $subClassroom->id)
    //             ->whereHas('roles', function ($query) {
    //                 $query->where('name', 'student');
    //             })
    //             ->update(['sub_classroom_id' => null]);
    //         return ['success' => true];
    //     }

    //     $conflicting = User::whereIn('id', $studentUserIds)
    //         ->whereNotNull('sub_classroom_id')
    //         ->where('sub_classroom_id', '!=', $subClassroom->id)
    //         ->hasRole('student')
    //         ->pluck('id');

    //     if ($conflicting->isNotEmpty()) {
    //         return ['conflict' => $conflicting];
    //     }

    //     DB::transaction(function () use ($subClassroom, $studentUserIds) {
    //         User::whereIn('id', $studentUserIds)->update(['sub_classroom_id' => $subClassroom->id]);
    //         $subClassroom->students()->whereNotIn('id', $studentUserIds)->update(['sub_classroom_id' => null]);
    //     });

    //     return ['success' => true];
    // }

    public function update(SubClassroom $subClassroom, array $data): SubClassroom
    {
        return DB::transaction(function () use ($subClassroom, $data) {
            $subClassroom->update($data);
            return $subClassroom->load('classroom');
        });
    }

    public function getStudents(SubClassroom $subClassroom, Request $request)
    {
        return $subClassroom->students()->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function getSubjects(SubClassroom $subClassroom, Request $request)
    {
        $subjects = SubClassroomSubject::with(['subject', 'teacher'])
            ->where('sub_classroom_id', $subClassroom->id)
            ->orderByRaw("COALESCE(updated_at, '2000-01-01') DESC")
            ->paginate($request->limit ?? config('app.pagination.max'));

        return $subjects;
    }

    public function assignSubjectTeacher(SubClassroom $subClassroom, int $subjectId, int $teacherUserId)
    {
        DB::transaction(function () use ($subClassroom, $subjectId, $teacherUserId) {
            $record = SubClassroomSubject::firstOrNew([
                'sub_classroom_id' => $subClassroom->id,
                'subject_id' => $subjectId
            ]);

            $record->teacher_user_id = $teacherUserId;
            $record->save();
        });
    }

    public function getAssignments(SubClassroom $subClassroom, Request $request)
    {
        return Assignment::where('sub_classroom_id', $subClassroom->id)
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function getAssignedSubClassrooms(int $teacherUserId, Request $request)
    {
        $ids = SubClassroomSubject::where('teacher_user_id', $teacherUserId)
            ->pluck('sub_classroom_id');

        return SubClassroom::whereIn('id', $ids)
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function assignTeacher(SubClassroom $subClassroom, int $teacherUserId)
    {
        DB::transaction(function () use ($subClassroom, $teacherUserId) {
            $activeSchoolId = session('active_school_id');
            $activeSchool = School::find($activeSchoolId);

            // Unassign current teacher from the sub-classroom
            $oldTeacher = User::where('sub_classroom_id', $subClassroom->id)->hasRole(Role::HOMEROOM_TEACHER)->first();
            if ($oldTeacher) {
                $oldTeacher->update(['sub_classroom_id' => null]);
                $oldTeacher->removeRole(Role::HOMEROOM_TEACHER);
            }

            // Assign new teacher to the sub-classroom
            $newTeacher = User::find($teacherUserId);
            $newTeacher->update(['sub_classroom_id' => $subClassroom->id]);
            $newTeacher->assignSchoolRole($activeSchool, Role::HOMEROOM_TEACHER);
        });
    }

    public function assignSubject(int $subClassroomId, array $subjectIds)
    {
        DB::transaction(function () use ($subClassroomId, $subjectIds) {
            SubClassroom::find($subClassroomId)?->subjects()->sync($subjectIds);
        });
    }

    public function syncStudents(?int $schoolId = null): void
    {
        $term = Term::ofCurrentYear($schoolId)->firstOrFail();

        DB::insert(<<<'SQL'
        INSERT INTO sub_classroom_subject_has_students
            (sub_classroom_subject_id, user_id, term_id, is_enrolled, created_at, updated_at)
        SELECT DISTINCT
            scs.id AS sub_classroom_subject_id,
            u.id  AS user_id,
            ?::bigint AS term_id,
            TRUE       AS is_enrolled,   -- kalau mau default true di insert
            NOW() AS created_at,
            NOW() AS updated_at
        FROM
            users u
        INNER JOIN user_school_roles ur ON u.id = ur.user_id
        INNER JOIN sub_classrooms sc    ON u.sub_classroom_id = sc.id
        INNER JOIN sub_classroom_subjects scs ON sc.id = scs.sub_classroom_id
        WHERE
            ur.role_id = 4
            AND u.deleted_at  IS NULL
            AND sc.deleted_at IS NULL
            AND scs.deleted_at IS NULL
            AND u.sub_classroom_id IS NOT NULL
            AND NOT EXISTS (
                SELECT 1
                FROM sub_classroom_subject_has_students scsh
                WHERE scsh.sub_classroom_subject_id = scs.id
                  AND scsh.user_id = u.id
                  AND scsh.term_id = ?::bigint
                  AND scsh.deleted_at IS NULL
            )
    SQL, [$term->id, $term->id]);
    }
}
