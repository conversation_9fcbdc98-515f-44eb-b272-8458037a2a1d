<?php

namespace App\Repositories\Transaction;

use App\Enums\PaymentMethod;
use App\Enums\PaymentStatus;
use App\Enums\TransactionType;
use App\Http\Requests\Transaction\StoreTransactionRequest;
use App\Http\Requests\Transaction\UpdateTransactionRequest;
use App\Models\Cashier;
use App\Models\NfcCard;
use App\Models\NfcCardTransaction;
use App\Models\Product;
use App\Models\Transaction;
use App\Models\TransactionItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class TransactionRepositoryImpl implements TransactionRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        $user = $request->user();

        $canteenId = $user->canteenAdmin->canteen_id
            ?? $user->cashier->canteen_id
            ?? null;

        if (!$canteenId) {
            abort(403, 'Anda tidak memiliki akses ke kantin manapun.');
        }

        $query = Transaction::query()
            ->where('canteen_id', $canteenId)
            ->with('items.product');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Optional filter tambahan
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('cashier_user_id')) {
            $query->where('cashier_id', $request->cashier_user_id);
        }

        return $query->orderByDesc('transacted_at')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreTransactionRequest $request): Transaction
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();
            $cashier = Cashier::where('user_id', $request->user()->id)->firstOrFail();

            // Generate kode transaksi unik
            $code = $this->generateTransactionCode($request);

            $status = $validated['payment_method'] === PaymentMethod::PAY_LATER ? 'unpaid' : 'completed';

            $transaction = Transaction::create([
                'code' => $code,
                'canteen_id' => $cashier->canteen_id,
                'cashier_id' => $cashier->id,
                'total_price' => $validated['total_price'],
                'paid_amount' => $validated['paid_amount'],
                'change_amount' => $validated['change_amount'],
                'payment_method' => $validated['payment_method'],
                'status' => $status,
                'transacted_at' => now(),
            ]);

            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);

                TransactionItem::create([
                    'transaction_id' => $transaction->id,
                    'canteen_id' => $cashier->canteen_id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                ]);

                $product->decrement('stock', $item['quantity']);
            }

            // If using RFID Card
            if ($validated['payment_method'] === 'rfid') {
                $rfid = $validated['rfid'] ?? null;
                if (!$rfid) {
                    throw ValidationException::withMessages([
                        'rfid' => ['RFID tidak ditemukan atau tidak valid.'],
                    ]);
                }

                $card = NfcCard::where('uid', $rfid)->first();
                if (!$card) {
                    throw ValidationException::withMessages([
                        'rfid' => ['Kartu NFC tidak ditemukan. Pastikan UID kartu benar.'],
                    ]);
                }

                if ($card->balance < $transaction->total_price) {
                    throw ValidationException::withMessages([
                        'rfid' => ['Saldo NFC Card tidak cukup untuk transaksi ini.'],
                    ]);
                }

                // Kurangi saldo kartu
                $card->update([
                    'balance' => $card->balance - $transaction->total_price,
                ]);

                // Catat transaksi kartu NFC
                $nfcTransaction = NfcCardTransaction::create([
                    'nfc_card_id'   => $card->id,
                    'transaction_id' => $transaction->id,
                    'type'          => TransactionType::EXPENSE,
                    'amount'        => $transaction->total_price,
                    'status'        => PaymentStatus::COMPLETED,
                    'description'   => "Pembayaran transaksi {$transaction->code}",
                    'transaction_number' => $this->generateNfcTransactionNumber(),
                ]);

                // Record to FinancialCore (spending = income for school)
                app(\App\Services\FinancialCoreService::class)->createFromCashlessTransaction($nfcTransaction->load('nfcCard.user'));
            }

            return $transaction->load('items.product');
        });
    }

    private function generateTransactionCode(Request $request): string
    {
        $user = $request->user();
        $canteenName = optional($user->cashier->canteen)->name ?? 'TRX';

        // Ambil huruf pertama dari setiap kata
        $initial = collect(explode(' ', strtoupper($canteenName)))
            ->map(fn($word) => substr($word, 0, 1))
            ->implode('');

        $date = now()->format('Ymd');
        $random = strtoupper(Str::random(4));

        return "{$initial}-{$date}-{$random}";
    }

    public function show(Transaction $model): Transaction
    {
        return $model->load('items.product');
    }

    public function update(Transaction $model, UpdateTransactionRequest $request): Transaction
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();
            $changeAmount = $validated['paid_amount'] - $model->total_price;

            if ($validated['paid_amount'] < $model->total_price) {
                throw ValidationException::withMessages([
                    'paid_amount' => ['Jumlah bayar tidak boleh kurang dari total harga.'],
                ]);
            }

            $model->update([
                'payment_method' => $validated['payment_method'],
                'paid_amount'    => $validated['paid_amount'],
                'change_amount'  => $changeAmount,
                'status'         => 'completed',
            ]);

            return $model->load('items.product');
        });
    }

    public function delete(Transaction $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    private function generateNfcTransactionNumber(): string
    {
        $time = time();
        $lastTransaction = NfcCardTransaction::orderBy('id', 'desc')->first();
        if (!$lastTransaction) {
            return 'NFC-' . $time . '-0001';
        }
        
        // Extract number from transaction_number if it exists
        $lastNumber = 0;
        if ($lastTransaction->transaction_number) {
            $parts = explode('-', $lastTransaction->transaction_number);
            if (count($parts) >= 3) {
                $lastNumber = (int) end($parts);
            }
        }
        
        return 'NFC-' . $time . '-' . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
