<?php

namespace App\Repositories\TuitionInvoice;

use App\Enums\PaymentStatus;
use App\Enums\TuitionFeeType;
use App\Http\Requests\TuitionInvoice\GetTuitionInvoiceRequest;
use App\Http\Requests\TuitionInvoice\StoreTuitionInvoiceRequest;
use App\Http\Requests\TuitionInvoice\UpdateTuitionInvoiceRequest;
use App\Models\ExtracurricularStudent;
use App\Models\TuitionFee;
use App\Models\TuitionInvoice;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TuitionInvoiceRepositoryImpl implements TuitionInvoiceRepository
{
    public function paginate(GetTuitionInvoiceRequest $request)
    {
        $this->generateInvoice($request->student_user_id);
        $this->generateExtracurricularInvoice($request->student_user_id);
        return TuitionInvoice::where('student_user_id', $request->student_user_id)
            ->where('status', $request->status ?? PaymentStatus::UNPAID)
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreTuitionInvoiceRequest $request): TuitionInvoice
    {
        return DB::transaction(fn() => TuitionInvoice::create($request));
    }

    public function show(TuitionInvoice $model): TuitionInvoice
    {
        return $model;
    }

    public function update(TuitionInvoice $model, UpdateTuitionInvoiceRequest $request): TuitionInvoice
    {
        return DB::transaction(function () use ($model, $request) {
            $model->update($request->validated());
            return $model;
        });
    }

    public function delete(TuitionInvoice $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    /**
     * Generates a new invoice number based on the last invoice number in the database.
     *
     * @return string
     */
    private function generateInvoiceNumber()
    {
        $time = time();
        $lastInvoice = TuitionInvoice::orderBy('invoice_number', 'desc')->first();
        if (!$lastInvoice) {
            return 'INV-' . $time . '-0001';
        }

        $lastNumber = (int) substr($lastInvoice->invoice_number, 15);
        $newNumber = $lastNumber + 1;
        return 'INV-' . $time . '-' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generates invoices for a student based on their tuition fees.
     *
     * @param int $studentUserId
     * @return void
     */
    private function generateInvoice(int $studentUserId): void
    {
        $student = User::findOrFail($studentUserId);
        $fees = TuitionFee::where('academic_year_id', $student->academic_year_id)
            ->where('type', '!=', 'extracurricular')
            ->where('is_active', true) // Only generate invoices for active tuition fees
            ->get();

        foreach ($fees as $fee) {
            // Build base query for this student and fee
            $query = TuitionInvoice::where('student_user_id', $student->id)
                ->where('tuition_fee_id', $fee->id);

            switch ($fee->type) {
                case TuitionFeeType::MONTHLY:
                    $query->whereMonth('created_at', now()->month)
                        ->whereYear('created_at', now()->year);
                    break;
                case TuitionFeeType::ANNUAL:
                    $query->whereYear('created_at', now()->year);
                    break;
            }

            // If an invoice already exists for this period, skip it
            if ($query->exists()) {
                continue;
            }

            // Otherwise, create the invoice
            TuitionInvoice::create([
                'student_user_id' => $student->id,
                'tuition_fee_id' => $fee->id,
                'amount' => $fee->amount,
                'due_date' => now()->endOfMonth(),
                'description' => $fee->name,
                'invoice_number' => $this->generateInvoiceNumber(),
            ]);
        }
    }


    /**
     * Generates invoices for extracurricular activities for a student.
     *
     * @param int $studentUserId
     * @return void
     */
    private function generateExtracurricularInvoice(int $studentUserId): void
    {
        $extras = ExtracurricularStudent::with('extracurricular')
            ->where('student_user_id', $studentUserId)
            ->get();


        foreach ($extras as $extra) {
            $query = TuitionInvoice::where('student_user_id', $extra->student_user_id)
                ->where('extracurricular_id', $extra->extracurricular->id);

            // Check if an invoice already exists for this month
            if ($query->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->exists()
            ) {
                continue; // Skip if invoice already exists
            }

            // Create a new invoice for the extracurricular activity
            if (!$extra->extracurricular->fee) {
                continue; // Skip if no fee is set for the extracurricular activity
            }
            if ($extra->extracurricular->fee <= 0) {
                continue; // Skip if the fee is not set or is zero
            }
            // Create the invoice
            TuitionInvoice::create([
                'student_user_id' => $extra->student_user_id,
                'extracurricular_id' => $extra->extracurricular->id,
                'amount' => $extra->extracurricular->fee,
                'due_date' => now()->endOfMonth(),
                'description' => 'Ekstrakurikuler: ' . $extra->extracurricular->name,
                'invoice_number' => $this->generateInvoiceNumber(),
            ]);
        }
    }
}
