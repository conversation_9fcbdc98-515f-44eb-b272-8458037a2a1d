<?php

namespace App\Repositories\Facility;

use App\Models\Facility;
use Illuminate\Http\Request;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface FacilityRepository
{
    public function paginate(Request $request): LengthAwarePaginator;
    public function store(array $data): Facility;
    public function show(Facility $model): Facility;
    public function update(Facility $model, array $data): Facility;
    public function delete(Facility $model): void;
    public function import(Request $request, ?int $schoolId = null): array;
    public function export();
    public function downloadImportTemplate();
}
