<?php

namespace App\Repositories\Facility;

use App\Exports\FacilityExport;
use App\Imports\Facility\FacilitiesImport;
use App\Imports\Facility\FacilitiesImportTemplate;
use App\Models\Facility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;


class FacilityRepositoryImpl implements FacilityRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return Facility::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(array $data): Facility
    {
        return DB::transaction(function () use ($data) {
            $imagePath = $this->handleImageUpload($data['image'] ?? null);

            $goodCondition = $data['good_condition'];
            $minorDamage   = $data['minor_damage'];
            $majorDamage   = $data['major_damage'];

            $stock = $goodCondition + $minorDamage + $majorDamage;

            return Facility::create([
                'name'            => $data['name'],
                'stock'           => $stock,
                'unit'            => $data['unit'],
                'good_condition'  => $goodCondition,
                'minor_damage'    => $minorDamage,
                'major_damage'    => $majorDamage,
                'image'           => $imagePath,
            ]);
        });
    }

    public function show(Facility $model): Facility
    {
        return $model;
    }

    public function update(Facility $model, array $data): Facility
    {
        return DB::transaction(function () use ($model, $data) {
            if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
                if ($model->image && Storage::disk('public')->exists($model->image)) {
                    Storage::disk('public')->delete($model->image);
                }
                $data['image'] = $this->handleImageUpload($data['image']);
            } else {
                unset($data['image']);
            }

            $good = $data['good_condition'] ?? $model->good_condition;
            $minor = $data['minor_damage'] ?? $model->minor_damage;
            $major = $data['major_damage'] ?? $model->major_damage;

            $data['stock'] = $good + $minor + $major;

            $model->update($data);
            return $model;
        });
    }

    public function delete(Facility $model): void
    {
        DB::transaction(function () use ($model) {
            if ($model->image && Storage::disk('public')->exists($model->image)) {
                Storage::disk('public')->delete($model->image);
            }
            $model->delete();
        });
    }

    private function handleImageUpload(?UploadedFile $uploadedFile): ?string
    {
        if (!$uploadedFile) {
            return null;
        }

        $extension = $uploadedFile->getClientOriginalExtension();
        $filename = 'facility_' . Str::random(10) . '.' . $extension;

        return Storage::disk('public')->putFileAs(
            'facilities',
            $uploadedFile,
            $filename
        );
    }

    public function import(Request $request, ?int $schoolId = null): array
    {
        // Validate that we have a school ID
        if (!$schoolId) {
            throw new \InvalidArgumentException('School ID is required for import');
        }
        
        // Pass the school ID explicitly to the import class
        Excel::import(new FacilitiesImport($schoolId), $request->file('file'));
        return ['status' => true, 'message' => 'Import data fasilitas berhasil'];
    }

    public function export(): BinaryFileResponse
    {
        return Excel::download(new FacilityExport, 'fasilitas_export_' . now()->format('Ymd_His') . '.xlsx');
    }
    
    public function downloadImportTemplate(): BinaryFileResponse
    {
        return Excel::download(new FacilitiesImportTemplate, 'template_import_fasilitas.xlsx');
    }
}
