<?php

namespace App\Repositories\Cashier;

use App\Models\Canteen;
use App\Models\Cashier;
use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;

class CashierRepositoryImpl implements CashierRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return Cashier::with(['user', 'canteen'])->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(array $data): Cashier
    {
        return DB::transaction(function () use ($data) {
            $user = User::where('email', $data['user']['email'])->first();

            if (!$user) {
                $user = User::create([
                    'name' => $data['user']['name'],
                    'email' => $data['user']['email'],
                    'phone' => $data['user']['phone'] ?? null,
                    'password' => Hash::make('password'),
                    'is_active' => true,
                    'created_by' => auth()->id(),
                ]);

                // Instead of using assignRole directly, we'll handle school roles properly
                // This prevents duplicate role entries
            }
            
            // Get the canteen to find its school
            $canteen = Canteen::find($data['canteen_id']);
            $schoolId = null;
            
            // Try to get school ID from the canteen
            if ($canteen && $canteen->school_id) {
                $schoolId = $canteen->school_id;
            } 
            // Fall back to session or header
            else {
                $request = request();
                
                // Try to get from header
                if ($request->hasHeader('X-School-ID')) {
                    $schoolId = $request->header('X-School-ID');
                }
                // Fall back to session
                elseif (session()->has('active_school_id')) {
                    $schoolId = session('active_school_id');
                }
            }
            
            // If we have a school ID, assign the school role
            if ($schoolId) {
                $school = School::find($schoolId);
                if ($school && method_exists($user, 'assignSchoolRole')) {
                    // Only assign the school-specific role, not a global one
                    $user->assignSchoolRole($school, 'cashier');
                }
            } else {
                // Only if no school ID is available, assign a global role
                if (!$user->hasRole('cashier')) {
                    $user->assignRole('cashier');
                }
            }

            return Cashier::create([
                'user_id' => $user->id,
                'canteen_id' => $data['canteen_id'],
                'created_by' => auth()->id(),
            ]);
        });
    }

    public function show(Cashier $model): Cashier
    {
        return $model->load(['user', 'canteen']);
    }

    public function update(Cashier $model, array $data): Cashier
    {
        return DB::transaction(function () use ($model, $data) {
            $model->update([
                'canteen_id' => $data['canteen_id'] ?? $model->canteen_id,
            ]);

            if (!empty($data['user'])) {
                $model->user->update([
                    'name' => $data['user']['name'] ?? $model->user->name,
                    'email' => $data['user']['email'] ?? $model->user->email,
                    'phone' => $data['user']['phone'] ?? $model->user->phone,
                ]);
            }

            return $model;
        });
    }

    public function delete(Cashier $model): void
    {
        DB::transaction(function () use ($model) {
            $model->delete();
        });
    }
}
