<?php

namespace App\Repositories\ExamAttempt;

use App\Enums\QuestionType;
use App\Http\Requests\ExamAttempt\ExamAttemptAnswerRequest;
use App\Http\Resources\ExamAttemptAnswerResource;
use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\ExamQuestion;
use App\Models\ExamAttemptAnswer;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ExamAttemptRepositoryImpl implements ExamAttemptRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return ExamAttempt::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function show(ExamAttempt $model): ExamAttempt
    {
        $model = $model->load([
            'exam' => fn($q) => $q->select('id', 'title', 'description', 'start_datetime', 'end_datetime'),
            'exam.questions' => fn($q) => $q->orderBy('order_index', 'asc'),
            'answers'
        ])->append('questions_with_status');

        $model->answers->transform(function (ExamAttemptAnswer $a) {
            $a->setAttribute('media_answer_url', $a->media_answer ? Storage::disk('gcs')->url($a->media_answer) : null);
            return $a;
        });

        return $model;
    }

    public function submitAttempt(Exam $exam): ExamAttempt
    {
        $user = auth()->user();
        $examAttempt = ExamAttempt::where('exam_id', $exam->id)
            ->where('user_id', $user->id)
            ->whereNull('submit_datetime')
            ->latest()
            ->firstOrFail();

        $examAttempt->update([
            'submit_datetime' => now(),
            'status' => 'completed',
        ]);

        return $examAttempt->fresh();
    }

    public function createAttempt(Exam $exam): ExamAttempt
    {
        $user = auth()->user();
        return DB::transaction(function () use ($exam, $user) {
            Log::info($user->id);
            Log::info($exam->id);
            $attempt = ExamAttempt::create([
                'exam_id' => $exam->id,
                'user_id' => $user->id,
                'status' => 'in_progress',
                'start_datetime' => now(),
            ]);

            $this->createExamAttemptAnswers($attempt, $exam->is_shuffled);

            return $attempt;
        });
    }

    protected function createExamAttemptAnswers(ExamAttempt $attempt, bool $shuffled): void
    {
        $examQuestions = ExamQuestion::where('exam_id', $attempt->exam_id)
            ->orderBy('order_index')
            ->get();

        $studentAnswers = [];

        foreach ($examQuestions as $index => $question) {
            $orderIndex = $shuffled ? $index : $question->order_index;
            $studentAnswers[] = [
                'exam_attempt_id' => $attempt->id,
                'exam_question_id' => $question->id,
                'order_index' => $orderIndex,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if ($shuffled) {
            shuffle($studentAnswers);
            foreach ($studentAnswers as $index => $answer) {
                $studentAnswers[$index]['order_index'] = $index + 1;
            }
        }

        ExamAttemptAnswer::insert($studentAnswers);
    }

    public function findByExamAndStudent(Exam $exam, int $studentId): ?ExamAttempt
    {
        return ExamAttempt::where('exam_id', $exam->id)
            ->where('user_id', $studentId)
            ->first();
    }

    public function showQuestion(ExamAttempt $examAttempt, ExamAttemptAnswer $examAttemptAnswer): ExamAttemptAnswer
    {
        Log::info($examAttemptAnswer->exam_attempt_id);
        if ($examAttemptAnswer->exam_attempt_id !== $examAttempt->id) {
            throw new Exception('This answer does not belong to the specified exam attempt.');
        }

        $examAttemptAnswer->load([
            'examQuestion' => fn($q) => $q->with(['answerOptions' => fn($qq) => $qq->orderBy('order_index', 'asc')]),
            'selectedOption'
        ]);

        $examAttemptAnswer->setAttribute(
            'media_answer_url',
            $examAttemptAnswer->media_answer ? Storage::disk('gcs')->url($examAttemptAnswer->media_answer) : null
        );

        return $examAttemptAnswer;
    }

    public function answer(ExamAttemptAnswer $examAttemptAnswer, ExamAttemptAnswerRequest $request)
    {
        $examAttemptAnswer->loadMissing('examQuestion.answerOptions');
        $questionType = $examAttemptAnswer->examQuestion->question_type;

        if ($questionType === QuestionType::ESSAY) {
            $examAttemptAnswer->essay_answer = $request->input('essay_answer');
            $examAttemptAnswer->selected_option_id = null;

            if ($request->hasFile('media_answer')) {
                if ($examAttemptAnswer->media_answer) {
                    $this->removeObjectFromS3($examAttemptAnswer->media_answer);
                }
                $path = $this->uploadMedia($request->file('media_answer'), 'exam/answers', true);
                if ($path) $examAttemptAnswer->media_answer = $path;
            }

            if ($request->boolean('remove_media') && $examAttemptAnswer->media_answer) {
                $this->removeObjectFromS3($examAttemptAnswer->media_answer);
                $examAttemptAnswer->media_answer = null;
            }
        }

        if ($questionType === QuestionType::MULTIPLE_CHOICE) {
            $selectedOptionId = (int)$request->input('answer_option_id');
            $validOptionIds = $examAttemptAnswer->examQuestion->answerOptions->pluck('id')->toArray();

            if (!in_array($selectedOptionId, $validOptionIds)) {
                throw new Exception('Invalid option: The selected option does not belong to this question.');
            }

            $examAttemptAnswer->selected_option_id = $selectedOptionId;
            $examAttemptAnswer->essay_answer = null;

            if ($examAttemptAnswer->media_answer) {
                $this->removeObjectFromS3($examAttemptAnswer->media_answer);
                $examAttemptAnswer->media_answer = null;
            }
        }

        $examAttemptAnswer->save();

        $examAttemptAnswer->load(['examQuestion.answerOptions', 'selectedOption']);

        $examAttemptAnswer->setAttribute(
            'media_answer_url',
            $examAttemptAnswer->media_answer ? Storage::disk('gcs')->url($examAttemptAnswer->media_answer) : null
        );

        return new ExamAttemptAnswerResource($examAttemptAnswer);
    }

    private function uploadMedia(?UploadedFile $file, string $folder, bool $public = false): ?string
    {
        if (!$file) return null;

        $folder = trim($folder, '/');
        $ext = $file->getClientOriginalExtension() ?: $file->extension() ?: 'bin';
        $filename = \Illuminate\Support\Str::uuid()->toString() . '.' . $ext;

        $options = [];
        if ($public) $options['visibility'] = 'public';

        try {
            $stored = Storage::disk('gcs')->putFileAs($folder, $file, $filename, $options);
            return $stored ?: null;
        } catch (\Throwable $e) {
            Log::error('Upload media (attempt answer) error', ['err' => $e->getMessage()]);
            return null;
        }
    }

    private function removeObjectFromS3(?string $path): void
    {
        if (!$path) return;
        try {
            if (Storage::disk('gcs')->exists($path)) {
                Storage::disk('gcs')->delete($path);
            }
        } catch (\Throwable $e) {
            Log::warning('gcs delete failed', ['path' => $path, 'err' => $e->getMessage()]);
        }
    }

    public function getLatestAttempt(int $examId, int $studentId): ?ExamAttempt
    {
        return ExamAttempt::where('exam_id', $examId)
            ->where('user_id', $studentId)
            ->latest('id')
            ->first();
    }

    public function resetAttemptStatus(ExamAttempt $attempt): void
    {
        $attempt->update([
            'submit_datetime' => null,
            'status' => 'in_progress',
            'score' => null,
            'graded_at' => null,
            'correct_count' => null,
        ]);
    }

    public function forceSubmit(ExamAttempt $attempt): void
    {
        $attempt->update([
            'submit_datetime' => now(),
            'status' => 'completed',
        ]);
    }

    public function findAttemptDetails(int $examId, int $studentId, int $attemptId)
    {
        $attempt = ExamAttempt::with([
            'exam.subClassroomSubject.subject',
            'exam.subClassroomSubject.subClassroom',
            'student',
            'answers.examQuestion.answerOptions',
            'answers.answerOptions',
        ])
            ->where('exam_id', $examId)
            ->where('user_id', $studentId)
            ->where('id', $attemptId)
            ->first();

        if ($attempt) {
            $attempt->answers->transform(function (ExamAttemptAnswer $a) {
                $a->setAttribute('media_answer_url', $a->media_answer ? Storage::disk('gcs')->url($a->media_answer) : null);
                return $a;
            });
        }

        return $attempt;
    }

    public function updateEssayScore(int $examAttemptId, array $answer): void
    {
        ExamAttemptAnswer::where('id', $answer['exam_attempt_answers_id'])
            ->where('exam_attempt_id', $examAttemptId)
            ->update([
                'points_awarded' => $answer['points_awarded'],
                'updated_by' => auth()->id(),
            ]);
    }

    public function findWithAnswers(int $examAttemptId)
    {
        $attempt = ExamAttempt::with(['answers.examQuestion', 'answers.selectedOption'])
            ->findOrFail($examAttemptId);

        $attempt->answers->transform(function (ExamAttemptAnswer $a) {
            $a->setAttribute('media_answer_url', $a->media_answer ? Storage::disk('gcs')->url($a->media_answer) : null);
            return $a;
        });

        return $attempt;
    }
}
