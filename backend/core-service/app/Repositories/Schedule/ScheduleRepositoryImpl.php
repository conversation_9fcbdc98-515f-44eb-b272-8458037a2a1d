<?php

namespace App\Repositories\Schedule;

use App\Http\Requests\Schedule\GetAttendanceStatusRequest;
use App\Models\Schedule;
use App\Models\SubClassroomSubject;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ScheduleRepositoryImpl implements ScheduleRepository
{
    public function getSchedulesByDayAndSubClassroom(int $day, int $subClassroomId)
    {
        $activeSchoolId = session('active_school_id');
        return DB::table('schedule_periods as sp')
            ->leftJoin(DB::raw('
                (
                    SELECT
                        s.id,
                        s.schedule_period_id,
                        s.created_at,
                        s.updated_at,
                        sc.name as sub_classroom,
                        scs.sub_classroom_id,
                        scs.subject_id,
                        scs.teacher_user_id,
                        subjects.name AS subject,
                        users.name AS teacher
                    FROM schedules s
                    JOIN sub_classroom_subjects scs ON s.sub_classroom_subject_id = scs.id
                    JOIN subjects ON subjects.id = scs.subject_id
                    JOIN (
                        SELECT sc.id, CONCAT(c.name, \' - \', sc.sequence) as name
                        FROM sub_classrooms as sc
                        JOIN classrooms as c ON sc.classroom_id = c.id
                    ) AS sc on sc.id = scs.sub_classroom_id
                    LEFT JOIN users ON users.id = scs.teacher_user_id
                    WHERE s.day = ' . (int)$day . ' AND scs.sub_classroom_id = ' . (int)$subClassroomId . '
                ) as filtered_schedules
            '), 'filtered_schedules.schedule_period_id', '=', 'sp.id')
            ->select(
                'sp.id as period_id',
                'sp.period',
                DB::raw("TO_CHAR(sp.start_time, 'HH24:MI') as start_time"),
                DB::raw("TO_CHAR(sp.end_time, 'HH24:MI') as end_time"),
                'filtered_schedules.id',
                'filtered_schedules.subject',
                'filtered_schedules.subject_id',
                'filtered_schedules.teacher',
                'filtered_schedules.teacher_user_id',
                'filtered_schedules.sub_classroom',
                'filtered_schedules.sub_classroom_id',
                'filtered_schedules.created_at',
                'filtered_schedules.updated_at'
            )
            ->where('school_id', $activeSchoolId)
            ->orderBy('sp.period', 'asc')
            ->get();
    }

    public function getSchedulesByTeacher(int $teacherUserId, int $day)
    {
        $activeSchoolId = session('active_school_id');
        return DB::table('schedule_periods as sp')
            ->leftJoin(DB::raw('
            (
                SELECT
                    s.id,
                    s.schedule_period_id,
                    s.created_at,
                    s.updated_at,
                    scs.sub_classroom_id,
                    sc.name as sub_classroom,
                    scs.subject_id,
                    scs.teacher_user_id,
                    subjects.name AS subject,
                    users.name AS teacher
                FROM schedules s
                JOIN sub_classroom_subjects scs ON s.sub_classroom_subject_id = scs.id
                JOIN subjects ON subjects.id = scs.subject_id
                JOIN (
                    SELECT sc.id, CONCAT(c.name, \' - \', sc.sequence) as name
                    FROM sub_classrooms as sc
                    JOIN classrooms as c ON sc.classroom_id = c.id
                ) AS sc on sc.id = scs.sub_classroom_id
                LEFT JOIN users ON users.id = scs.teacher_user_id
                WHERE s.day = ' . (int)$day . ' AND scs.teacher_user_id = ' . (int)$teacherUserId . '
            ) as filtered_schedules
        '), 'filtered_schedules.schedule_period_id', '=', 'sp.id')
            ->where('school_id', $activeSchoolId)
            ->select(
                'sp.id as period_id',
                'sp.period',
                DB::raw("TO_CHAR(sp.start_time, 'HH24:MI') as start_time"),
                DB::raw("TO_CHAR(sp.end_time, 'HH24:MI') as end_time"),
                'filtered_schedules.id',
                'filtered_schedules.subject',
                'filtered_schedules.subject_id',
                'filtered_schedules.teacher',
                'filtered_schedules.teacher_user_id',
                'filtered_schedules.sub_classroom',
                'filtered_schedules.sub_classroom_id',
                'filtered_schedules.created_at',
                'filtered_schedules.updated_at'
            )
            ->orderBy('sp.period', 'asc')
            ->get();
    }

    public function getStudentCurrentSchedule(int $subClassroomId, int $day)
    {
        return Schedule::with(['schedulePeriod', 'subClassroomSubject.subClassroom'])
            ->where('day', $day)
            ->whereHas('subClassroomSubject', fn($q) => $q->where('sub_classroom_id', $subClassroomId))
            ->whereHas('schedulePeriod', fn($q) => $q->whereTime('start_time', '<=', now())->whereTime('end_time', '>=', now()))
            ->first();
    }

    public function getTeacherCurrentSchedule(int $teacherUserId, int $day)
    {
        return Schedule::with(['schedulePeriod', 'subClassroomSubject.subClassroom'])
            ->where('day', $day)
            ->whereHas('subClassroomSubject', fn($q) => $q->where('teacher_user_id', $teacherUserId))
            ->whereHas('schedulePeriod', fn($q) => $q->whereTime('start_time', '<=', now())->whereTime('end_time', '>=', now()))
            ->first();
    }

    public function getStudentAttendances(int $scheduleId, ?string $date)
    {
        $subquery = DB::table('schedules as s')
            ->join('sub_classroom_subjects as scs', 's.sub_classroom_subject_id', '=', 'scs.id')
            ->select('s.id as schedule_id', 'scs.sub_classroom_id');

        $query = User::withoutGlobalScopes()
            ->from('users as u')
            ->leftJoinSub($subquery, 'schedule', function ($join) {
                $join->on('schedule.sub_classroom_id', '=', 'u.sub_classroom_id');
            })
            ->leftJoin('attendances as a', function ($join) use ($date) {
                $join->on('a.schedule_id', '=', 'schedule.schedule_id')
                    ->whereColumn('a.user_id', '=', 'u.id');

                if ($date) {
                    $join->whereDate('a.checked_in_at', $date);
                }
            })
            // 🔽 Join role_user and roles to filter by role name
            ->join('user_school_roles as ru', 'ru.user_id', '=', 'u.id')
            ->where('ru.school_id', session('active_school_id'))
            ->join('roles as r', 'r.id', '=', 'ru.role_id')
            ->where('r.name', 'student') // 👈 Pass desired role name here
            ->where('schedule.schedule_id', $scheduleId)
            ->whereNull('u.deleted_at') // Soft delete manual check
            ->orderBy('u.name', 'asc')
            ->select([
                'u.id as user_id',
                'u.name',
                'a.is_present',
                'a.notes',
                'a.checked_in_at',
                'a.created_at',
                'a.updated_at',
            ])
            ->get();

        return $query;
    }

    public function createSchedule(array $data)
    {
        $subClassroomSubject = SubClassroomSubject::where('sub_classroom_id', $data['sub_classroom_id'])
            ->where('subject_id', $data['subject_id'])
            ->first();

        if ($data['teacher_user_id']) {
            $subClassroomSubject->teacher_user_id = $data['teacher_user_id'];
            $subClassroomSubject->save();
        }

        return Schedule::create([
            'day' => $data['day'],
            'schedule_period_id' => $data['schedule_period_id'],
            'sub_classroom_subject_id' => $subClassroomSubject->id
        ]);
    }

    public function updateSchedule(Schedule $schedule, array $data)
    {
        $subClassroomSubject = SubClassroomSubject::where('sub_classroom_id', $data['sub_classroom_id'])
            ->where('subject_id', $data['subject_id'])
            ->first();

        if ($data['teacher_user_id']) {
            $subClassroomSubject->teacher_user_id = $data['teacher_user_id'];
            $subClassroomSubject->save();
        }
        return $schedule->update([
            'day' => $data['day'],
            'schedule_period_id' => $data['schedule_period_id'],
            'sub_classroom_subject_id' => $subClassroomSubject->id
        ]);
    }

    public function deleteSchedule(Schedule $schedule)
    {
        return $schedule->delete();
    }
}
