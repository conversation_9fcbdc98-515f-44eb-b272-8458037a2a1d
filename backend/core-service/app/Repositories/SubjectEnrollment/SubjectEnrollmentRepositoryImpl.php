<?php

namespace App\Repositories\SubjectEnrollment;

use App\Models\Role;
use App\Models\SubClassroomSubject;
use App\Models\SubClassroomSubjectHasStudent;
use App\Models\User;

class SubjectEnrollmentRepositoryImpl implements SubjectEnrollmentRepository
{
    public function getMatrix(int $termId, int $subClassroomId): array
    {
        $schoolId = null;
        if (function_exists('app') && app()->bound('active_school_id')) {
            $schoolId = app('active_school_id');
        } elseif (request()->attributes->has('active_school_id')) {
            $schoolId = request()->attributes->get('active_school_id');
        }
        $studentsQ = User::query()
            ->select('users.id', 'users.name')
            ->join('user_school_roles as usr', 'usr.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'usr.role_id')
            ->where('roles.name', 'student')
            ->where('users.sub_classroom_id', $subClassroomId)
            ->whereNull('users.deleted_at');

        if (!empty($schoolId)) {
            $studentsQ->where('usr.school_id', $schoolId);
        }

        $students = $studentsQ
            ->distinct()
            ->orderBy('users.name')
            ->get();

        $subjects = SubClassroomSubject::query()
            ->select('id', 'subject_id')
            ->with('subject:id,name')
            ->where('sub_classroom_id', $subClassroomId)
            ->orderBy('id')
            ->get();

        if ($students->isEmpty() || $subjects->isEmpty()) {
            return ['students' => [], 'subjects' => [], 'matrix' => []];
        }

        $enrollments = SubClassroomSubjectHasStudent::query()
            ->where('term_id', $termId)
            ->whereIn('sub_classroom_subject_id', $subjects->pluck('id'))
            ->whereIn('user_id', $students->pluck('id'))
            ->get()
            ->keyBy(fn($e) => "{$e->sub_classroom_subject_id}:{$e->user_id}");

        if ($enrollments->isEmpty()) {
            return ['students' => [], 'subjects' => [], 'matrix' => []];
        }

        $matrix = [];
        foreach ($students as $stu) {
            $row = [
                'user_id' => $stu->id,
                'student_name'    => $stu->name,
                'subjects'        => [],
            ];
            foreach ($subjects as $subj) {
                $key = "{$subj->id}:{$stu->id}";
                $row['subjects'][] = [
                    'sub_classroom_subject_id' => $subj->id,
                    'subject_name'             => $subj->subject?->name,
                    'is_enrolled'              => (bool)($enrollments[$key]->is_enrolled ?? false),
                ];
            }
            $matrix[] = $row;
        }

        return [
            'students' => $students,
            'subjects' => $subjects->map(fn($s) => (object)[
                'id'   => $s->id,
                'name' => $s->subject?->name,
            ]),
            'matrix'   => $matrix,
        ];
    }


    public function upsertSingle(array $payload): void
    {
        $termId   = (int) ($payload['term_id'] ?? $payload['termId'] ?? 0);
        $subjId   = (int) ($payload['sub_classroom_subject_id'] ?? $payload['subClassroomSubjectId'] ?? 0);
        $userId   = (int) ($payload['user_id'] ?? $payload['userId'] ?? 0);
        $isEnrolled = (bool) ($payload['is_enrolled'] ?? $payload['isEnrolled'] ?? false);

        $now = now();

        SubClassroomSubjectHasStudent::upsert([[
            'term_id'                   => $termId,
            'sub_classroom_subject_id'  => $subjId,
            'user_id'                   => $userId,
            'is_enrolled'               => $isEnrolled,
            'enrolled_at'               => $isEnrolled ? $now : null,
            'unenrolled_at'             => $isEnrolled ? null : $now,
            'updated_at'                => $now,
            'created_at'                => $now,
        ]], ['term_id', 'sub_classroom_subject_id', 'user_id'], [
            'is_enrolled',
            'enrolled_at',
            'unenrolled_at',
            'updated_at',
        ]);
    }

    public function upsertBulk(array $payloads): void
    {
        $now = now();
        $rows = array_map(function ($r) use ($now) {
            $is = (bool) ($r['is_enrolled'] ?? $r['isEnrolled'] ?? false);
            return [
                'term_id'                  => (int) $r['term_id'],
                'sub_classroom_subject_id' => (int) ($r['sub_classroom_subject_id'] ?? $r['subClassroomSubjectId']),
                'user_id'                  => (int) ($r['user_id'] ?? $r['userId']),
                'is_enrolled'              => $is,
                'enrolled_at'              => $is ? $now : null,
                'unenrolled_at'            => $is ? null : $now,
                'created_at'               => $now,
                'updated_at'               => $now,
            ];
        }, $payloads);

        SubClassroomSubjectHasStudent::upsert(
            $rows,
            ['term_id', 'sub_classroom_subject_id', 'user_id'],
            ['is_enrolled', 'enrolled_at', 'unenrolled_at', 'updated_at']
        );
    }
}
