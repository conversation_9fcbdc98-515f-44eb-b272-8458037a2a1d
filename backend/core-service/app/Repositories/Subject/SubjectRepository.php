<?php

namespace App\Repositories\Subject;

use App\Models\Subject;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

interface SubjectRepository
{
    public function paginate(Request $request);

    public function store(array $data): Subject;

    public function show(Subject $subject): Subject;

    public function update(Subject $subject, array $data): Subject;

    public function delete(Subject $subject): void;

    public function getAssignedSubjects(int $teacherUserId, Request $request);

    public function import(Request $request, ?int $schoolId = null): array;

    public function export(): BinaryFileResponse;
}
