<?php

namespace App\Repositories\Subject;

use App\Exports\SubjectExport;
use App\Imports\Subject\SubjectsImport;
use App\Models\SubClassroomSubject;
use App\Models\Subject;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class SubjectRepositoryImpl implements SubjectRepository
{
    public function store(array $data): Subject
    {
        return DB::transaction(fn() => Subject::create($data));
    }

    public function show(Subject $subject): Subject
    {
        return $subject;
    }

    public function update(Subject $subject, array $data): Subject
    {
        return DB::transaction(function () use ($subject, $data) {
            $subject->update($data);
            return $subject;
        });
    }

    public function delete(Subject $subject): void
    {
        DB::transaction(fn() => $subject->delete());
    }

    public function getAssignedSubjects(int $teacherUserId, Request $request): LengthAwarePaginator
    {
        $user  = $request->user();

        $query = SubClassroomSubject::with(['subClassroom', 'subject', 'teacher']);

        Log::info('Assigned subjects query', [
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings(),
        ]);

        $isAdmin =
            (method_exists($user, 'hasRole') && ($user->hasRole('school_admin') || $user->hasRole('foundation_admin') || $user->hasRole('superadmin')));

        if (!$isAdmin) {
            $query->where('teacher_user_id', $teacherUserId);
        }

        if ($request->filled('sub_classroom_id')) {
            $query->where('sub_classroom_id', (int) $request->input('sub_classroom_id'));
        }

        return $query->paginate($request->integer('limit') ?? config('app.pagination.max'));
    }

    public function paginate(Request $request)
    {
        $limit = $request->limit ?? config('app.pagination.max');
        
        $query = Subject::query();
        
        // Apply search filter if provided
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('description', 'like', '%' . $searchTerm . '%');
            });
        }
        
        // Apply school filter if user is in a specific school context
        if ($request->user() && !$request->user()->isSuperadmin) {
            $schoolId = $request->header('X-School-ID');
            if ($schoolId) {
                $query->where('school_id', $schoolId);
            }
        }
        
        return $query->orderBy('priority', 'asc')
                    ->orderBy('name', 'asc')
                    ->paginate($limit);
    }

    public function import(Request $request, ?int $schoolId = null): array
    {
        // Validate that we have a school ID
        if (!$schoolId) {
            throw new \InvalidArgumentException('School ID is required for import');
        }
        
        // Pass the school ID explicitly to the import class
        Excel::import(new SubjectsImport($schoolId), $request->file('file'));
        return ['status' => true, 'message' => 'Import data subject berhasil'];
    }

    public function export(): BinaryFileResponse
    {
        return Excel::download(new SubjectExport, 'mata_pelajaran_expor_' . now()->format('Ymd_His') . '.xlsx');
    }
}
