<?php

namespace App\Repositories\FinancialCore;

use App\Models\FinancialCore;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class FinancialCoreRepository
{
    /**
     * Get paginated financial core records with filters.
     */
    public function paginate(Request $request): LengthAwarePaginator
    {
        return FinancialCore::query()
            ->with(['student', 'parent', 'school'])
            ->when($request->has('school_id'), fn(Builder $q) => $q->where('school_id', $request->school_id))
            ->when($request->has('student_user_id'), fn(Builder $q) => $q->where('student_user_id', $request->student_user_id))
            ->when($request->has('parent_user_id'), fn(Builder $q) => $q->where('parent_user_id', $request->parent_user_id))
            ->when($request->has('reference_type'), fn(Builder $q) => $q->where('reference_type', $request->reference_type))
            ->when($request->has('status'), fn(Builder $q) => $q->where('status', $request->status))
            ->when($request->has('date_from'), fn(Builder $q) => $q->whereDate('transaction_date', '>=', $request->date_from))
            ->when($request->has('date_to'), fn(Builder $q) => $q->whereDate('transaction_date', '<=', $request->date_to))
            ->when($request->has('search'), function (Builder $q) use ($request) {
                $q->where(function (Builder $subQ) use ($request) {
                    $subQ->where('transaction_number', 'like', "%{$request->search}%")
                        ->orWhere('notes', 'like', "%{$request->search}%")
                        ->orWhere('reference_id', 'like', "%{$request->search}%");
                });
            })
            ->orderBy('transaction_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }



    /**
     * Find record by transaction number.
     */
    public function findByTransactionNumber(string $transactionNumber): ?FinancialCore
    {
        return FinancialCore::where('transaction_number', $transactionNumber)->first();
    }

    /**
     * Store new financial core record.
     */
    public function store(array $data): FinancialCore
    {
        return FinancialCore::create($data);
    }
}