<?php

namespace App\Repositories\Event;

use App\Enums\EventFileType;
use App\Models\Event;
use App\Models\EventFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\Event\StoreEventRequest;
use App\Http\Requests\Event\UpdateEventRequest;

class EventRepositoryImpl implements EventRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        $limit = min(max((int) $request->input('limit', config('app.pagination.max')), 1), config('app.pagination.max'));

        $query = Event::visibleFor(auth()->user())
            ->with(['files' => function ($q) use ($request) {
                if ($this->shouldIncludeTrashedFiles($request)) {
                    $q->withTrashed();
                }
            }]);

        if ($this->shouldIncludeTrashedEvents($request)) {
            $query->withTrashed();
        }

        $filters = [
            'school_id' => fn($q, $v) => $q->where('school_id', $v),
            'is_published' => fn($q, $v) => $q->where('is_published', (bool) $v),
            'start_date' => fn($q, $v) => $q->whereDate('event_date', '>=', $v),
            'end_date' => fn($q, $v) => $q->whereDate('event_date', '<=', $v),
        ];

        foreach ($filters as $key => $callback) {
            if ($request->filled($key)) {
                $callback($query, $request->input($key));
            }
        }

        if ($request->filled('search')) {
            $query->where(
                fn($q) => $q
                    ->where('title', 'like', "%{$request->search}%")
                    ->orWhere('content', 'like', "%{$request->search}%")
            );
        }

        return $query->orderByDesc('event_date')->orderByDesc('created_at')->paginate($limit);
    }

    public function store(StoreEventRequest $request): Event
    {
        $validated = $request->validated();

        return DB::transaction(function () use ($request, $validated) {
            $eventData = $this->extractEventData($validated);
            $eventData['created_by'] = auth()->id();

            if ($request->hasFile('thumbnail_file')) {
                $eventData['thumbnail_file'] = $this->uploadFile($request->file('thumbnail_file'), 'events/thumbnails');
            }

            $event = Event::create($eventData);

            foreach ($request->file('files', []) as $file) {
                $this->uploadEventFile($event, $file);
            }

            return $event->load('files');
        });
    }

    public function show(Event $model): Event
    {
        $user = auth()->user();

        $canSee = Event::visibleFor($user)
            ->where('id', $model->id)
            ->exists();

        if (!$canSee) {
            abort(403, 'You are not allowed to view this event.');
        }

        $withTrashed = $this->shouldIncludeTrashedFiles(request());
        return $model->load(['files' => fn($q) => $withTrashed ? $q->withTrashed() : $q]);
    }


    public function update(Event $model, UpdateEventRequest $request): Event
    {
        $validated = $request->validated();

        return DB::transaction(function () use ($model, $request, $validated) {
            $eventData = $this->extractEventData($validated, $model);
            $eventData['updated_by'] = auth()->id();

            if ($request->hasFile('thumbnail_file')) {
                $this->deleteStoredFile($model->thumbnail_file);
                $eventData['thumbnail_file'] = $this->uploadFile($request->file('thumbnail_file'), 'events/thumbnails');
            } elseif ($request->boolean('clear_thumbnail')) {
                $this->deleteStoredFile($model->thumbnail_file);
                $eventData['thumbnail_file'] = null;
            }

            $model->update($eventData);

            foreach ($request->input('deleted_files', []) as $fileId) {
                $eventFile = $this->getEventFileById($model, $fileId, $request);

                if ($eventFile) {
                    $this->deleteStoredFile($eventFile->file_path);
                    $eventFile->forceDelete();
                }
            }

            foreach ($request->file('files', []) as $file) {
                $this->uploadEventFile($model, $file);
            }

            return $model->load('files');
        });
    }

    public function delete(Event $model): void
    {
        DB::transaction(function () use ($model) {
            $this->deleteStoredFile($model->thumbnail_file);

            foreach ($model->files as $file) {
                $file->update(['deleted_by' => auth()->id()]);
                $this->deleteStoredFile($file->file_path);
                $file->delete();
            }

            $model->update(['deleted_by' => auth()->id()]);
            $model->delete();
        });
    }

    public function restore(Event $model): Event
    {
        return DB::transaction(function () use ($model) {
            $model->restore();
            $model->files()->onlyTrashed()->restore();
            $model->update(['deleted_by' => null, 'updated_by' => auth()->id()]);
            return $model->load('files');
        });
    }

    public function forceDelete(Event $model): void
    {
        if (!$model->trashed()) {
            throw new \Exception("Event must be soft-deleted before force deleting.");
        }

        DB::transaction(function () use ($model) {
            $this->deleteStoredFile($model->thumbnail_file);

            foreach ($model->files()->withTrashed()->get() as $file) {
                $this->deleteStoredFile($file->file_path);
                $file->forceDelete();
            }

            $model->forceDelete();
        });
    }

    private function uploadFile(UploadedFile $file, string $path): string
    {
        return $file->store($path, 'public');
    }

    private function uploadEventFile(Event $event, UploadedFile $file): EventFile
    {
        return $event->files()->create([
            'file_path'  => $this->uploadFile($file, 'events/content_files'),
            'file_type'  => $this->getFileType($file->getMimeType()),
            'created_by' => auth()->id(),
        ]);
    }

    private function getFileType(string $mimeType): string
    {
        return match (true) {
            str_starts_with($mimeType, 'image/') => EventFileType::IMAGE->value,
            str_starts_with($mimeType, 'video/') => EventFileType::VIDEO->value,
            default => EventFileType::OTHER->value,
        };
    }

    private function deleteStoredFile(?string $filePath): void
    {
        if (!$filePath) {
            Log::warning("deleteStoredFile(): filePath is null or empty");
            return;
        }

        if (!Storage::disk('public')->exists($filePath)) {
            Log::warning("File not found in storage: $filePath");
            return;
        }

        if (!Storage::disk('public')->delete($filePath)) {
            Log::error("Failed to delete file from storage: $filePath");
        } else {
            Log::info("File successfully deleted: $filePath");
        }
    }

    private function shouldIncludeTrashedFiles(Request $request): bool
    {
        return $request->boolean('with_trashed_files') && auth()->user()?->is_superadmin;
    }

    private function shouldIncludeTrashedEvents(Request $request): bool
    {
        return $request->boolean('with_trashed_events') && auth()->user()?->is_superadmin;
    }

    private function extractEventData(array $validated, ?Event $model = null): array
    {
        return [
            'school_id'    => $validated['school_id'] ?? $model?->school_id,
            'title'        => $validated['title'] ?? $model?->title,
            'excerpt'      => $validated['excerpt'] ?? $model?->excerpt,
            'content'      => $validated['content'] ?? $model?->content,
            'location'     => $validated['location'] ?? $model?->location,
            'event_date'   => !empty($validated['event_date']) ? $validated['event_date'] : $model?->event_date,
            'start_time'   => !empty($validated['start_time']) ? $validated['start_time'] : $model?->start_time,
            'end_time'     => !empty($validated['end_time']) ? $validated['end_time'] : $model?->end_time,
            'is_published' => $validated['is_published'] ?? $model?->is_published,
        ];
    }

    private function getEventFileById(Event $event, $fileId, Request $request): ?EventFile
    {
        return $event->files()->find($fileId)
            ?? ($this->shouldIncludeTrashedFiles($request) ? $event->files()->withTrashed()->find($fileId) : null);
    }
}
