<?php

namespace App\Repositories\ExamToken;

use App\Models\ExamToken;
use Illuminate\Http\Request;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\ExamToken\StoreExamTokenRequest;
use App\Http\Requests\ExamToken\UpdateExamTokenRequest;

interface ExamTokenRepository
{
    public function paginate(Request $request): LengthAwarePaginator;
    public function store(StoreExamTokenRequest $request): ExamToken;
    public function show(ExamToken $model): ExamToken;
    public function update(ExamToken $model, UpdateExamTokenRequest $request): ExamToken;
    public function delete(ExamToken $model): void;

    public function updateTokenForUser(int $userId, string $examToken, string $expiration);
    public function getStudentsBySchoolId(int $schoolId);
    public function getAllUsers();
}
