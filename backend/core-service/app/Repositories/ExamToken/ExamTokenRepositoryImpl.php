<?php

namespace App\Repositories\ExamToken;

use App\Models\ExamToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Requests\ExamToken\StoreExamTokenRequest;
use App\Http\Requests\ExamToken\UpdateExamTokenRequest;
use App\Models\User;
use Carbon\Carbon;
use Exception;

class ExamTokenRepositoryImpl implements ExamTokenRepository
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return ExamToken::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreExamTokenRequest $request): ExamToken
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();
            $created = ExamToken::create($validated);
            return $created;
        });
    }

    public function show(ExamToken $model): ExamToken
    {
        return $model;
    }

    public function update(ExamToken $model, UpdateExamTokenRequest $request): ExamToken
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();
            $model->update($validated);
            return $model;
        });
    }

    public function delete(ExamToken $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    /**
     * Update the token and expiration for a user.
     */
    public function updateTokenForUser(int $userId, string $examToken, string $expiration)
    {
        try {
            $expiration = Carbon::parse($expiration);

            $user = User::findOrFail($userId);
            $user->exam_token = $examToken;
            $user->exam_token_expiration = $expiration;
            $user->save();
        } catch (Exception $e) {
            throw new Exception('Failed to update token for user: ' . $e->getMessage());
        }
    }

    public function getStudentsBySchoolId(int $schoolId)
    {
        try {
            $studentIds = \DB::table('user_school_roles')
                ->join('roles', 'roles.id', '=', 'user_school_roles.role_id')
                ->where('roles.name', 'student')
                ->where('user_school_roles.school_id', $schoolId)
                ->pluck('user_school_roles.user_id');

            return User::whereIn('id', $studentIds)->get();
        } catch (\Exception $e) {
            throw new \Exception('Failed to retrieve students: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get all users from the database.
     */
    public function getAllUsers()
    {
        try {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'student');
            })->get();
        } catch (Exception $e) {
            throw new Exception('Failed to retrieve users: ' . $e->getMessage());
        }
    }
}
