<?php

namespace App\Repositories\Term;

use App\Models\Term;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface TermRepository
{
    public function paginate(array $query): LengthAwarePaginator;
    public function store(array $data): Term;
    public function show(Term $model): Term;
    public function update(Term $model, array $data): Term;
    public function delete(Term $model): void;
}
