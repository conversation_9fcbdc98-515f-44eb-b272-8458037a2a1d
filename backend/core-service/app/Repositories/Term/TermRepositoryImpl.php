<?php

namespace App\Repositories\Term;

use App\Models\Term;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class TermRepositoryImpl implements TermRepository
{
    public function paginate(array $query): LengthAwarePaginator
    {
        $limit    = $query['limit']      ?? config('app.pagination.max');
        $schoolId = $query['school_id'] ?? optional(auth()->user())->school_id;

        return Term::query()
            ->with('academicYear:id,school_id,name,start_date,end_date,is_current')
            ->whereHas('academicYear', function ($ay) use ($schoolId) {
                if ($schoolId) {
                    $ay->where('school_id', (int) $schoolId);
                }
            })
            ->orderByDesc('start_date')
            ->orderByDesc('id')
            ->paginate($limit);
    }


    public function store(array $data): Term
    {
        return DB::transaction(function () use ($data) {
            $created = Term::create($data);
            return $created;
        });
    }

    public function show(Term $model): Term
    {
        return $model;
    }

    public function update(Term $model, array $data): Term
    {
        return DB::transaction(function () use ($model, $data) {
            $model->update($data);
            return $model;
        });
    }

    public function delete(Term $model): void
    {
        DB::transaction(fn() => $model->delete());
    }
}
