<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UniqueWithoutTrashed implements ValidationRule
{
    /**
     * The table to check.
     *
     * @var string
     */
    protected $table;

    /**
     * The column to check.
     *
     * @var string
     */
    protected $column;

    /**
     * Ignore a given ID during the unique check.
     *
     * @var mixed
     */
    protected $ignore;

    /**
     * Create a new rule instance.
     *
     * @param  string  $table
     * @param  string  $column
     * @param  mixed  $ignore
     * @return void
     */
    public function __construct(string $table, string $column = 'NULL', $ignore = null)
    {
        $this->table = $table;
        $this->column = $column;
        $this->ignore = $ignore;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $query = DB::table($this->table)
            ->where($this->column, $value)
            ->whereNull('deleted_at');

        if ($this->ignore) {
            $query->where('id', '!=', $this->ignore);
        }

        if ($query->exists()) {
            $fail("The $attribute has already been taken.");
        }
    }
}
