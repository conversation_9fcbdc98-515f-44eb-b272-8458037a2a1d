<?php

namespace App\Rules;

use App\Models\Assignment;
use App\Models\Exam;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class MatchesParentTerm implements ValidationRule
{
    public function __construct(private ?int $examId, private ?int $assignmentId) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$value) return; // jika grade.term_id null, biarkan service mengisi otomatis

        if ($this->examId) {
            $termId = Exam::whereKey($this->examId)->value('term_id');
            if ($termId && (int)$value !== (int)$termId) {
                $fail('term_id tidak cocok dengan term ujian.');
            }
        }

        if ($this->assignmentId) {
            $termId = Assignment::whereKey($this->assignmentId)->value('term_id');
            if ($termId && (int)$value !== (int)$termId) {
                $fail('term_id tidak cocok dengan term tugas.');
            }
        }
    }
}
