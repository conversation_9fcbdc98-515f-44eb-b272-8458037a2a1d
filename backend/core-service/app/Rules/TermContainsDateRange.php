<?php

namespace App\Rules;

use App\Models\Term;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Carbon;

class TermContainsDateRange implements ValidationRule
{
    public function __construct(private string $startField, private string $endField) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $term = Term::find($value);
        if (!$term) { $fail('Term not found.'); return; }

        $start = Carbon::parse(request()->input($this->startField));
        $end   = Carbon::parse(request()->input($this->endField));
        if ($end->lt($start)) { $fail('End datetime must be >= start datetime.'); return; }

        if ($start->lt($term->start_date->startOfDay()) || $end->gt($term->end_date->endOfDay())) {
            $fail('Exam time range is not within the term.');
        }
    }
}
