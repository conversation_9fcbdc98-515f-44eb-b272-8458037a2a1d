<?php

namespace App\Rules;

use App\Models\Term;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TermContainsDate implements ValidationRule
{
    public function __construct(private string $dateFieldName) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // use English

        $term = Term::find($value);
        if (!$term) {
            $fail('Term not found.');
            return;
        }

        $date = request()->input($this->dateFieldName);
        if (!$date) return;

        $d = \Illuminate\Support\Carbon::parse($date);
        if ($d->lt($term->start_date->startOfDay()) || $d->gt($term->end_date->endOfDay())) {
            $fail("Date {$this->dateFieldName} is not within the term.");
        }
    }
}
