<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell; // ⬅️ tambah
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

class ClassAttendanceTemplateExport implements
    FromCollection,
    WithHeadings,
    WithMapping,
    ShouldAutoSize,
    WithEvents,
    WithTitle,
    WithCustomStartCell // ⬅️ tambah
{
    public function __construct(
        protected int $termId,
        protected ?int $subClassroomId = null,
    ) {}

    /* ====== Header mulai di baris 2 ====== */
    public function startCell(): string
    {
        return 'A2'; // ⬅️ header & data dimulai dari A2
    }

    /* ====== Data ====== */
    public function collection(): Collection
    {
        return $this->getStudentsBySubClassroom($this->subClassroomId);
    }

    public function headings(): array
    {
        // Baris 2 (karena startCell A2)
        return ['Semester/Tahun ID', 'ID Siswa', 'Nama Siswa', 'Sakit', 'Izin', 'Hadir', 'Tidak Hadir'];
    }

    public function map($row): array
    {
        return [
            $this->termId,
            (int) $row->student_user_id,
            (string) $row->student_name,
            0, // Sakit
            0, // Izin
            0, // Hadir
            0, // Tidak Hadir
        ];
    }

    /* ====== Styling & UX ====== */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // 1) Baris petunjuk di A1:G1 (aman, header ada di baris 2)
                $sheet->mergeCells('A1:G1');
                $sheet->setCellValue(
                    'A1',
                    "Template Absensi: • Jangan ubah kolom A–C • Isi hanya Sakit/Izin/Hadir/Tidak Hadir (angka ≥ 0) • " .
                        "Baris dengan D+E+F+G = 0 akan dilewati saat import."
                );
                $sheet->getStyle('A1')->getFont()->setBold(true);
                $sheet->getStyle('A1')->getAlignment()->setWrapText(true);

                // 2) Styling header (row 2)
                $sheet->getStyle('A2:G2')->getFont()->setBold(true)->setColor(
                    new \PhpOffice\PhpSpreadsheet\Style\Color('FFFFFFFF')
                );
                $sheet->getStyle('A2:G2')->getFill()->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('FF374151'); // abu gelap
                $sheet->freezePane('A3'); // freeze setelah header

                // 3) Autofilter + border
                $highestRow = max(2, $sheet->getHighestRow());
                $sheet->setAutoFilter("A2:G{$highestRow}");
                $sheet->getStyle("A2:G{$highestRow}")->getBorders()->getAllBorders()
                    ->setBorderStyle(Border::BORDER_THIN)
                    ->getColor()->setARGB('FFCCCCCC');

                // 4) Validasi angka ≥ 0 untuk kolom D–G (baris 3..akhir)
                if ($highestRow >= 3) {
                    foreach (['D', 'E', 'F', 'G'] as $col) {
                        $dv = $sheet->getCell("{$col}3")->getDataValidation();
                        $dv->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_WHOLE);
                        $dv->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_STOP);
                        $dv->setAllowBlank(true);
                        $dv->setShowInputMessage(true);
                        $dv->setShowErrorMessage(true);
                        $dv->setOperator(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::OPERATOR_GREATERTHANOREQUAL);
                        $dv->setFormula1('0');
                        $dv->setErrorTitle('Input tidak valid');
                        $dv->setError('Masukkan bilangan bulat ≥ 0.');
                        $dv->setPromptTitle('Isi angka');
                        $dv->setPrompt('Masukkan bilangan bulat ≥ 0.');
                        $sheet->setDataValidation("{$col}3:{$col}{$highestRow}", $dv);
                    }
                }
            },
        ];
    }

    public function title(): string
    {
        return 'Template Absensi';
    }

    /* ====== Source roster ====== */
    protected function getStudentsBySubClassroom(?int $subClassroomId): Collection
    {
        if (empty($subClassroomId) || (int)$subClassroomId <= 0) {
            return collect();
        }


        $schoolId = null;
        if (function_exists('app') && app()->bound('active_school_id')) {
            $schoolId = app('active_school_id');
        } elseif (request()->attributes->has('active_school_id')) {
            $schoolId = request()->attributes->get('active_school_id');
        }

        $q = DB::table('users as u')
            ->join('user_school_roles as usr', 'usr.user_id', '=', 'u.id')
            ->join('roles as r', 'r.id', '=', 'usr.role_id')
            ->where('r.name', 'student')
            ->whereNull('u.deleted_at');

        if (!empty($subClassroomId) && $subClassroomId > 0) {
            $q->where('u.sub_classroom_id', $subClassroomId);
        }
        if (!empty($schoolId)) {
            $q->where('usr.school_id', $schoolId);
        }

        return $q->select('u.id as student_user_id', 'u.name as student_name')
            ->distinct()
            ->orderBy('u.name')
            ->get();
    }
}
