<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Models\UserSchoolRole;
use Illuminate\Support\Facades\Auth;

class StudentsExport implements WithMultipleSheets
{
    public function __construct(
        protected string $preset = 'students_all' // 'students_all' | 'exam_token'
    ) {}

    public function sheets(): array
    {
        $school = Auth::user()?->schools->first();
        $schoolId = $school?->id;

        if (!$schoolId) {
            abort(403, 'User tidak terkait sekolah.');
        }

        $studentUserIds = UserSchoolRole::where('school_id', $schoolId)
            ->whereHas('role', fn($q) => $q->where('name', 'student'))
            ->pluck('user_id');

        $students = User::with(['subClassroom', 'academicYear'])
            ->whereIn('id', $studentUserIds)
            ->get();

        $byClass = $students->groupBy(fn($u) => $u->getSubClassroomName() ?? '-')->sortKeys();

        $sheets = [];
        foreach ($byClass as $className => $classStudents) {
            $sheets[] = new StudentsClassSheet(
                className: (string) $className,
                students: $classStudents->values(),
                preset: $this->preset
            );
        }

        if (empty($sheets)) {
            $sheets[] = new StudentsClassSheet('-', collect(), $this->preset);
        }

        return $sheets;
    }
}
