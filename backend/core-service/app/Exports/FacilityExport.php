<?php

namespace App\Exports;

use App\Models\Facility;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Imports\HeadingRowFormatter as ImportsHeadingRowFormatter;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class FacilityExport implements FromCollection, WithHeadings, WithChunkReading, WithStyles
{
    public function __construct()
    {
        ImportsHeadingRowFormatter::default('none');
    }

    public function collection()
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        return Facility::all()->map(function ($facility) {
            return [
                'Nama Fasilitas' => $facility->name,
                'Stok' => $facility->stock,
                'Satuan' => $facility->unit,
                'Kondisi Baik' => $facility->good_condition,
                'Rusak Ringan' => $facility->minor_damage,
                'Rusak Berat' => $facility->major_damage,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Nama Fasilitas',
            'Stok',
            'Satuan',
            'Kondisi Baik',
            'Rusak Ringan',
            'Rusak Berat',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Styling header
        $sheet->getStyle('A1:F1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007ACC'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);

        return [];
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }
}
