<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Protection;

class NotesTemplateExport implements
    FromCollection,
    WithHeadings,
    WithCustomStartCell,
    WithEvents,
    WithColumnWidths,
    ShouldAutoSize,
    WithTitle
{
    /** @var \Illuminate\Support\Collection */
    protected Collection $rows;

    public function __construct(Collection $rows)
    {
        $this->rows = $rows;
    }

    public function startCell(): string
    {
        return 'A2';
    }

    public function title(): string
    {
        return 'Template Catatan';
    }

    public function headings(): array
    {
        return ['ID Siswa', 'Nama Siswa', 'Catatan'];
    }

    public function collection()
    {
        return $this->rows->map(fn($r) => [
            $r->student_user_id,
            $r->student_name,
            '',
        ]);
    }

    public function columnWidths(): array
    {
        return [
            'A' => 16,
            'B' => 28,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                $sheet->mergeCells('A1:C1');
                $sheet->setCellValue('A1', 'Isi kolom CATATAN untuk masing-masing siswa. Jangan ubah ID Siswa atau Nama Siswa.');
                $sheet->getStyle('A1')->getFont()->setBold(true);
                $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

                $sheet->getStyle('A2:C2')->applyFromArray([
                    'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '4F81BD'],
                    ],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
                ]);

                $highestRow = $sheet->getHighestRow();

                $sheet->getStyle("C3:C{$highestRow}")
                    ->getAlignment()->setWrapText(true);
            },
        ];
    }
}
