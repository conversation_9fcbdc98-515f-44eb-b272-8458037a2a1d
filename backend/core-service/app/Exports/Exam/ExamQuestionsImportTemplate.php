<?php

namespace App\Exports\Exam;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExamQuestionsImportTemplate implements FromCollection, WithHeadings, WithStyles, WithEvents, ShouldAutoSize
{
    /**
     * Headings final (13 kolom, A..M)
     */
    public function headings(): array
    {
        return [
            'No KD',                // A: kd_number
            'Konten KD',            // B: learning_outcome
            'Indikator Pencapaian', // C: competency_indicator
            'Level Kognitif',       // D: level_kognitif
            'Bentuk',               // E: Pilihan Ganda / Essay
            'Soal',                 // F: content
            'Opsi A',               // G
            'Opsi B',               // H
            'Opsi C',               // I
            'Opsi D',               // J
            'Opsi E',               // K
            'Jawaban(HURUF)',       // L: untuk PG
            'Jawaban(Esai)',        // M: untuk Essay
            'Poin',                 // N
        ];
    }

    /**
     * Dua baris contoh: Pilihan Ganda & Essay
     */
    public function collection()
    {
        return new Collection([
            // Pilihan Ganda
            [
                '3.2',
                'Memahami konsep X',
                'Menjelaskan konsep X',
                'C2',
                'Pilihan Ganda',
                'Apa itu X?',
                'Pilihan A',
                'Pilihan B',
                'Pilihan C',
                'Pilihan D',
                'Pilihan E',
                'B',
                '',
                1
            ],

            // Essay
            [
                '4.1',
                'Menjelaskan hubungan X–Y',
                'Menguraikan konsep Y',
                'C3',
                'Essay',
                'Jelaskan hubungan X dan Y!',
                '',
                '',
                '',
                '',
                '',
                '',
                'Hubungan X dan Y adalah ...',
                2
            ],
        ]);
    }

    /**
     * Styling umum (header, wrap text, alignment) + komentar penjelasan
     */
    public function styles(Worksheet $sheet)
    {
        // Header A1:M1
        $sheet->getStyle('A1:N1')->applyFromArray([
            'font'      => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill'      => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '1F4E79']],
            'borders'   => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => '000000']]],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Wrap text untuk kolom teks panjang
        foreach (['B', 'C', 'F', 'G', 'H', 'I', 'J', 'K', 'M'] as $col) {
            $sheet->getStyle($col)->getAlignment()->setWrapText(true);
        }

        // Align tengah untuk kolom label/angka
        foreach (['A', 'D', 'E', 'L', 'M', 'N'] as $col) {
            $sheet->getStyle($col)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        }

        // Komentar penjelasan per kolom (seperti "kalimat di atas tabel")
        $sheet->getComment('A1')->getText()->createTextRun("Nomor/Kode KD (mis. 3.2).");
        $sheet->getComment('B1')->getText()->createTextRun("Konten/uraian KD.");
        $sheet->getComment('C1')->getText()->createTextRun("Indikator pencapaian kompetensi.");
        $sheet->getComment('D1')->getText()->createTextRun("Level kognitif (C1..C6).");
        $sheet->getComment('E1')->getText()->createTextRun("Bentuk soal: pilih 'Pilihan Ganda' atau 'Essay'.");
        $sheet->getComment('F1')->getText()->createTextRun("Isi pertanyaan. Wajib diisi.");
        $sheet->getComment('G1')->getText()->createTextRun("Opsi A untuk Pilihan Ganda.");
        $sheet->getComment('H1')->getText()->createTextRun("Opsi B untuk Pilihan Ganda.");
        $sheet->getComment('I1')->getText()->createTextRun("Opsi C untuk Pilihan Ganda.");
        $sheet->getComment('J1')->getText()->createTextRun("Opsi D untuk Pilihan Ganda.");
        $sheet->getComment('K1')->getText()->createTextRun("Opsi E (opsional) untuk Pilihan Ganda.");
        $sheet->getComment('L1')->getText()->createTextRun("Kunci jawaban huruf (A/B/C/D/E) untuk Pilihan Ganda. Kosongkan untuk Essay.");
        $sheet->getComment('M1')->getText()->createTextRun("Kunci jawaban essay dari guru. Kosongkan jika tidak perlu.");
        $sheet->getComment('N1')->getText()->createTextRun("Bobot/poin soal (>= 0).");

        return [];
    }

    /**
     * Freeze header, border konten, validations (dropdown/list & numeric)
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Freeze header
                $sheet->freezePane('A2');

                // Border untuk seluruh range berisi data
                $lastRow = max(3, $sheet->getHighestRow());
                $sheet->getStyle("A1:N{$lastRow}")->applyFromArray([
                    'borders' => [
                        'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => '000000']]
                    ]
                ]);

                $this->applyListValidation($sheet, 'E', ['Pilihan Ganda', 'Essay']);

                $this->applyListValidation($sheet, 'L', ['A', 'B', 'C', 'D', 'E'], true);

                $this->applyNumericValidation($sheet, 'N');

                // Lebar minimal agar rapi (auto-size tetap aktif)
                foreach (['A' => 10, 'D' => 14, 'E' => 16, 'L' => 16, 'M' => 10, 'N' => 10] as $col => $minWidth) {
                    $w = $sheet->getColumnDimension($col)->getWidth();
                    if ($w < $minWidth) $sheet->getColumnDimension($col)->setWidth($minWidth);
                }
            },
        ];
    }

    private function applyListValidation(Worksheet $sheet, string $column, array $list, bool $allowBlank = false): void
    {
        $formula = '"' . implode(',', $list) . '"';
        for ($row = 2; $row <= 1000; $row++) {
            $valid = $sheet->getCell("{$column}{$row}")->getDataValidation();
            $valid->setType(DataValidation::TYPE_LIST);
            $valid->setErrorStyle(DataValidation::STYLE_STOP);
            $valid->setAllowBlank($allowBlank);
            $valid->setShowDropDown(true);
            $valid->setShowErrorMessage(true);
            $valid->setErrorTitle('Pilihan tidak valid');
            $valid->setError('Silakan pilih salah satu opsi yang tersedia.');
            $valid->setFormula1($formula);
        }
    }

    private function applyNumericValidation(Worksheet $sheet, string $column): void
    {
        for ($row = 2; $row <= 1000; $row++) {
            $valid = $sheet->getCell("{$column}{$row}")->getDataValidation();
            $valid->setType(DataValidation::TYPE_DECIMAL);
            $valid->setErrorStyle(DataValidation::STYLE_STOP);
            $valid->setAllowBlank(true);
            $valid->setShowErrorMessage(true);
            $valid->setErrorTitle('Angka tidak valid');
            $valid->setError('Nilai harus berupa angka >= 0');
            $valid->setOperator(DataValidation::OPERATOR_GREATERTHANOREQUAL);
            $valid->setFormula1('0');
        }
    }
}
