<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class QuestionsCardStackedExport implements WithEvents, ShouldAutoSize
{
    /**
     * Struktur rows:
     * [No, Tipe, Kode, Indikator, Soal, A, B, C, D, E, Kunci(HURUF), Model, Tingkat, Bobot, Catatan]
     */
    public function __construct(
        private string $sheetTitle,
        private string $schoolName,
        private string $classSemester,
        private string $subjectName,
        private string $teacherName,
        private string $academicYear,
        private array  $rows,
        private ?string $kompetensiKeahlian = null
    ) {}

    // Layout
    private const START_COL   = 'B'; // mulai di kolom B
    private const START_ROW   = 2;   // mulai di baris 2
    private const CARD_HEIGHT = 29;  // tinggi total satu kartu termasuk footer
    private const GAP         = 2;   // jarak antar kartu

    // Warna & Style
    private const BLUE = 'D9EEF7';

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $event->sheet->setTitle($this->sheetTitle);

                // Tampilan global
                $sheet->setShowGridlines(false);
                $sheet->getPageSetup()->setFitToWidth(1)->setFitToHeight(0);
                $sheet->getPageMargins()->setTop(0.4)->setBottom(0.4)->setLeft(0.4)->setRight(0.4);

                // Fokus B..H
                $this->setColumnWidthsBtoH($sheet);

                // Posisi awal
                $left   = self::START_COL;
                $rowTop = self::START_ROW;

                // ------- PASS 1: gambar kartu (LAYOUT TETAP) -------
                foreach ($this->rows as $r) {
                    [$no, $type, $codeTP, $indicator, $questions, $a, $b, $c, $d, $eOpt, $answerKeyOption, $answerKeyEssay, $cognitiveLevel,, $learningOutcome] = [
                        (int)($r[0] ?? 0),
                        (string)($r[1] ?? ''),
                        (string)($r[2] ?? ''),
                        (string)($r[3] ?? ''),
                        (string)($r[4] ?? ''),
                        (string)($r[5] ?? ''),
                        (string)($r[6] ?? ''),
                        (string)($r[7] ?? ''),
                        (string)($r[8] ?? ''),
                        (string)($r[9] ?? ''),
                        (string)($r[10] ?? ''),
                        (string)($r[11] ?? ''),
                        (string)($r[12] ?? ''),
                        (string)($r[13] ?? ''),
                        (string)($r[14] ?? ''),
                    ];

                    $this->drawCardBH(
                        s: $sheet,
                        left: $left,
                        top: $rowTop,
                        no: $no,
                        codeTP: $codeTP,
                        indicator: $indicator,
                        questions: $questions,
                        optA: $a,
                        optB: $b,
                        optC: $c,
                        optD: $d,
                        optE: $eOpt,
                        answerKeyOption: $answerKeyOption,
                        answerKeyEssay: $answerKeyEssay,
                        cognitiveLevel: $cognitiveLevel,
                        learningOutcome: $learningOutcome,
                        isEssay: strcasecmp($type, 'essay') === 0
                    );

                    // Geser ke kartu berikutnya
                    $rowTop += self::CARD_HEIGHT + self::GAP;
                }

                // ------- PASS 2: konversi teks → RichText (sup/sub/√/⁄/Greek) -------
                $rowTop = self::START_ROW;
                foreach ($this->rows as $r) {
                    [$no, $type, $codeTP, $indicator, $questions, $a, $b, $c, $d, $eOpt, $answerKeyOption, $answerKeyEssay, $cognitiveLevel, $learningOutcome] = [
                        (int)($r[0] ?? 0),
                        (string)($r[1] ?? ''),
                        (string)($r[2] ?? ''),
                        (string)($r[3] ?? ''),
                        (string)($r[4] ?? ''),
                        (string)($r[5] ?? ''),
                        (string)($r[6] ?? ''),
                        (string)($r[7] ?? ''),
                        (string)($r[8] ?? ''),
                        (string)($r[9] ?? ''),
                        (string)($r[10] ?? ''),
                        (string)($r[11] ?? ''),
                        (string)($r[12] ?? ''),
                        (string)($r[13] ?? ''),
                        (string)($r[14] ?? ''),
                    ];

                    $B = $this->col(self::START_COL, 0);
                    $C = $this->col(self::START_COL, 1);
                    $D = $this->col(self::START_COL, 2);
                    $E = $this->col(self::START_COL, 3);
                    $F = $this->col(self::START_COL, 4);
                    $G = $this->col(self::START_COL, 5);
                    $H = $this->col(self::START_COL, 6);

                    // Catatan (B(top+11))
                    $this->setCellMathRich($sheet, $B . ($rowTop + 10), $learningOutcome);

                    // Indikator (B(top+13))
                    $this->setCellMathRich($sheet, $B . ($rowTop + 12), $indicator);

                    $isEssay = strcasecmp($type, 'essay') === 0;

                    // Soal (E(top+13))
                    $this->setCellMathRich($sheet, $E . ($rowTop + 12), $questions);

                    if (!$isEssay) {
                        // Opsi A..E → F(row)
                        $optStart = $rowTop + 15; // baris 17 relatif kartu
                        $opts = [$a, $b, $c, $d, $eOpt];
                        for ($i = 0; $i < 5; $i++) {
                            $this->setCellMathRich($sheet, $F . ($optStart + $i), $opts[$i] ?? '');
                        }
                    }

                    $rowTop += self::CARD_HEIGHT + self::GAP;
                }
            }
        ];
    }

    /* -------------------------- Helpers tampilan -------------------------- */

    private function setColumnWidthsBtoH(Worksheet $s): void
    {
        foreach (
            [
                'B' => 10,
                'C' => 14,
                'D' => 14,
                'E' => 16,
                'F' => 16,
                'G' => 12,
                'H' => 12,
            ] as $col => $w
        ) {
            $s->getColumnDimension($col)->setWidth($w);
        }
    }

    private function col(string $base, int $offset): string
    {
        $baseIndex = Coordinate::columnIndexFromString($base);
        return Coordinate::stringFromColumnIndex($baseIndex + $offset);
    }

    private function mergeAndSet(Worksheet $s, string $range, string $value): void
    {
        $s->mergeCells($range);
        $s->setCellValue(explode(':', $range)[0], $value);
    }

    private function fillBlueHeader(Worksheet $s, string $range): void
    {
        $s->getStyle($range)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB(self::BLUE);
        $s->getStyle($range)->getFont()->setBold(true);
    }

    private function setAllBorders(Worksheet $s, string $range): void
    {
        $s->getStyle($range)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
    }

    private function setOutlineBorder(Worksheet $s, string $range): void
    {
        $s->getStyle($range)->getBorders()->getOutline()->setBorderStyle(Border::BORDER_THIN);
    }

    private function alignCenter(Worksheet $s, string $cell, bool $bold = false, ?int $fontSize = null): void
    {
        $s->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER)->setVertical(Alignment::VERTICAL_CENTER);
        if ($bold) {
            $s->getStyle($cell)->getFont()->setBold(true);
        }
        if ($fontSize) {
            $s->getStyle($cell)->getFont()->setSize($fontSize);
        }
    }

    private function alignLeft(Worksheet $s, string $cell, bool $bold = false, ?int $fontSize = null): void
    {
        $s->getStyle($cell)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT)->setVertical(Alignment::VERTICAL_CENTER);
        if ($bold) {
            $s->getStyle($cell)->getFont()->setBold(true);
        }
        if ($fontSize) {
            $s->getStyle($cell)->getFont()->setSize($fontSize);
        }
    }

    /* --------------------------- Gambar satu kartu --------------------------- */

    private function drawCardBH(
        Worksheet $s,
        string $left, // 'B'
        int $top,     // 2
        int $no,
        string $codeTP,
        string $indicator,
        string $questions,
        string $optA,
        string $optB,
        string $optC,
        string $optD,
        string $optE,
        string $answerKeyOption,
        string $answerKeyEssay,
        string $cognitiveLevel,
        string $learningOutcome,
        bool $isEssay = false
    ): void {
        // Kolom B..H
        $B = $this->col($left, 0);
        $C = $this->col($left, 1);
        $D = $this->col($left, 2);
        $E = $this->col($left, 3);
        $F = $this->col($left, 4);
        $G = $this->col($left, 5);
        $H = $this->col($left, 6);

        $bottom = $top + self::CARD_HEIGHT - 1;

        // Outline kartu
        $this->setOutlineBorder($s, "{$B}{$top}:{$H}{$bottom}");

        /* --------- Header instansi (3 baris), baris ke-4 kosong, judul di baris ke-5 --------- */

        $this->mergeAndSet($s, "{$B}{$top}:{$H}{$top}", 'PEMERINTAH DAERAH PROVINSI BEKASI');
        $this->alignCenter($s, "{$B}{$top}", true);

        $this->mergeAndSet($s, $B . ($top + 1) . ':' . $H . ($top + 1), 'CABANG DINAS PENDIDIKAN');
        $this->alignCenter($s, $B . ($top + 1), true);

        $this->mergeAndSet($s, $B . ($top + 2) . ':' . $H . ($top + 2), $this->academicYear);
        $this->alignCenter($s, $B . ($top + 2), true);

        // Baris 4 kosong (top+3)
        $s->mergeCells($B . ($top + 3) . ':' . $H . ($top + 3));

        // Judul (row 5)
        $this->mergeAndSet($s, $B . ($top + 4) . ':' . $H . ($top + 4), 'KARTU SOAL');
        $this->fillBlueHeader($s, $B . ($top + 4) . ':' . $H . ($top + 4));
        $this->alignCenter($s, $B . ($top + 4), true, 14);

        /* -------------------- Info sekolah / kelas / mapel / keahlian -------------------- */

        $this->mergeAndSet($s, $B . ($top + 5) . ':' . $C . ($top + 5), 'Nama Sekolah');
        $this->mergeAndSet($s, $D . ($top + 5) . ':' . $F . ($top + 5), ': ' . $this->schoolName);

        $this->mergeAndSet($s, $B . ($top + 6) . ':' . $C . ($top + 6), 'Kelas / Semester');
        $this->mergeAndSet($s, $D . ($top + 6) . ':' . $F . ($top + 6), ': ' . $this->classSemester);

        $this->mergeAndSet($s, $B . ($top + 7) . ':' . $C . ($top + 7), 'Mata Pelajaran');
        $this->mergeAndSet($s, $D . ($top + 7) . ':' . $F . ($top + 7), ': ' . $this->subjectName);

        $this->mergeAndSet($s, $B . ($top + 8) . ':' . $C . ($top + 8), 'Kompetensi Keahlian');
        $this->mergeAndSet($s, $D . ($top + 8) . ':' . $F . ($top + 8), ': ' . ($this->kompetensiKeahlian ?? ''));

        // KODE TP
        $this->mergeAndSet($s, $G . ($top + 5) . ':' . $H . ($top + 5), 'Kode TP');
        $this->alignCenter($s, $G . ($top + 5), true);
        $this->mergeAndSet($s, $G . ($top + 6) . ':' . $H . ($top + 8), $codeTP);
        $this->alignCenter($s, $G . ($top + 6), false);

        // Border kotak info
        $this->setAllBorders($s, $B . ($top + 5) . ':' . $H . ($top + 9));

        /* ------------------------- Materi / Tujuan / Aspek ------------------------- */

        // Header
        $this->mergeAndSet($s, $B . ($top + 9) . ':' . $E . ($top + 9), 'Materi Pembelajaran');
        $this->alignCenter($s, $B . ($top + 9) . ':' . $E . ($top + 9), true);

        $this->mergeAndSet($s, $F . ($top + 9) . ':' . $G . ($top + 9), 'Tujuan Pembelajaran');
        $this->alignCenter($s, $F . ($top + 9) . ':' . $G . ($top + 9), true);

        $s->setCellValue($H . ($top + 9), 'Aspek');
        $this->alignCenter($s, $H . ($top + 9), true);

        $this->fillBlueHeader($s, $B . ($top + 9) . ':' . $H . ($top + 9));

        // Nilai
        $this->mergeAndSet($s, $B . ($top + 10) . ':' . $E . ($top + 10), $learningOutcome ?: '');
        $this->alignLeft($s, $B . ($top + 10), false);

        $this->mergeAndSet($s, $F . ($top + 10) . ':' . $G . ($top + 10), '');
        $this->alignLeft($s, $F . ($top + 10), false);

        $s->setCellValue($H . ($top + 10), $cognitiveLevel ?: '');
        $this->alignLeft($s, $H . ($top + 10), false);

        $this->setAllBorders($s, $B . ($top + 10) . ':' . $H . ($top + 10));

        /* ------------------- Indikator / Nomor / Rumusan / Buku / Media ------------------- */

        // Header blok bawah
        $s->setCellValue($B . ($top + 11), 'Indikator Soal');
        $this->alignCenter($s, $B . ($top + 11), true);

        $this->mergeAndSet($s, $C . ($top + 11) . ':' . $D . ($top + 12), 'Nomor');
        $this->alignCenter($s, $C . ($top + 11), true);

        $this->mergeAndSet($s, $E . ($top + 11) . ':' . $G . ($top + 11), 'Rumusan Butir Soal');
        $this->alignCenter($s, $E . ($top + 11) . ':' . $G . ($top + 11), true);

        $s->setCellValue($H . ($top + 11), 'Buku');
        $this->alignCenter($s, $H . ($top + 11), true);

        // Label media (di bawah Buku)
        $this->mergeAndSet($s, $H . ($top + 13) . ':' . $H . ($top + 13), 'Gambar/Tabel/Grafik/Wacana');
        $this->alignCenter($s, $H . ($top + 13), true);

        $this->mergeAndSet($s, $H . ($top + 14) . ':' . $H . ($top + 19), '');

        $this->fillBlueHeader($s, $B . ($top + 11) . ':' . $H . ($top + 11));
        $this->fillBlueHeader($s, $H . ($top + 13) . ':' . $H . ($top + 13));

        // Indikator: B13..B21
        $this->mergeAndSet($s, $B . ($top + 12) . ':' . $B . ($top + 19), $indicator ?: '');
        $this->alignLeft($s, $B . ($top + 12), false);

        // Nomor: C14..D16
        $this->mergeAndSet($s, $C . ($top + 13) . ':' . $D . ($top + 14), (string)$no);
        $this->alignCenter($s, $C . ($top + 13), true, 13);

        if ($isEssay) {
            // Essay → Rumusan melebar E13..G21
            $this->mergeAndSet($s, $E . ($top + 12) . ':' . $G . ($top + 19), $questions ?: '');

            // Essay → Kunci + Opsi
            $this->mergeAndSet($s, $C . ($top + 15) . ':' . $D . ($top + 16), 'Kunci Jawaban');
            $this->alignCenter($s, $C . ($top + 15), true);
            $this->mergeAndSet($s, $C . ($top + 17) . ':' . $D . ($top + 19), mb_strtoupper($answerKeyEssay ?: '-', 'UTF-8'));
            $this->alignLeft($s, $C . ($top + 17), true, 12);
        } else {
            // PG → Kunci + Opsi
            $this->mergeAndSet($s, $C . ($top + 15) . ':' . $D . ($top + 16), 'Kunci Jawaban');
            $this->alignCenter($s, $C . ($top + 15), true);
            $this->mergeAndSet($s, $C . ($top + 17) . ':' . $D . ($top + 19), mb_strtoupper($answerKeyOption ?: '-', 'UTF-8'));
            $this->alignCenter($s, $C . ($top + 17), true, 14);

            // Rumusan pertanyaan E13..G16
            $this->mergeAndSet($s, $E . ($top + 12) . ':' . $G . ($top + 14), $questions ?: '');

            // Opsi A–E E17..G21
            $optStart = $top + 15; // baris 17 relatif kartu
            $r = $optStart;
            foreach ([['A', $optA], ['B', $optB], ['C', $optC], ['D', $optD], ['E', $optE]] as [$lbl, $txt]) {
                $s->setCellValue($E . $r, $lbl);
                $this->mergeAndSet($s, $F . $r . ':' . $G . $r, $txt ?: '');
                $r++;
            }
            $this->setAllBorders($s, $E . $optStart . ':' . $G . ($r - 1));

            $this->fillBlueHeader($s, $C . ($top + 15) . ':' . $D . ($top + 16));
        }

        // Border blok bawah (B13..H21)
        $this->setAllBorders($s, $B . ($top + 11) . ':' . $H . ($top + 19));

        // Wrap text area utama (B5..H21)
        $s->getStyle($B . ($top + 3) . ':' . $H . ($top + 19))
            ->getAlignment()->setWrapText(true);

        /* ------------------------------- Footer TTD ------------------------------- */

        $footTop = $top + 20;

        // Baris judul footer
        $this->mergeAndSet($s, $B . $footTop . ':' . $E . $footTop, 'Mengetahui,');
        $this->mergeAndSet($s, $F . $footTop . ':' . $H . $footTop, 'Guru Mata Pelajaran');

        // Jabatan
        $this->mergeAndSet($s, $B . ($footTop + 1) . ':' . $E . ($footTop + 1), 'Kepala Sekolah');

        // Area kosong tanda tangan
        $s->mergeCells($B . ($footTop + 2) . ':' . $E . ($footTop + 6));
        $s->mergeCells($F . ($footTop + 1) . ':' . $H . ($footTop + 6));

        // Garis nama
        $this->mergeAndSet($s, $B . ($footTop + 8) . ':' . $E . ($footTop + 8), 'Novita Yusnaini, S.S., M.Pd');
        $this->mergeAndSet($s, $F . ($footTop + 8) . ':' . $H . ($footTop + 8), $this->teacherName ?: '________________________');
    }

    /* ===================== Math → RichText Helpers (private) ===================== */

    private function setCellMathRich(Worksheet $sheet, string $cell, string $htmlOrText): void
    {
        $text = (string)$htmlOrText;
        if ($text === '') {
            $sheet->setCellValue($cell, '');
            return;
        }

        $segments = $this->splitTextAndMath($text);
        $hasMath = array_reduce($segments, fn($c, $s) => $c || $s['type'] === 'math', false);

        $rich = new RichText();

        if ($hasMath) {
            foreach ($segments as $seg) {
                if ($seg['type'] === 'text') {
                    $rich->createTextRun($seg['value']);
                } else {
                    $runs = $this->latexToRuns($seg['value']);
                    foreach ($runs as $r) {
                        $run = $rich->createTextRun($r['text']);
                        $font = $run->getFont();
                        if ($r['sup']) $font->setSuperscript(true);
                        if ($r['sub']) $font->setSubscript(true);
                    }
                }
            }
        } else {
            // Tidak ada $...$ → tetap coba parse ^/_/sqrt/frac pada teks penuh
            $runs = $this->latexToRuns($text);
            foreach ($runs as $r) {
                $run = $rich->createTextRun($r['text']);
                $font = $run->getFont();
                if ($r['sup']) $font->setSuperscript(true);
                if ($r['sub']) $font->setSubscript(true);
            }
        }

        $sheet->getCell($cell)->setValueExplicit($rich, DataType::TYPE_INLINE);
    }

    /** Pisah [text] vs [math] berdasar $$...$$ atau $...$ */
    private function splitTextAndMath(string $s): array
    {
        $out = [];
        $pos = 0;
        $len = strlen($s);

        while ($pos < $len) {
            $start = strpos($s, '$$', $pos);
            if ($start !== false) {
                if ($start > $pos) $out[] = ['type' => 'text', 'value' => substr($s, $pos, $start - $pos)];
                $end = strpos($s, '$$', $start + 2);
                if ($end === false) {
                    $out[] = ['type' => 'text', 'value' => substr($s, $start)];
                    break;
                }
                $out[] = ['type' => 'math', 'value' => substr($s, $start + 2, $end - $start - 2)];
                $pos = $end + 2;
                continue;
            }
            $start = strpos($s, '$', $pos);
            if ($start === false) {
                $out[] = ['type' => 'text', 'value' => substr($s, $pos)];
                break;
            }
            if ($start > $pos) $out[] = ['type' => 'text', 'value' => substr($s, $pos, $start - $pos)];
            $end = strpos($s, '$', $start + 1);
            if ($end === false) {
                $out[] = ['type' => 'text', 'value' => substr($s, $start)];
                break;
            }
            $out[] = ['type' => 'math', 'value' => substr($s, $start + 1, $end - $start - 1)];
            $pos = $end + 1;
        }
        return $out;
    }

    /** Map Greek: \alpha → α, dst */
    private function replaceGreek(string $t): string
    {
        static $map = [
            '\\alpha' => 'α',
            '\\beta' => 'β',
            '\\gamma' => 'γ',
            '\\delta' => 'δ',
            '\\epsilon' => 'ε',
            '\\zeta' => 'ζ',
            '\\eta' => 'η',
            '\\theta' => 'θ',
            '\\iota' => 'ι',
            '\\kappa' => 'κ',
            '\\lambda' => 'λ',
            '\\mu' => 'μ',
            '\\nu' => 'ν',
            '\\xi' => 'ξ',
            '\\pi' => 'π',
            '\\rho' => 'ρ',
            '\\sigma' => 'σ',
            '\\tau' => 'τ',
            '\\upsilon' => 'υ',
            '\\phi' => 'φ',
            '\\chi' => 'χ',
            '\\psi' => 'ψ',
            '\\omega' => 'ω',
            '\\Gamma' => 'Γ',
            '\\Delta' => 'Δ',
            '\\Theta' => 'Θ',
            '\\Lambda' => 'Λ',
            '\\Xi' => 'Ξ',
            '\\Pi' => 'Π',
            '\\Sigma' => 'Σ',
            '\\Upsilon' => 'Υ',
            '\\Phi' => 'Φ',
            '\\Psi' => 'Ψ',
            '\\Omega' => 'Ω',
        ];
        return strtr($t, $map);
    }

    /**
     * Parse LaTeX ringan → runs: ['text'=>..., 'sup'=>bool, 'sub'=>bool]
     * Support: \frac{a}{b}, \sqrt{x}, ^, _, Greek, \cdot, \times
     */
    private function latexToRuns(string $tex): array
    {
        $tex = trim($tex);
        $tex = $this->replaceGreek($tex);

        // \frac{num}{den} → (num) ⁄ (den)
        $tex = preg_replace_callback('/\\\\frac\{([^{}]+|(?R))\}\{([^{}]+|(?R))\}/', function ($m) {
            $num = $m[1];
            $den = $m[2];
            if (mb_strlen($num) > 1) $num = '(' . $num . ')';
            if (mb_strlen($den) > 1) $den = '(' . $den . ')';
            return $num . ' ⁄ ' . $den;
        }, $tex);

        // \sqrt{expr} → √(expr)
        $tex = preg_replace('/\\\\sqrt\{([^{}]+)\}/', '√($1)', $tex);

        // simbol
        $tex = str_replace(['\\cdot', '\\times'], ['·', '×'], $tex);

        // Tokenisasi ^/_ untuk sup/sub
        $runs = [];
        $len = mb_strlen($tex);
        for ($i = 0; $i < $len; $i++) {
            $ch = mb_substr($tex, $i, 1);

            if ($ch === '^' || $ch === '_') {
                $isSup = ($ch === '^');
                $next = ($i + 1 < $len) ? mb_substr($tex, $i + 1, 1) : '';
                if ($next === '{') {
                    $j = $i + 2;
                    $brace = 1;
                    $buf = '';
                    while ($j < $len && $brace > 0) {
                        $c = mb_substr($tex, $j, 1);
                        if ($c === '{') $brace++;
                        elseif ($c === '}') {
                            $brace--;
                            if ($brace === 0) break;
                        }
                        $buf .= $c;
                        $j++;
                    }
                    if ($brace === 0) {
                        $runs[] = ['text' => $buf, 'sup' => $isSup, 'sub' => !$isSup];
                        $i = $j;
                        continue;
                    }
                } elseif ($next !== '') {
                    $runs[] = ['text' => $next, 'sup' => $isSup, 'sub' => !$isSup];
                    $i++;
                    continue;
                }
                // fallback
                $runs[] = ['text' => $ch, 'sup' => false, 'sub' => false];
                continue;
            }

            if (!empty($runs) && !$runs[array_key_last($runs)]['sup'] && !$runs[array_key_last($runs)]['sub']) {
                $runs[array_key_last($runs)]['text'] .= $ch;
            } else {
                $runs[] = ['text' => $ch, 'sup' => false, 'sub' => false];
            }
        }

        // gabungkan run normal berurutan
        $merged = [];
        foreach ($runs as $r) {
            if (
                !empty($merged) && !$r['sup'] && !$r['sub'] &&
                !$merged[array_key_last($merged)]['sup'] && !$merged[array_key_last($merged)]['sub']
            ) {
                $merged[array_key_last($merged)]['text'] .= $r['text'];
            } else {
                $merged[] = $r;
            }
        }
        return $merged;
    }
}
