<?php

namespace App\Imports\Student;

use App\Http\Requests\User\StoreUserRequest;
use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SubClassroom;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class StudentsImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    /** @var int|null */
    protected $schoolId;

    /** @var string|null */
    protected $role;

   public function __construct(?int $schoolId = null, ?string $role = 'student')
{
    HeadingRowFormatter::default('none');

    if (!$schoolId) {
        throw new \InvalidArgumentException('schoolId wajib diisi saat import.');
    }

    $this->schoolId = $schoolId;
    $this->role     = $role ?: 'student';
}

    /**
     * Generate a random password for imported users.
     */
    private function generateRandomPassword(): string
    {
        return Str::random(8);
    }

    public function collection(Collection $rows)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                // cari subClassroom terdekat dari teks kelas
                $classroomRow = strtolower(trim($row['Tingkat - Rombel'] ?? $row['Kelas'] ?? ''));
                $subClassroom = SubClassroom::with('classroom')->get()
                    ->sortByDesc(function ($item) use ($classroomRow) {
                        /** @var \App\Models\SubClassroom $item */
                        $name = strtolower($item->fullName ?? ($item->name ?? ''));
                        similar_text($name, $classroomRow, $percent);
                        return $percent ?? 0;
                    })
                    ->first();

                if (!$subClassroom) {
                    $line = $index + 2;
                    throw new Exception("SubClassroom tidak ditemukan untuk baris {$line} dengan kelas: '{$classroomRow}'");
                }

                $academicYear = AcademicYear::where('name', $row['Tahun Akademik'] ?? '')->first();

                $data = [
                    'name'                => $row['Nama'] ?? $row['Nama Lengkap'] ?? null,
                    'email'               => $row['Email'] ?? (($row['NISN'] ?? uniqid('nisn.')) . '@tias.com'),
                    'phone'               => $row['No Telepon'] ?? null,
                    'birth_place'         => $row['Tempat Lahir'] ?? null,
                    'birth_date'          => $row['Tanggal Lahir'] ?? null,
                    'religion'            => $row['Agama'] ?? null,
                    'address'             => $row['Alamat'] ?? null,
                    'registration_number' => (string) ($row['NISN'] ?? ''),
                    'school_origin'       => $row['Asal Sekolah'] ?? null,
                    'academic_year_id'    => optional($academicYear)->id,
                    'sub_classroom_id'    => $subClassroom->id,
                ];

                // minimal: harus ada nama & NISN
                if (empty($data['name']) || empty($data['registration_number'])) {
                    continue;
                }

                // VALIDASI — jika StoreUserRequest mengandalkan context Request/route,
                // lebih aman bikin rules tersendiri di sini.
                $rules = [
                    'name'                => ['required', 'string', 'max:255'],
                    'email'               => ['required', 'email', 'max:255'],
                    'registration_number' => ['required', 'string', 'max:50'],
                    'sub_classroom_id'    => ['required', 'integer'],
                ];

                $validator = Validator::make($data, $rules);
                if ($validator->fails()) {
                    $line = $index + 2;
                    throw new Exception("Gagal impor di baris {$line}. Error: " . implode(' ', $validator->errors()->all()));
                }

                // password random
                $randomPassword = $this->generateRandomPassword();

                // Check if the user was soft-deleted
                $existingDeletedUser = User::withTrashed()
                    ->where('email', $data['email'])
                    ->whereNotNull('deleted_at')
                    ->first();

                if ($existingDeletedUser) {
                    // Restore the deleted user
                    $existingDeletedUser->restore();
                    $user = $existingDeletedUser;
                    
                    // Update the user with new data
                    $user->update([
                        'name'                => $data['name'],
                        'phone'               => $data['phone'],
                        'birth_place'         => $data['birth_place'],
                        'birth_date'          => $data['birth_date'],
                        'religion'            => $data['religion'],
                        'address'             => $data['address'],
                        'password'            => Hash::make($randomPassword),
                        'default_password'    => $randomPassword,
                        'is_active'           => true,
                        'registration_number' => $data['registration_number'],
                        'school_origin'       => $data['school_origin'],
                        'sub_classroom_id'    => $data['sub_classroom_id'],
                        'academic_year_id'    => $data['academic_year_id'],
                    ]);
                } else {
                    // Create a new user
                    $user = User::updateOrCreate(
                        [
                            'email' => $data['email'],
                        ],
                        [
                            'name'                => $data['name'],
                            'phone'               => $data['phone'],
                            'birth_place'         => $data['birth_place'],
                            'birth_date'          => $data['birth_date'],
                            'religion'            => $data['religion'],
                            'address'             => $data['address'],
                            'password'            => Hash::make($randomPassword),
                            'default_password'    => $randomPassword,
                            'is_active'           => true,
                            'registration_number' => $data['registration_number'],
                            'school_origin'       => $data['school_origin'],
                            'sub_classroom_id'    => $data['sub_classroom_id'],
                            'academic_year_id'    => $data['academic_year_id'],
                        ]
                    );
                }

                // penetapan role
                if ($this->schoolId) {
                    $school = School::find($this->schoolId);
                    if ($school && method_exists($user, 'assignSchoolRole')) {
                        $user->assignSchoolRole($school, $this->role);
                    } elseif ($this->role && method_exists($user, 'assignRole')) {
                        $user->assignRole($this->role);
                    }
                } elseif ($this->role && method_exists($user, 'assignRole')) {
                    $user->assignRole($this->role);
                }

                // data orang tua / wali (opsional)
                $motherName   = $row['Nama Ibu Kandung'] ?? null;
                $guardianName = $row['Nama Orang Tua'] ?? $row['Nama Wali'] ?? null;
                $parentName   = $motherName ?: $guardianName;

                // Log import data for debugging
                \Log::info("Student import with parent data: " . json_encode([
                    'student_email' => $data['email'],
                    'school_id' => $this->schoolId,
                    'parent_name' => $parentName,
                    'mother_name' => $motherName,
                    'guardian_name' => $guardianName,
                ]));

                if ($parentName) {
                    $email = str()->slug($parentName, '.') . '@tias.com';
                    $counter = 1;
                    while (User::where('email', $email)->exists()) {
                        $email = str()->slug($parentName, '.') . $counter++ . '@tias.com';
                    }

                    $parentRandomPassword = $this->generateRandomPassword();

                    // Check if the parent was soft-deleted
                    $existingDeletedParent = User::withTrashed()
                        ->where('email', $email)
                        ->whereNotNull('deleted_at')
                        ->first();

                    if ($existingDeletedParent) {
                        // Restore the deleted parent
                        $existingDeletedParent->restore();
                        $parent = $existingDeletedParent;
                        
                        // Update the parent with new data
                        $parent->update([
                            'name'             => $parentName,
                            'password'         => Hash::make($parentRandomPassword),
                            'default_password' => $parentRandomPassword,
                            'is_active'        => true,
                        ]);
                    } else {
                        // Create a new parent
                        $parent = User::updateOrCreate(
                            ['email' => $email],
                            [
                                'name'             => $parentName,
                                'password'         => Hash::make($parentRandomPassword),
                                'default_password' => $parentRandomPassword,
                                'is_active'        => true,
                            ]
                        );
                    }

                    if (method_exists($parent, 'assignRole')) {
                        $parent->assignRole('parent');
                    }
                    
                    // Assign parent to the same school as the student
                    if ($this->schoolId) {
                        $school = School::find($this->schoolId);
                        if ($school && method_exists($parent, 'assignSchoolRole')) {
                            try {
                                // First remove any existing role assignments to prevent duplicates
                                DB::table('user_school_roles')
                                    ->where('user_id', $parent->id)
                                    ->where('school_id', $school->id)
                                    ->delete();
                                    
                                // Then assign the role
                                $parent->assignSchoolRole($school, 'parent');
                                
                                // Log success for debugging
                                \Log::info("Parent {$parent->id} ({$parent->email}) assigned to school {$school->id}");
                            } catch (\Exception $e) {
                                \Log::error("Failed to assign parent {$parent->id} to school: " . $e->getMessage());
                            }
                        }
                    }

                    $user->parent()->syncWithoutDetaching([$parent->id]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw ValidationException::withMessages(['import_error' => $e->getMessage()]);
        }
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
