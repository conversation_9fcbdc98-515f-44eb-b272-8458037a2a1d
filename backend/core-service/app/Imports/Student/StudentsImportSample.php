<?php
declare(strict_types=1);

namespace App\Imports\Student;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentsImportSample implements
    FromCollection,
    WithHeadings,
    WithStyles,
    ShouldAutoSize,
    WithTitle,
    WithColumnFormatting
{
    /**
     * Sample data (make sure number-like values are strings).
     */
    public function collection()
    {
        return new Collection([
            [
                'Budi Seti',        // A - <PERSON>a
                '9876543210',       // B - NISN (already correct; keep as string)
                'X IPA 1',          // C - Kelas
                '2025/2026',        // D - <PERSON><PERSON> Akademik (string)
                '02/02/2005',       // E - Tanggal Lahir (string; adjust as needed)
                'Tangerang',        // F - Tempar Lahir
                'Jl. Pangeran',     // G - Alamat
                '0812345678902',    // H - No Telepon (string to preserve leading zero)
                '<EMAIL>', // I - Email
                'SMK 003',          // J - Asal Sekolah
                'Islam',            // K - Agama
                'Siti Nuraini',     // L - Nama Orang Tua
            ],
        ]);
    }

    /**
     * Header row.
     */
    public function headings(): array
    {
        return [
            'Nama',
            'NISN',
            'Kelas',
            'Tahun Akademik',
            'Tanggal Lahir',
            'Tempat Lahir',
            'Alamat',
            'No Telepon',
            'Email',
            'Asal Sekolah',
            'Agama',
            'Nama Orang Tua',
        ];
    }

    /**
     * Force specific columns to Text in Excel (so numbers stay as strings).
     * H = No Telepon. (Add B if you ever want to enforce NISN as text too.)
     */
    public function columnFormats(): array
    {
        return [
            'H' => NumberFormat::FORMAT_TEXT, // No Telepon as text
        ];
    }

    /**
     * Styles and header comments.
     */
    public function styles(Worksheet $sheet)
    {
        // Header comments for required/important fields
        $required = [
            'A1' => 'Nama Lengkap Wajib diisi',
            'C1' => 'Masukkan Kelas sesuai dengan referensi yang ada',
            'D1' => 'Masukkan tahun akademik sesuai dengan referensi yang ada',
            'E1' => 'Format Tanggal Lahir: MM-DD-YYYY',
            'I1' => 'Email Wajib diisi',
        ];

        foreach ($required as $cell => $commentText) {
            $sheet->getComment($cell)->getText()->createTextRun($commentText);
        }

        // Header styling (A1:L1)
        $sheet->getStyle('A1:L1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007ACC'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => '000000'],
                ],
            ],
        ]);

        return [];
    }

    /**
     * Sheet name.
     */
    public function title(): string
    {
        return 'Data Siswa';
    }
}
