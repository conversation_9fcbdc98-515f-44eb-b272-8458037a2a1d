<?php

namespace App\Imports\Exam;

use Illuminate\Support\Str;
use InvalidArgumentException;

class ExamQuestionRowMapper
{
    /**
     * Headings:
     * A No KD | B Konten KD | C Indikator Pencapaian | D Level Kognitif
     * E Bentuk | F Soal | G-K Opsi A–E | L Jawaban(HURUF) | M Poin
     */
    public function map(array $row, int $rowIndex): array
    {
        $g = fn(string $k) => $this->get($row, $k);

        // required
        $kd     = $this->req($g('No KD'), 'No KD', $rowIndex);
        $konten = $this->req($g('Konten KD'), 'Konten KD', $rowIndex);
        $indik  = $this->req($g('Indikator Pencapaian'), 'Indikator Pencapaian', $rowIndex);
        $level  = $this->req($g('Level Kognitif'), 'Level Kognitif', $rowIndex);
        $soal   = $this->req($g('Soal'), 'Soal', $rowIndex);

        $type   = $this->mapType((string)$g('Bentuk'), $rowIndex);
        $kunci  = strtoupper(trim((string) $g('Jawaban(HURUF)')));
        $answerKeyEssay  = ((string) $g('Jawaban(Esai)'));
        $poin   = $this->normalizePoints($g('Poin'));

        // normalisasi KD ke max 100 char
        $kd = Str::limit(trim((string)$kd), 100, '');

        // 🔧 normalisasi Level Kognitif -> backing value enum
        $levelEnum = $this->normalizeLevelKognitif($level, $rowIndex);

        $data = [
            'question_type'        => $type,
            'content'              => trim((string)$soal),
            'answer_key_essay'     => trim((string)$answerKeyEssay),
            'points'               => $poin,
            'kd_number'            => trim((string)$kd),
            'learning_outcome'     => trim((string)$konten),
            'competency_indicator' => trim((string)$indik),
            'level_kognitif'       => $levelEnum,
            'options'              => [],
        ];

        if ($type === 'multiple_choice') {
            $letters = ['A', 'B', 'C', 'D', 'E'];
            $opts = [];
            foreach ($letters as $i => $L) {
                $content = (string) $g("Opsi {$L}");
                if (trim($content) !== '') {
                    $opts[] = [
                        'order_index' => $i + 1,   // A=1..E=5
                        'content'     => trim($content),
                        'is_correct'  => ($L === $kunci),
                    ];
                }
            }
            if (empty($opts)) {
                throw new InvalidArgumentException("Soal Pilihan Ganda harus memiliki minimal 1 opsi (row {$rowIndex}).");
            }
            $hasCorrect = false;
            foreach ($opts as $o) {
                if ($o['is_correct']) {
                    $hasCorrect = true;
                    break;
                }
            }
            if (!$hasCorrect) {
                throw new InvalidArgumentException("Kunci '{$kunci}' tidak cocok dengan opsi A–E (row {$rowIndex}).");
            }
            $data['options'] = $opts;
        }

        return $data;
    }

    private function mapType(string $t, int $rowIndex): string
    {
        $t = Str::lower(trim($t));
        if ($t === '') {
            throw new InvalidArgumentException("Kolom 'Bentuk' wajib diisi (row {$rowIndex}). Gunakan 'Pilihan Ganda' atau 'Essay'.");
        }
        if ($t === 'pg')   $t = 'pilihan ganda';
        if ($t === 'esai') $t = 'essay';

        return match (true) {
            str_contains($t, 'pilihan') || str_contains($t, 'ganda') => 'multiple_choice',
            $t === 'essay' => 'essay',
            default => throw new InvalidArgumentException("Nilai 'Bentuk' tidak valid (row {$rowIndex}). Gunakan 'Pilihan Ganda' atau 'Essay'."),
        };
    }

    private function normalizeLevelKognitif(string $value, int $rowIndex): string
    {
        $v = Str::lower(trim($value));

        // dukung bentuk singkat C1..C6
        $mapShort = [
            'c1' => 'rememberingC1',
            'c2' => 'understandingC2',
            'c3' => 'applyingC3',
            'c4' => 'analyzingC4',
            'c5' => 'evaluatingC5',
            'c6' => 'creatingC6',
        ];

        // dukung label Indonesia umum
        $mapId = [
            'mengingat'     => 'rememberingC1',
            'memahami'      => 'understandingC2',
            'menerapkan'    => 'applyingC3',
            'menganalisis'  => 'analyzingC4',
            'mengevaluasi'  => 'evaluatingC5',
            'mengkreasi'    => 'creatingC6',     // sesuai enum kamu
            'mencipta'      => 'creatingC6',     // alias umum
            'menciptakan'   => 'creatingC6',
            'mengkreasi - c6' => 'creatingC6',   // kalau ada label lengkap
        ];

        // dukung langsung backing value (kalau user isi apa adanya)
        $mapDirect = [
            'rememberingc1'  => 'rememberingC1',
            'understandingc2' => 'understandingC2',
            'applyingc3'     => 'applyingC3',
            'analyzingc4'    => 'analyzingC4',
            'evaluatingc5'   => 'evaluatingC5',
            'creatingc6'     => 'creatingC6',
        ];

        // normalisasi: hapus spasi
        $vNoSpace = str_replace(' ', '', $v);

        if (isset($mapShort[$vNoSpace]))  return $mapShort[$vNoSpace];
        if (isset($mapId[$v]))            return $mapId[$v];
        if (isset($mapDirect[$vNoSpace])) return $mapDirect[$vNoSpace];

        // pesan error ramah + daftar yang diterima
        $allowed = implode(', ', [
            'C1',
            'C2',
            'C3',
            'C4',
            'C5',
            'C6',
            'Mengingat',
            'Memahami',
            'Menerapkan',
            'Menganalisis',
            'Mengevaluasi',
            'Mengkreasi'
        ]);
        throw new InvalidArgumentException(
            "Nilai 'Level Kognitif' tidak valid (row {$rowIndex}). " .
                "Gunakan salah satu dari: {$allowed}."
        );
    }

    private function req(mixed $val, string $label, int $rowIndex): string
    {
        $v = trim((string)($val ?? ''));
        if ($v === '') throw new InvalidArgumentException("Kolom '{$label}' wajib diisi (row {$rowIndex}).");
        return $v;
    }

    private function normalizePoints(mixed $val): float
    {
        $p = is_numeric($val) ? (float)$val : 1.0;
        return $p < 0 ? 0.0 : $p;
    }

    private function get(array $row, string $key): mixed
    {
        foreach ($row as $k => $v) {
            if (mb_strtolower(trim((string)$k)) === mb_strtolower(trim($key))) {
                return $v;
            }
        }
        return null;
    }
}
