<?php

namespace App\Imports\Teacher;

use App\Http\Requests\User\StoreUserRequest;
use App\Models\AcademicYear;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter as ImportsHeadingRowFormatter;

class TeachersImport implements ToCollection, WithHeadingRow
{
    /** @var int|null */
    protected $schoolId;

    /** @var string|null */
    protected $role;

    public function __construct(?int $schoolId = null, ?string $role = 'teacher')
    {
        ImportsHeadingRowFormatter::default('none');
        
        if (!$schoolId) {
            throw new \InvalidArgumentException('schoolId wajib diisi saat import.');
        }

        $this->schoolId = $schoolId;
        $this->role = $role ?: 'teacher';
    }

    /**
     * Generate a random password for imported users.
     * Uses same method as AuthService for consistency.
     *
     * @return string
     */
    private function generateRandomPassword(): string
    {
        return Str::random(8);
    }

    public function collection(Collection $rows)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                $nameRow = $row['Nama Guru'] ?? $row['Nama Lengkap'] ?? null;
                $phoneRow = $row['No Telepon'] ?? $row['Nomor Handphone'] ?? null;
                $academicYear = AcademicYear::where('name', $row['Tahun Akademik'] ?? '')->first();

                $data = [
                    'name' => $nameRow,
                    'registration_number' => (string) $row['NUPTK'] ?? null,
                    'academic_year_id' => optional($academicYear)->id ?? null,
                    'birth_date' => $row['Tanggal Lahir'] ?? null,
                    'birth_place' => $row['Tempat Lahir'] ?? null,
                    'address' => $row['Alamat'] ?? null,
                    'phone' => (string) $phoneRow,

                    'email' => $row['Email'] ?? null,
                    'last_education' => $row['Pendidikan Terakhir'] ?? null,
                    'religion' => $row['Agama'] ?? null,
                ];

                $requestRules = new StoreUserRequest();
                $validator = Validator::make($data, $requestRules->rules(), $requestRules->messages());

                if ($validator->fails()) {
                    $errors = $validator->errors()->all();
                    $line = $index + 2;
                    throw new \Exception("Gagal impor di baris {$line}. Error: " . implode(" ", $errors));
                }

                // Generate random password for imported user
                $randomPassword = $this->generateRandomPassword();

                // Simpan user
                $user = User::create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'phone' => $data['phone'],
                    'birth_place' => $data['birth_place'],
                    'birth_date' => $data['birth_date'],
                    'religion' => $data['religion'],
                    'address' => $data['address'],
                    'password' => Hash::make($randomPassword),
                    'default_password' => $randomPassword,
                    'is_active' => true,
                    'registration_number' => $data['registration_number'],
                    'last_education' => $data['last_education'],
                    'academic_year_id' => $data['academic_year_id'] ?? null,
                    'school_id' => $this->schoolId,
                ]);
                
                // Assign role dan school
                if ($this->schoolId) {
                    $school = \App\Models\School::find($this->schoolId);
                    if ($school && method_exists($user, 'assignSchoolRole')) {
                        $user->assignSchoolRole($school, $this->role);
                    } elseif ($this->role && method_exists($user, 'assignRole')) {
                        $user->assignRole($this->role);
                    }
                } elseif ($this->role && method_exists($user, 'assignRole')) {
                    $user->assignRole($this->role);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // Menampilkan pesan error yang lebih detail untuk debugging
            throw new \Exception('Import gagal: ' . $e->getMessage());
        }
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
