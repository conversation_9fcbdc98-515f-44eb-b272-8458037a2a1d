<?php

namespace App\Imports\Teacher;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class TeachersImportHandler implements WithMultipleSheets
{
    protected ?int $schoolId;
    protected ?string $role;

    public function __construct(?int $schoolId = null, ?string $role = 'teacher')
    {
        $this->schoolId = $schoolId;
        $this->role = $role;
    }
    
    public function sheets(): array
    {
        return [
            0 => new TeachersImport($this->schoolId, $this->role),
            1 => new IgnoreSheetImport(),
        ];
    }
}
