<?php

namespace App\Imports\Parent;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ParentsImportSample implements
    FromCollection,
    WithHeadings,
    WithStyles,
    ShouldAutoSize,
    WithTitle,
    WithColumnFormatting
{
    /**
     * Sample data (make sure number-like values are strings).
     */
    public function collection()
    {
        return new Collection([
            [
                'Siti Nuraini',       // A - Nama
                '<EMAIL>', // B - Email
                '08123456789',        // C - No Telepon (string to preserve leading zero)
                'Jl. Pangeran No. 123',   // D - Alamat
            ],
        ]);
    }

    /**
     * Header row.
     */
    public function headings(): array
    {
        return [
            'Nama',
            'Email',
            'No Telepon',
            'Alamat',
        ];
    }

    /**
     * Force specific columns to Text in Excel (so numbers stay as strings).
     * C = No Telepon.
     */
    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_TEXT, // No Telepon as text
        ];
    }

    /**
     * Styles and header comments.
     */
    public function styles(Worksheet $sheet)
    {
        // Header comments for required/important fields
        $required = [
            'A1' => 'Nama Lengkap Wajib diisi',
            'B1' => 'Email Wajib diisi',
        ];

        foreach ($required as $cell => $commentText) {
            $sheet->getComment($cell)->getText()->createTextRun($commentText);
        }

        // Header styling (A1:D1)
        $sheet->getStyle('A1:D1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007ACC'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color'       => ['argb' => '000000'],
                ],
            ],
        ]);

        return [];
    }

    /**
     * Sheet name.
     */
    public function title(): string
    {
        return 'Data Orang Tua';
    }
}
