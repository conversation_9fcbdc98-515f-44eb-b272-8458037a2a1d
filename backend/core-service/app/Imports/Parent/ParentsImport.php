<?php

namespace App\Imports\Parent;

use App\Models\School;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class ParentsImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    /** @var int|null */
    protected $schoolId;

    public function __construct(?int $schoolId = null)
    {
        HeadingRowFormatter::default('none');

        if (!$schoolId) {
            throw new \InvalidArgumentException('schoolId wajib diisi saat import.');
        }

        $this->schoolId = $schoolId;
    }

    /**
     * Generate a random password for imported users.
     */
    private function generateRandomPassword(): string
    {
        return Str::random(8);
    }

    public function collection(Collection $rows)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $data = [
                    'name'    => $row['Nama'] ?? null,
                    'email'   => $row['Email'] ?? null,
                    'phone'   => $row['No Telepon'] ?? null,
                    'address' => $row['Alamat'] ?? null,
                ];

                // minimal: harus ada nama & email
                if (empty($data['name']) || empty($data['email'])) {
                    continue;
                }

                // VALIDASI
                $rules = [
                    'name'    => ['required', 'string', 'max:255'],
                    'email'   => ['required', 'email', 'max:255'],
                    'phone'   => ['nullable', 'string', 'max:255'],
                    'address' => ['nullable', 'string'],
                ];

                $validator = Validator::make($data, $rules);
                if ($validator->fails()) {
                    $line = $index + 2;
                    throw new Exception("Gagal impor di baris {$line}. Error: " . implode(' ', $validator->errors()->all()));
                }

                // password random
                $randomPassword = $this->generateRandomPassword();

                // Check if the parent was soft-deleted
                $existingDeletedUser = User::withTrashed()
                    ->where('email', $data['email'])
                    ->whereNotNull('deleted_at')
                    ->first();

                if ($existingDeletedUser) {
                    // Restore the deleted user
                    $existingDeletedUser->restore();
                    $parent = $existingDeletedUser;
                    
                    // Update the user with new data
                    $parent->update([
                        'name'             => $data['name'],
                        'phone'            => $data['phone'],
                        'address'          => $data['address'],
                        'password'         => Hash::make($randomPassword),
                        'default_password' => $randomPassword,
                        'is_active'        => true,
                    ]);
                } else {
                    // Create a new parent
                    $parent = User::updateOrCreate(
                        ['email' => $data['email']],
                        [
                            'name'             => $data['name'],
                            'phone'            => $data['phone'],
                            'address'          => $data['address'],
                            'password'         => Hash::make($randomPassword),
                            'default_password' => $randomPassword,
                            'is_active'        => true,
                        ]
                    );
                }

                // Assign parent role
                if (method_exists($parent, 'assignRole')) {
                    $parent->assignRole('parent');
                }
                
                // Assign parent to school
                if ($this->schoolId) {
                    $school = School::find($this->schoolId);
                    if ($school && method_exists($parent, 'assignSchoolRole')) {
                        try {
                            // First remove any existing role assignments to prevent duplicates
                            DB::table('user_school_roles')
                                ->where('user_id', $parent->id)
                                ->where('school_id', $school->id)
                                ->delete();
                                
                            // Then assign the role
                            $parent->assignSchoolRole($school, 'parent');
                            
                            // Log success for debugging
                            \Log::info("Parent {$parent->id} ({$parent->email}) assigned to school {$school->id}");
                        } catch (\Exception $e) {
                            \Log::error("Failed to assign parent {$parent->id} to school: " . $e->getMessage());
                        }
                    }
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw ValidationException::withMessages(['import_error' => $e->getMessage()]);
        }
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
