<?php

namespace App\Imports\Subject;

use App\Http\Requests\Subject\StoreSubjectRequest;
use App\Models\Subject;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter as ImportsHeadingRowFormatter;

class SubjectsImport implements ToCollection, WithHeadingRow
{
    /** @var int */
    protected $schoolId;

    /**
     * @param int|null $schoolId The school ID for which to import subjects
     * @throws \InvalidArgumentException if school ID is not provided
     */
    public function __construct(?int $schoolId = null)
    {
        ImportsHeadingRowFormatter::default('none');
        
        if (!$schoolId) {
            throw new \InvalidArgumentException('School ID is required for import');
        }
        
        $this->schoolId = $schoolId;
    }

    public function collection(Collection $rows)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        DB::beginTransaction();
        try {
            $processedNames = []; // Track names to prevent duplicates within the same import
            
            foreach ($rows as $index => $row) {
                $name = $row['Nama Mata Pelajaran'] ?? null;
                $line = $index + 2; // Add 2 for header row and 1-based indexing
                
                // Skip empty rows
                if (empty($name)) {
                    continue;
                }
                
                // Check for duplicate within the same import file
                if (in_array(strtolower($name), $processedNames)) {
                    throw new Exception("Baris {$line}: Mata pelajaran '{$name}' muncul lebih dari sekali dalam file import.");
                }
                
                // Check for existing subject with same name in this school
                if (Subject::where('name', $name)->where('school_id', $this->schoolId)->exists()) {
                    throw new Exception("Baris {$line}: Mata pelajaran '{$name}' sudah terdaftar di sekolah ini.");
                }
                
                // Track the processed name (case insensitive)
                $processedNames[] = strtolower($name);
                
                // School ID was validated in the constructor
                $data = [
                    'name' => $name,
                    'description' => $row['Deskripsi'] ?? null,
                    'school_id' => $this->schoolId,
                    'priority' => 999, // Default low priority for imported subjects
                ];

                $requestRules = new StoreSubjectRequest();
                $validator = Validator::make($data, $requestRules->rules(), $requestRules->messages());

                if ($validator->fails()) {
                    $errors = $validator->errors()->all();
                    $line = $index + 2;
                    throw new Exception("Gagal impor di baris {$line}. Error: " . implode(" ", $errors));
                }

                Subject::create($data);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            // Menampilkan pesan error yang lebih detail untuk debugging
            throw new Exception($e->getMessage());
        }
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
