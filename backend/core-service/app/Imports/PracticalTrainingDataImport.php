<?php

namespace App\Imports;

use App\Models\PracticalTrainingData;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithValidation;

class PracticalTrainingDataImport implements ToModel, WithHeadingRow, SkipsEmptyRows, WithValidation
{
    protected ?int $trainingId;

    public function __construct(?int $trainingId = null)
    {
        $this->trainingId = $trainingId;
    }


    public function model(array $row)
    {
        $val = fn(array $candidates) => $this->firstNonNull($row, $candidates);

        $learningObjective = $val(['learning_objective', 'tujuan_pembelajaran', 'tujuan pembelajaran']);
        $finalScore        = $val(['final_score', 'nilai_akhir', 'nilai akhir', 'nilai']);
        $description       = $val(['description', 'deskripsi']);

        $finalScore = is_numeric($finalScore) ? (float) $finalScore : 0.0;

        return new PracticalTrainingData([
            'practical_training_id' => $this->trainingId,
            'learning_objective'    => $this->strOrNull($learningObjective),
            'final_score'           => $finalScore,
            'description'           => $this->strOrNull($description),
            'created_by'            => Auth::id(),
        ]);
    }


    public function rules(): array
    {
        return [
            '*.learning_objective'   => 'required_without_all:*.tujuan_pembelajaran|nullable|string|max:255',
            '*.tujuan_pembelajaran'  => 'required_without_all:*.learning_objective|nullable|string|max:255',

            '*.final_score'          => 'nullable|numeric|min:0|max:100',
            '*.nilai_akhir'          => 'nullable|numeric|min:0|max:100',

            '*.description'          => 'nullable|string|max:500',
            '*.deskripsi'            => 'nullable|string|max:500',
        ];
    }


    private function firstNonNull(array $row, array $candidates)
    {
        foreach ($candidates as $key) {
            $normalized = str_replace(' ', '_', strtolower($key));
            if (array_key_exists($normalized, $row) && $row[$normalized] !== null && $row[$normalized] !== '') {
                return $row[$normalized];
            }
            if (array_key_exists($key, $row) && $row[$key] !== null && $row[$key] !== '') {
                return $row[$key];
            }
        }
        return null;
    }

    private function strOrNull($v): ?string
    {
        $s = is_string($v) ? trim($v) : (is_null($v) ? null : (string)$v);
        return $s === '' ? null : $s;
    }
}
