<?php

namespace App\Imports\Facility;

use App\Http\Requests\Facility\StoreFacilityRequest;
use App\Models\Facility;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter as ImportsHeadingRowFormatter;

class FacilitiesImport implements ToCollection, WithHeadingRow
{
    /** @var int */
    protected $schoolId;

    /**
     * @param int|null $schoolId The school ID for which to import facilities
     * @throws \InvalidArgumentException if school ID is not provided
     */
    public function __construct(?int $schoolId = null)
    {
        ImportsHeadingRowFormatter::default('none');
        
        if (!$schoolId) {
            throw new \InvalidArgumentException('School ID is required for import');
        }
        
        $this->schoolId = $schoolId;
    }

    public function collection(Collection $rows)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        DB::beginTransaction();
        try {
            $processedNames = []; // Track names to prevent duplicates within the same import
            
            foreach ($rows as $index => $row) {
                $name = $row['Nama Fasilitas'] ?? null;
                $line = $index + 2; // Add 2 for header row and 1-based indexing
                
                // Skip empty rows
                if (empty($name)) {
                    continue;
                }
                
                // Check for duplicate within the same import file
                if (in_array(strtolower($name), $processedNames)) {
                    throw new Exception("Baris {$line}: Fasilitas '{$name}' muncul lebih dari sekali dalam file import.");
                }
                
                // Check for existing facility with same name in this school
                if (Facility::where('name', $name)->where('school_id', $this->schoolId)->exists()) {
                    throw new Exception("Baris {$line}: Fasilitas '{$name}' sudah terdaftar di sekolah ini.");
                }
                
                // Track the processed name (case insensitive)
                $processedNames[] = strtolower($name);

                // Validate numeric fields
                $goodCondition = $this->parseNumericField($row['Kondisi Baik'] ?? 0, $line, 'Kondisi Baik');
                $minorDamage = $this->parseNumericField($row['Rusak Ringan'] ?? 0, $line, 'Rusak Ringan');
                $majorDamage = $this->parseNumericField($row['Rusak Berat'] ?? 0, $line, 'Rusak Berat');
                
                // Calculate total stock
                $stock = $goodCondition + $minorDamage + $majorDamage;
                
                // Prepare data for validation
                $data = [
                    'name' => $name,
                    'unit' => $row['Satuan'] ?? null,
                    'good_condition' => $goodCondition,
                    'minor_damage' => $minorDamage,
                    'major_damage' => $majorDamage,
                    'stock' => $stock,
                    'school_id' => $this->schoolId,
                ];

                // Validate data
                $validator = Validator::make($data, [
                    'name' => 'required|string|max:255',
                    'unit' => 'required|string|max:50',
                    'good_condition' => 'required|integer|min:0',
                    'minor_damage' => 'required|integer|min:0',
                    'major_damage' => 'required|integer|min:0',
                    'stock' => 'required|integer|min:1',
                    'school_id' => 'required|integer|exists:schools,id',
                ], [
                    'name.required' => "Baris {$line}: Nama fasilitas wajib diisi.",
                    'unit.required' => "Baris {$line}: Satuan wajib diisi.",
                    'good_condition.required' => "Baris {$line}: Kondisi baik wajib diisi.",
                    'good_condition.integer' => "Baris {$line}: Kondisi baik harus berupa angka.",
                    'good_condition.min' => "Baris {$line}: Kondisi baik minimal 0.",
                    'minor_damage.required' => "Baris {$line}: Rusak ringan wajib diisi.",
                    'minor_damage.integer' => "Baris {$line}: Rusak ringan harus berupa angka.",
                    'minor_damage.min' => "Baris {$line}: Rusak ringan minimal 0.",
                    'major_damage.required' => "Baris {$line}: Rusak berat wajib diisi.",
                    'major_damage.integer' => "Baris {$line}: Rusak berat harus berupa angka.",
                    'major_damage.min' => "Baris {$line}: Rusak berat minimal 0.",
                    'stock.min' => "Baris {$line}: Total stok minimal 1.",
                    'school_id.exists' => "Baris {$line}: Sekolah tidak ditemukan.",
                ]);

                if ($validator->fails()) {
                    $errors = $validator->errors()->all();
                    throw new Exception("Baris {$line}: " . implode(" ", $errors));
                }

                // Create facility
                Facility::create($data);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }

    /**
     * Parse and validate a numeric field
     *
     * @param mixed $value The value to parse
     * @param int $line The line number for error reporting
     * @param string $fieldName The field name for error reporting
     * @return int The parsed value
     */
    private function parseNumericField($value, int $line, string $fieldName): int 
    {
        $value = trim($value);
        
        if ($value === '') {
            return 0;
        }
        
        if (!is_numeric($value)) {
            throw new Exception("Baris {$line}: Nilai {$fieldName} '{$value}' bukan angka yang valid.");
        }
        
        $numericValue = (int)$value;
        
        if ($numericValue < 0) {
            throw new Exception("Baris {$line}: Nilai {$fieldName} tidak boleh kurang dari 0.");
        }
        
        return $numericValue;
    }
}
