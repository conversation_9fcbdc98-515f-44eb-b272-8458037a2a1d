<?php

namespace App\Imports\Facility;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class FacilitiesImportTemplate implements FromCollection, WithHeadings, WithStyles, ShouldAutoSize
{
    public function headings(): array
    {
        return [
            'Nama Fasilitas',
            'Satuan',
            'Kondi<PERSON> Baik',
            'Rusak <PERSON>an',
            'Rusak Berat',
        ];
    }

    public function collection()
    {
        return new Collection([
            [
                'Meja',
                'Unit',
                '10',
                '2',
                '1',
            ],
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        // Add comments to columns
        $sheet->getComment('A1')->getText()->createTextRun('Kolom ini wajib diisi.');
        $sheet->getComment('B1')->getText()->createTextRun('Kolom ini wajib diisi. Contoh: Unit, Buah, Meter, dll.');
        $sheet->getComment('C1')->getText()->createTextRun('Jumlah barang dalam kondisi baik. Minimal 0.');
        $sheet->getComment('D1')->getText()->createTextRun('Jumlah barang dalam kondisi rusak ringan. Minimal 0.');
        $sheet->getComment('E1')->getText()->createTextRun('Jumlah barang dalam kondisi rusak berat. Minimal 0.');

        // Styling header
        $sheet->getStyle('A1:E1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007ACC'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);

        return [];
    }
}
