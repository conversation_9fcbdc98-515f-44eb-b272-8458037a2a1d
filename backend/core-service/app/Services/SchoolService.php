<?php

namespace App\Services;

use App\Http\Requests\School\AssignSchoolAdminRequest;
use App\Models\School;
use Illuminate\Http\Request;
use App\Http\Requests\School\StoreSchoolRequest;
use App\Http\Requests\School\UpdateSchoolRequest;
use App\Models\Role;
use App\Models\User;
use App\Models\UserSchoolRole;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class SchoolService
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return School::with('schoolLevel')
            ->when(isset($request->search), function ($query) use ($request) {
                $query->whereRaw('LOWER(name) LIKE ?', ["%{$request->search}%"]);
            })
            ->when($request->has('foundation_id'), function ($query) use ($request) {
                $query->where('foundation_id', $request->foundation_id);
            })
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreSchoolRequest $request): School
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            $school = School::create($validated);
            if ($validated['headmaster_user_id']) {
                $headmaster = User::find($validated['headmaster_user_id']);
                $school->assignHeadmaster($headmaster);
            }
            return $school;
        });
    }

    public function show(School $model): School
    {
        return $model;
    }

    public function update(School $model, UpdateSchoolRequest $request): School
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);

            // Handle headmaster change
            if ($validated['headmaster_user_id']) {
                // Remove old headmaster role
                if ($model->headmaster()) {
                    $model->headmaster()->removeRole(Role::HEADMASTER);
                }

                $headmaster = User::find($validated['headmaster_user_id']);
                $model->assignHeadmaster($headmaster);
            }
            return $model;
        });
    }

    public function delete(School $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    public function getAdmins(School $model)
    {
        return $model->admins;
    }

    public function assignAdmin(AssignSchoolAdminRequest $request, School $model)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $user = User::find($validated['user_id']);

            // Check if user already has the role
            $exists = UserSchoolRole::where('user_id', $user->id)
                ->where('school_id', $model->id)
                ->where('role_id', Role::firstWhere('name', Role::SCHOOL_ADMIN)->id)
                ->exists();
            if ($user->hasRole(Role::SCHOOL_ADMIN) && $exists) {
                throw new \Exception('User already has the role of school admin.');
            }

            $model->assignAdmin($user);
            return $user;
        });
    }

    public function updateByAdmin(School $model, UpdateSchoolRequest $request): School
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();

            if ($request->hasFile('logo')) {
                $path = $request->file('logo')->store('logos');
                $validated['logo'] = $path;
            }

            $model->update($validated);
            return $model;
        });
    }
}
