<?php

namespace App\Services;

use App\Models\SubClassroom;
use App\Repositories\ClassEnrollment\ClassEnrollmentRepository;

class ClassEnrollmentService
{
    protected ClassEnrollmentRepository $repo;

    public function __construct(ClassEnrollmentRepository $repo)
    {
        $this->repo = $repo;
    }

    public function summary(int $termId, SubClassroom $subClassroom): array
    {
        return $this->repo->summary($termId, $subClassroom);
    }

    public function syncMatrix(int $termId, SubClassroom $subClassroom): int
    {
        return $this->repo->syncMatrix($termId, $subClassroom);
    }

    public function setDefault(int $termId, SubClassroom $subClassroom, bool $isEnrolled, ?string $note = null): int
    {
        return $this->repo->setDefault($termId, $subClassroom, $isEnrolled, $note);
    }

    public function enrollAllForSubject(int $termId, SubClassroom $subClassroom, int $subClassroomSubjectId, bool $isEnrolled, ?string $note = null): int
    {
        return $this->repo->enrollAllForSubject($termId, $subClassroom, $subClassroomSubjectId, $isEnrolled, $note);
    }

    public function enrollAllForStudent(int $termId, SubClassroom $subClassroom, int $studentUserId, bool $isEnrolled, ?string $note = null): int
    {
        return $this->repo->enrollAllForStudent($termId, $subClassroom, $studentUserId, $isEnrolled, $note);
    }
}
