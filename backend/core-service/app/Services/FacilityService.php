<?php

namespace App\Services;

use App\Models\Facility;
use App\Repositories\Facility\FacilityRepository;
use Illuminate\Http\Request;
use App\Models\School;
use Illuminate\Support\Facades\DB;


class FacilityService
{
    protected FacilityRepository $repo;

    public function __construct(FacilityRepository $repo)
    {
        $this->repo = $repo;
    }

  public function paginate(Request $request)
{
    $activeSchoolId = session('active_school_id');

    $q = School::findOrFail($activeSchoolId)->facilities();

    // SEARCH: name ATAU unit (case-insensitive, lintas-DB)
    if ($request->filled('search')) {
        $term   = trim((string) $request->input('search'));
        $driver = DB::connection()->getDriverName();

        if ($driver === 'pgsql') {
            $q->where(function ($qq) use ($term) {
                $qq->where('name', 'ILIKE', "%{$term}%")
                   ->orWhere('unit', 'ILIKE', "%{$term}%");
            });
        } else {
            $l = mb_strtolower($term);
            $q->where(function ($qq) use ($l) {
                $qq->whereRaw('LOWER(name) LIKE ?', ["%{$l}%"])
                   ->orWhereRaw('LOWER(unit) LIKE ?', ["%{$l}%"]);
            });
        }
    }

    // (opsional) sorting
    $q->orderBy('name');

    return $q->paginate((int) ($request->limit ?? config('app.pagination.max')));
}


    public function store(array $data)
    {
        return $this->repo->store($data);
    }

    public function show(Facility $model)
    {
        return $this->repo->show($model);
    }

    public function update(Facility $model, array $data)
    {
        return $this->repo->update($model, $data);
    }

    public function delete(Facility $model)
    {
        return $this->repo->delete($model);
    }
    
    public function import(Request $request, ?int $schoolId = null)
    {
        // Use the provided schoolId parameter, or try to get it from the session/header
        if (!$schoolId) {
            $schoolId = session('active_school_id') ?: $request->header('X-School-ID');
        }
        
        // Store it in the session for any code that might need it
        if ($schoolId) {
            session(['active_school_id' => $schoolId]);
        }
        
        return $this->repo->import($request, $schoolId);
    }

    public function export()
    {
        return $this->repo->export();
    }
    
    public function downloadImportTemplate()
    {
        return $this->repo->downloadImportTemplate();
    }
}
