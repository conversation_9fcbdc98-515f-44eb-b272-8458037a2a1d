<?php

namespace App\Services;

use App\Models\Extracurricular;
use App\Models\ExtracurricularStudent;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\ValidationException;

class ExtracurricularStudentService
{
    public function resolveExtracurricularIdForUser(?int $explicitId = null): ?int
    {
        $userId = Auth::id();

        if ($explicitId) {
            $own = Extracurricular::query()
                ->where('id', $explicitId)
                ->where('teacher_user_id', $userId)
                ->value('id');

            return $own ? (int) $own : null;
        }

        $first = Extracurricular::query()
            ->where('teacher_user_id', $userId)
            ->orderBy('id')
            ->value('id');

        return $first ? (int) $first : null;
    }

    /**
     * Return paginator (LengthAwarePaginator) untuk peserta ekskul milik guru login.
     */
    public function paginate(?int $termId = null, ?int $classId = null, int $perPage = 10): LengthAwarePaginator
    {
        $extracurricularId = $this->resolveExtracurricularIdForUser();
        if (!$extracurricularId) {
            // biar errornya jelas
            throw ValidationException::withMessages([
                'extracurricular_id' => ['Ekskul tidak ditemukan untuk guru yang login.'],
            ]);
        }

        $query = ExtracurricularStudent::query()
            ->where('extracurricular_id', $extracurricularId)
            ->with([
                'student:id,name,registration_number,sub_classroom_id',
                'student.subClassroom:id,classroom_id,sequence',
                'student.subClassroom.classroom:id,name',
            ]);

        if ($termId && Schema::hasColumn('extracurricular_students', 'term_id')) {
            $query->where('term_id', $termId);
        }

        if ($classId) {
            $query->whereHas('student', fn($q) => $q->where('sub_classroom_id', $classId));
        }

        return $query
            ->orderByDesc('id')
            ->paginate($perPage)
            ->appends([
                'term_id'  => $termId,
                'class_id' => $classId,
                'per_page' => $perPage,
            ]);
    }

    public function enroll(int $studentUserId, int $termId): ExtracurricularStudent
    {
        $extracurricularId = $this->resolveExtracurricularIdForUser();
        if (!$extracurricularId) {
            throw ValidationException::withMessages([
                'extracurricular_id' => ['Ekskul tidak ditemukan untuk guru yang login.'],
            ]);
        }

        $key = [
            'extracurricular_id' => $extracurricularId,
            'student_user_id'    => $studentUserId,
            'term_id'            => $termId,
        ];

        $already = ExtracurricularStudent::query()->where($key)->exists();
        if ($already) {
            throw ValidationException::withMessages([
                'student_user_id' => ['Siswa sudah terdaftar pada ekskul ini untuk term tersebut.'],
            ]);
        }

        $trashed = ExtracurricularStudent::withTrashed()->where($key)->first();
        if ($trashed && $trashed->trashed()) {
            $trashed->restore();
            $trashed->update([
                'predicate'  => null,
                'updated_by' => Auth::id(),
            ]);
            return $trashed->refresh();
        }

        try {
            return ExtracurricularStudent::create([
                ...$key,
                'predicate'  => null,
                'created_by' => Auth::id(),
            ]);
        } catch (QueryException $e) {
            $isPgUnique = $e->getCode() === '23505'
                || str_contains($e->getMessage(), 'uniq_exkul_student_term');

            if ($isPgUnique) {
                throw ValidationException::withMessages([
                    'student_user_id' => ['Siswa sudah terdaftar pada ekskul ini untuk term tersebut.'],
                ]);
            }

            throw $e;
        }
    }

    public function updatePredicate(int $rowId, string $predicate): ExtracurricularStudent
    {
        $extracurricularId = $this->resolveExtracurricularIdForUser();
        if (!$extracurricularId) {
            throw ValidationException::withMessages([
                'extracurricular_id' => ['Ekskul tidak ditemukan untuk guru yang login.'],
            ]);
        }

        /** @var ExtracurricularStudent $row */
        $row = ExtracurricularStudent::where('extracurricular_id', $extracurricularId)->findOrFail($rowId);

        $row->update([
            'predicate'  => (string) $predicate,
            'updated_by' => Auth::id(),
        ]);

        return $row->refresh();
    }

    public function remove(int $rowId): void
    {
        $extracurricularId = $this->resolveExtracurricularIdForUser();
        if (!$extracurricularId) {
            throw ValidationException::withMessages([
                'extracurricular_id' => ['Ekskul tidak ditemukan untuk guru yang login.'],
            ]);
        }

        $row = ExtracurricularStudent::where('extracurricular_id', $extracurricularId)->findOrFail($rowId);
        $row->delete();
    }

    /**
     * Dropdown dependent siswa per kelas (Nama - NISN).
     */
    public function listStudentsByClass(int $subClassroomId, ?int $schoolId = null): array
    {
        $q = User::query()
            ->select('users.id', 'users.name', 'users.registration_number', 'users.sub_classroom_id')
            ->whereNull('users.deleted_at')
            ->where('users.sub_classroom_id', $subClassroomId)
            ->whereExists(function ($x) use ($schoolId) {
                $x->from('user_school_roles as usr')
                    ->join('roles', 'roles.id', '=', 'usr.role_id')
                    ->whereColumn('usr.user_id', 'users.id')
                    ->where('roles.name', '=', 'student');

                if (!empty($schoolId)) {
                    $x->where('usr.school_id', '=', $schoolId);
                }

                if (Schema::hasColumn('user_school_roles', 'deleted_at')) {
                    $x->whereNull('usr.deleted_at');
                } elseif (Schema::hasColumn('user_school_roles', 'deleted_by')) {
                    $x->whereNull('usr.deleted_by');
                }
            })
            ->orderBy('users.name');

        return $q->get()->map(fn($u) => [
            'id'   => $u->id,
            'text' => trim($u->name . ($u->registration_number ? ' - ' . $u->registration_number : '')),
        ])->all();
    }
}
