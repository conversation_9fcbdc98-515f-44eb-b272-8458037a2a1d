<?php

namespace App\Services;

use App\Models\Term;
use App\Repositories\Term\TermRepository;
use Illuminate\Http\Request;
use App\Http\Requests\Term\StoreTermRequest;
use App\Http\Requests\Term\UpdateTermRequest;

class TermService
{
    protected TermRepository $repo;

    public function __construct(TermRepository $repo)
    {
        $this->repo = $repo;
    }

    public function paginate(Request $request)
    {
        return $this->repo->paginate($request->all());
    }

    public function store(StoreTermRequest $request)
    {
        $validated = $request->validated();
        return $this->repo->store($validated);
    }

    public function show(Term $model)
    {
        return $this->repo->show($model);
    }

    public function update(Term $model, UpdateTermRequest $request)
    {
        $validated = $request->validated();
        return $this->repo->update($model, $validated);
    }

    public function delete(Term $model)
    {
        return $this->repo->delete($model);
    }
}
