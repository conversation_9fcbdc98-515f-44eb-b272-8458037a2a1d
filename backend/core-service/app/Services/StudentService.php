<?php

namespace App\Services;

use App\Exports\StudentsExport;
use App\Imports\Student\StudentsImport;
use App\Models\School;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\UploadedFile;

class StudentService
{
    public function paginate(Request $request)
    {
        $activeSchoolId = session('active_school_id');
        return School::find($activeSchoolId)->students()
            ->with(['subClassroom', 'academicYear', 'parent'])
            ->when($request->search, function ($query) use ($request) {
                $search = mb_strtolower($request->search);

                // Search by name
                $query->where(function ($q) use ($search) {
                    $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"]);
                });

                // Search by registration number
                $query->orWhereRaw('LOWER(registration_number) LIKE ?', ["%{$search}%"]);

                // Search by parent name
                $query->orWhereHas('parent', function ($q) use ($search) {
                    $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"]);
                });
            })
            ->when($request->sub_classroom_id, function ($query) use ($request) {
                $query->where('sub_classroom_id', $request->sub_classroom_id);
            })
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function import(UploadedFile $file, int $schoolId): array
    {
        if (!$schoolId) {
            throw new \InvalidArgumentException('school_id wajib dikirim saat import.');
        }

        Excel::import(new StudentsImport($schoolId, 'student'), $file);
        return ['status' => true];
    }


    public function export(string $preset = 'students_all')
    {
        return Excel::download(new StudentsExport($preset), 'students_export_' . now()->format('Ymd_His') . '.xlsx');
    }
}
