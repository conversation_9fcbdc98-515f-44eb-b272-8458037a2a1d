<?php

namespace App\Services;

use App\Http\Requests\Parent\LinkStudentRequest;
use App\Imports\Parent\ParentsImport;
use App\Models\StudentParent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Maatwebsite\Excel\Facades\Excel;

class ParentService
{
    public function paginate(Request $request)
    {
        $activeSchoolId = session('active_school_id');
        return User::with(['children'])
            ->whereHas('roles', function ($query) {
                $query->where('name', 'parent');
            })
            ->whereHas('schools', function ($query) use ($activeSchoolId) {
                $query->where('school_id', $activeSchoolId);
            })
            ->when($request->search, function ($query) use ($request) {
                $search = mb_strtolower($request->search);

                // Search by name
                $query->where(function ($q) use ($search) {
                    $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"]);
                });

                // Search by children's name
                $query->orWhereHas('children', function ($q) use ($search) {
                    $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"]);
                });
            })
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function getStudents(int $parentUserId, int $limit)
    {
        $parent = User::find($parentUserId);
        if (!$parent) {
            throw new \Exception('Parent not found');
        }
        return $parent->children()->with('subClassroom')->paginate($limit);
    }

    public function addStudent(array $validated, int $parentUserId)
    {
        $user = User::create([
            'name' => $validated['name'],
            'password' => bcrypt($validated['password']),
            'phone' => $validated['phone'] ?? null,
            'birth_date' => $validated['birth_date'] ?? null,
            'birth_place' => $validated['birth_place'] ?? null,
            'religion' => $validated['religion'] ?? null,
            'address' => $validated['address'] ?? null,
            'email' => $validated['email'],
            'sub_classroom_id' => $validated['sub_classroom_id'] ?? null,
            'registration_number' => $validated['registration_number'],
            'school_origin' => $validated['school_origin'] ?? null,
            'academic_year_id' => $validated['academic_year_id'] ?? null,
        ]);

        $user->assignRole('student');

        StudentParent::create([
            'student_user_id' => $user->id,
            'parent_user_id' => $parentUserId,
        ]);

        return $user;
    }

    public function linkStudent(LinkStudentRequest $request)
    {
        $validated = $request->validated();
        $student = User::find($validated['student_user_id']);
        $parent = User::find($validated['parent_user_id'] ?? $request->user()->id);

        // Validate student exists and has student role
        if (!$student) {
            throw new \Exception('Student not found');
        }
        if (!$student->hasRole('student')) {
            throw new \Exception('Selected user is not a student');
        }

        // Validate parent exists and has parent role
        if (!$parent) {
            throw new \Exception('Parent not found');
        }
        if (!$parent->hasRole('parent')) {
            throw new \Exception('Selected user is not a parent');
        }

        StudentParent::updateOrCreate([
            'parent_user_id' => $parent->id,
            'student_user_id' => $student->id,
        ]);

        return $student;
    }

    public function import(UploadedFile $file, int $schoolId): array
    {
        if (!$schoolId) {
            throw new \InvalidArgumentException('school_id wajib dikirim saat import.');
        }

        Excel::import(new \App\Imports\Parent\ParentsImport($schoolId), $file);
        return ['status' => true];
    }
}
