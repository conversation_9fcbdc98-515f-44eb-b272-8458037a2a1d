<?php

namespace App\Services;

use App\Repositories\SubjectEnrollment\SubjectEnrollmentRepository;

class SubjectEnrollmentService
{
    protected SubjectEnrollmentRepository $repo;

    public function __construct(SubjectEnrollmentRepository $repo)
    {
        $this->repo = $repo;
    }

    /** @return array{students:mixed,subjects:mixed,matrix:array}|array */
    public function getMatrix(int $termId, int $subClassroomId): array
    {
        return $this->repo->getMatrix($termId, $subClassroomId);
    }

    public function toggleSingle(array $payload): void
    {
        $this->repo->upsertSingle($payload);
    }

    public function bulkUpsert(int $termId, array $rows): int
    {
        $payloads = array_map(function ($r) use ($termId) {
            $r['term_id'] = $termId;
            if (isset($r['subClassroomSubjectId'])) {
                $r['sub_classroom_subject_id'] = (int) $r['subClassroomSubjectId'];
                unset($r['subClassroomSubjectId']);
            }
            if (isset($r['userId'])) {
                $r['user_id'] = (int) $r['userId'];
                unset($r['userId']);
            }
            if (isset($r['isEnrolled'])) {
                $r['is_enrolled'] = (bool) $r['isEnrolled'];
                unset($r['isEnrolled']);
            }
            return $r;
        }, $rows);

        $this->repo->upsertBulk($payloads);
        return count($payloads);
    }
}
