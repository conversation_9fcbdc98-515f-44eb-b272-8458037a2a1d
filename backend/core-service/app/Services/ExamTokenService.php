<?php

namespace App\Services;

use App\Models\ExamToken;
use App\Repositories\ExamToken\ExamTokenRepository;
use Illuminate\Http\Request;
use App\Http\Requests\ExamToken\StoreExamTokenRequest;
use App\Http\Requests\ExamToken\UpdateExamTokenRequest;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ExamTokenService
{
    protected ExamTokenRepository $repo;

    public function __construct(ExamTokenRepository $repo)
    {
        $this->repo = $repo;
    }

    public function paginate(Request $request)
    {
        return $this->repo->paginate($request);
    }

    public function store(StoreExamTokenRequest $request)
    {
        return $this->repo->store($request);
    }

    public function show(ExamToken $model)
    {
        return $this->repo->show($model);
    }

    public function update(ExamToken $model, UpdateExamTokenRequest $request)
    {
        return $this->repo->update($model, $request);
    }

    public function delete(ExamToken $model)
    {
        return $this->repo->delete($model);
    }

    /**
     * Generate token for a specific student.
     */
    public function generateExamTokenForStudent(int $userId, string $expiration)
    {
        try {
            $examToken = strtoupper(Str::random(10));

            $expiration = Carbon::parse($expiration);

            $this->repo->updateTokenForUser($userId, $examToken, $expiration);

            return [
                'exam_token' => $examToken,
                'expiration' => $expiration
            ];
        } catch (Exception $e) {
            throw new Exception('Failed to generate token for student: ' . $e->getMessage());
        }
    }

    /**
     * Generate tokens for all students.
     */
    public function generateExamTokenForAllStudents(string $expiration)
    {
        try {
            $schoolId = auth()->user()?->schools->first()?->id;

            if (!$schoolId) {
                throw new \Exception('School ID not found for current user.');
            }

            $students = $this->repo->getStudentsBySchoolId($schoolId);

            $expiration = \Carbon\Carbon::parse($expiration);

            foreach ($students as $student) {
                $examToken = strtoupper(Str::random(10));
                $this->repo->updateTokenForUser($student->id, $examToken, $expiration);
            }
        } catch (\Exception $e) {
            throw new \Exception('Failed to generate tokens for students: ' . $e->getMessage(), 0, $e);
        }
    }
}
