<?php

namespace App\Services;

use App\Enums\PaymentStatus;
use App\Enums\TransactionType;
use App\Models\NfcCard;
use App\Repositories\NfcCard\NfcCardRepository;
use Illuminate\Http\Request;
use App\Http\Requests\NfcCard\StoreNfcCardRequest;
use App\Http\Requests\NfcCard\TopUpNfcCardRequest;
use App\Http\Requests\NfcCard\UpdateNfcCardRequest;
use App\Models\NfcCardTransaction;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class NfcCardService
{
    protected $financialCoreService;

    public function __construct(FinancialCoreService $financialCoreService)
    {
        $this->financialCoreService = $financialCoreService;
    }
    public function paginate(Request $request): LengthAwarePaginator
    {
        return NfcCard::when($request->filled('user_id'), function ($query) use ($request) {
            $query->where('user_id', $request->user_id);
        })
            ->when($request->filled('uid'), function ($query) use ($request) {
                $query->where('uid', 'like', '%' . $request->uid . '%');
            })
            ->with('user')
            ->orderByDesc('created_at')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreNfcCardRequest $request): NfcCard
    {
        $validated = $request->validated();
        return DB::transaction(fn() => NfcCard::create($validated));
    }

    public function show(NfcCard $model): NfcCard
    {
        return $model->load('user');
    }


    public function update(NfcCard $model, UpdateNfcCardRequest $request): NfcCard
    {
        return DB::transaction(function () use ($model, $request) {
            $model->update($request->validated());
            return $model;
        });
    }

    public function delete(NfcCard $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    public function getUserByUid(NfcCard $model): User
    {
        return $model->user;
    }

    public function topUp(NfcCard $model, TopUpNfcCardRequest $request): NfcCardTransaction
    {
        return DB::transaction(function () use ($model, $request) {
            $validated = $request->validated();
            if ($validated['amount'] <= 0) {
                throw ValidationException::withMessages(['amount' => 'The amount must be greater than zero.']);
            }

            // When payment proof is provided, validate it
            if ($request->hasFile('file')) {
                $validated['file'] = $request->file('file')->store('payment_proof', 'public');
            }

            $status = PaymentStatus::PENDING;
            if ($request->user()->hasRole('school_admin')) {
                $status = PaymentStatus::COMPLETED;
                $model->update(['balance' => $model->balance + $validated['amount']]);
            }

            // Add to NFC Card transaction
            $transaction = NfcCardTransaction::create([
                'nfc_card_id' => $model->id,
                'type' => TransactionType::INCOME,
                'description' => 'Top Up Kartu ' . $model->uid,
                'amount' => $validated['amount'],
                'file' => $validated['file'] ?? null,
                'status' => $status,
                'transaction_number' => $this->generateTransactionNumber(),
            ]);

            // If admin approves top up immediately, record to FinancialCore
            if ($status === PaymentStatus::COMPLETED) {
                $this->financialCoreService->createFromNfcTopUp($transaction->load('nfcCard.user'));
            }

            return $transaction;
        });
    }

    private function generateTransactionNumber()
    {
        $time = time();
        $lastTransaction = NfcCardTransaction::orderBy('id', 'desc')->first();
        if (!$lastTransaction) {
            return 'NFC-' . $time . '-0001';
        }
        $lastId = (int) substr($lastTransaction->transaction_number, 15);
        return 'NFC-' . $time . '-' . str_pad($lastId + 1, 4, '0', STR_PAD_LEFT);
    }

    public function getAllTransactions(Request $request): LengthAwarePaginator
    {
        return NfcCardTransaction::when($request->filled('nfc_card_id'), function ($query) use ($request) {
            $query->where('nfc_card_id', $request->nfc_card_id);
        })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->with('nfcCard')
            ->orderByDesc('created_at')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function approveTransaction(NfcCardTransaction $model): NfcCardTransaction
    {
        return DB::transaction(function () use ($model) {
            // Update the transaction status to completed
            $model->status = PaymentStatus::COMPLETED;
            $model->save();

            // Update the NFC card balance
            $nfcCard = $model->nfcCard;
            $nfcCard->balance += $model->amount;
            $nfcCard->save();

            // Record to FinancialCore - check transaction type
            if ($model->type === TransactionType::INCOME) {
                // Top up = income untuk sekolah (credit)
                $this->financialCoreService->createFromNfcTopUp($model->load('nfcCard.user'));
            } else {
                // Spending = pembelian siswa, income untuk sekolah (credit)
                $this->financialCoreService->createFromCashlessTransaction($model->load('nfcCard.user'));
            }

            return $model;
        });
    }

    public function rejectTransaction(NfcCardTransaction $model): NfcCardTransaction
    {
        return DB::transaction(function () use ($model) {
            // Update the transaction status to rejected
            $model->status = PaymentStatus::FAILED;
            $model->save();

            return $model;
        });
    }

    public function setDailyLimit(NfcCard $model, int $limit): NfcCard
    {
        return DB::transaction(function () use ($model, $limit) {
            $model->daily_limit = $limit;
            $model->save();
            return $model;
        });
    }
}
