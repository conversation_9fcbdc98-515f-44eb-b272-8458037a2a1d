<?php

namespace App\Services;

use App\Enums\GradeType;
use App\Enums\PredicateEnum;
use App\Models\ReportCard;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ReportCardService
{
    /* =========================
     * Pagination
     * ========================= */
    public function paginate(Request $request): LengthAwarePaginator
    {
        $q = ReportCard::query()
            ->with(['grades', 'subClassroomSubject'])
            ->leftJoin('sub_classroom_subject_has_students as scshs', function ($join) {
                $join->on('scshs.sub_classroom_subject_id', '=', 'report_cards.sub_classroom_subject_id')
                    ->on('scshs.term_id', '=', 'report_cards.term_id')
                    ->on('scshs.user_id', '=', 'report_cards.student_user_id');
            })
            ->select('report_cards.*')
            ->selectRaw('COALESCE(scshs.is_enrolled, false) as is_enrolled');

        if ($request->filled('sub_classroom_id')) {
            $q->subClassroom($request->integer('sub_classroom_id'));
        }
        if ($request->filled('subject_id')) {
            $q->subject($request->integer('subject_id'));
        }
        if ($request->filled('student_user_id')) {
            $q->where('report_cards.student_user_id', $request->integer('student_user_id'));
        }
        if ($request->filled('term_id')) {
            $q->where('report_cards.term_id', $request->integer('term_id'));
        }

        return $q->paginate($request->integer('limit') ?: config('app.pagination.max'));
    }

    /* =========================
     * Create
     * ========================= */
    public function store(\App\Http\Requests\ReportCard\StoreReportCardRequest $request): ReportCard
    {
        $v = $request->validated();

        return DB::transaction(function () use ($v) {
            $exists = ReportCard::where([
                'term_id'                  => $v['term_id'],
                'student_user_id'          => $v['student_user_id'],
                'sub_classroom_subject_id' => $v['sub_classroom_subject_id'],
                'semester'                 => $v['semester'],
            ])->exists();

            if ($exists) {
                throw new \RuntimeException('Report card already exists.');
            }

            $agg = $this->aggregateScores(
                $v['term_id'],
                $v['student_user_id'],
                $v['sub_classroom_subject_id']
            );

            $finalScore     = $this->calculateFinalScoreFromAgg($agg);
            $v['final_score'] = $finalScore;
            $v['grade']      = $this->calculateGrade($finalScore);

            $created = ReportCard::create($v);
            $this->recalculate($created->fresh());

            return $created;
        });
    }

    public function show(ReportCard $model): ReportCard
    {
        return $model;
    }

    public function update(ReportCard $model, \App\Http\Requests\ReportCard\UpdateReportCardRequest $request): ReportCard
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(ReportCard $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    /* =========================
     * Recalculate
     * ========================= */
    public function recalculate(ReportCard $rc): array
    {
        $rc->refresh();

        $agg = $this->aggregateScores($rc->term_id, $rc->student_user_id, $rc->sub_classroom_subject_id);

        $finalScore = $this->calculateFinalScoreFromAgg($agg);
        $gradeChar  = $this->calculateGrade($finalScore);

        $rc->update([
            'final_score' => round($finalScore, 2),
            'grade'       => $gradeChar,
        ]);

        return [$finalScore, $gradeChar];
    }

    /* =========================
     *  Aggregate cepat via JOIN rc
     * ========================= */
    private function aggregateScores(int $termId, int $studentUserId, int $scsId): array
    {
        $rows = DB::table('grades as g')
            ->join('report_cards as rc', 'rc.id', '=', 'g.report_card_id')
            ->selectRaw('g.type, COUNT(*) as cnt, COALESCE(SUM(g.score),0) as sum')
            ->where('g.term_id', $termId)
            ->where('g.student_user_id', $studentUserId)
            ->where('rc.sub_classroom_subject_id', $scsId)
            ->groupBy('g.type')
            ->get()
            ->keyBy('type');

        $cnt = fn(string $t) => (int) ($rows[$t]->cnt ?? 0);
        $sum = fn(string $t) => (float) ($rows[$t]->sum ?? 0);

        return [
            'assign' => ['cnt' => $cnt(GradeType::ASSIGNMENT->value), 'sum' => $sum(GradeType::ASSIGNMENT->value)],
            'mid'    => ['cnt' => $cnt(GradeType::MID_EXAM->value),    'sum' => $sum(GradeType::MID_EXAM->value)],
            'final'  => ['cnt' => $cnt(GradeType::FINAL_EXAM->value),  'sum' => $sum(GradeType::FINAL_EXAM->value)],
        ];
    }

    private function calculateFinalScoreFromAgg(array $agg): float
    {
        $avgAssign = $agg['assign']['cnt'] > 0 ? $agg['assign']['sum'] / $agg['assign']['cnt'] : 0.0;
        $avgMid    = $agg['mid']['cnt']    > 0 ? $agg['mid']['sum']    / $agg['mid']['cnt']    : 0.0;
        $avgFinal  = $agg['final']['cnt']  > 0 ? $agg['final']['sum']  / $agg['final']['cnt']  : 0.0;

        $parts = [];
        if ($agg['assign']['cnt'] > 0) $parts[] = $avgAssign;
        if ($agg['mid']['cnt']    > 0) $parts[] = $avgMid;
        if ($agg['final']['cnt']  > 0) $parts[] = $avgFinal;

        if (empty($parts)) return 0.0;
        return array_sum($parts) / count($parts);
    }

    private function calculateGrade(float $finalScore): string
    {
        return match (true) {
            $finalScore >= 90 => 'A',
            $finalScore >= 80 => 'B',
            $finalScore >= 70 => 'C',
            $finalScore >= 60 => 'D',
            default           => 'E',
        };
    }

    /* =========================
     * Upsert Assignments (tanpa kolom SCS)
     * ========================= */
    public function upsertAssignments(ReportCard $reportCard, FormRequest $request): array
    {
        $v = $request->validated();
        $assignmentGrades = $v['assignment_grades'];
        $notes            = $v['notes'] ?? null;
        $isList           = array_is_list($assignmentGrades);

        $schoolId = (int) ($reportCard->school_id ?? auth()->user()?->school_id);

        return DB::transaction(function () use ($reportCard, $assignmentGrades, $notes, $isList, $schoolId) {

            if ($isList) {
                DB::table('grades')
                    ->where('report_card_id', $reportCard->id)
                    ->where('type', GradeType::ASSIGNMENT->value)
                    ->whereNull('assignment_id')
                    ->where('school_id', $schoolId)
                    ->delete();

                $now = now();
                foreach ($assignmentGrades as $score) {
                    if ($score === null || $score === '') continue;

                    DB::table('grades')->insert([
                        'school_id'       => $schoolId,
                        'term_id'         => $reportCard->term_id,
                        'student_user_id' => $reportCard->student_user_id,
                        'report_card_id'  => $reportCard->id,
                        'assignment_id'   => null,
                        'exam_id'         => null,
                        'type'            => GradeType::ASSIGNMENT->value,
                        'score'           => (int) round($score),
                        'notes'           => $notes,
                        'created_by'      => auth()->id(),
                        'updated_by'      => auth()->id(),
                        'created_at'      => $now,
                        'updated_at'      => $now,
                    ]);
                }
            } else {
                // Merge by assignment_id
                $now = now();
                foreach ($assignmentGrades as $assignmentId => $score) {
                    if ($score === null || $score === '') continue;

                    DB::table('grades')->updateOrInsert(
                        [
                            'school_id'       => $schoolId,
                            'term_id'         => $reportCard->term_id,
                            'student_user_id' => $reportCard->student_user_id,
                            'report_card_id'  => $reportCard->id,
                            'assignment_id'   => (int) $assignmentId,
                            'type'            => GradeType::ASSIGNMENT->value,
                        ],
                        [
                            'score'      => (int) round($score),
                            'notes'      => $notes,
                            'updated_by' => auth()->id(),
                            'updated_at' => $now,
                        ]
                    );
                }
            }

            $rcFresh = $reportCard->fresh();
            [$finalScore, $grade] = $this->recalculate($rcFresh);

            $assignments = DB::table('grades')
                ->where('report_card_id', $rcFresh->id)
                ->where('type', GradeType::ASSIGNMENT->value)
                ->where('school_id', $schoolId)
                ->orderBy('id')
                ->get()
                ->all();

            return [
                'assignments' => $assignments,
                'final_score' => $finalScore,
                'grade'       => $grade,
                'report_card' => $rcFresh->load('grades'),
            ];
        });
    }

    /* =========================
     * Ensure & Revision
     * ========================= */
    public function ensureReportCardByKeys(int $termId, int $studentUserId, int $subClassroomSubjectId): ReportCard
    {
        $attrs = [
            'term_id'                  => $termId,
            'student_user_id'          => $studentUserId,
            'sub_classroom_subject_id' => $subClassroomSubjectId,
        ];

        try {
            return DB::transaction(function () use ($attrs) {
                return ReportCard::firstOrCreate(
                    $attrs,
                    ['final_score' => 0, 'grade' => null]
                );
            });
        } catch (\Throwable $e) {
            $code = (int) ($e->getCode() ?? 0);
            if (!in_array($code, [23505, 1062], true)) throw $e;
            return ReportCard::where($attrs)->firstOrFail();
        }
    }

    public function revisionScore(int $reportCardId, array $data): ReportCard
    {
        $validated = validator($data, [
            'revision_score'           => ['nullable', 'numeric', 'min:0', 'max:100'],
            'term_id'                  => ['nullable', 'integer'],
            'student_user_id'          => ['nullable', 'integer'],
            'sub_classroom_subject_id' => ['nullable', 'integer'],
        ])->validate();

        return DB::transaction(function () use ($reportCardId, $validated) {
            if ($reportCardId > 0) {
                $rc = ReportCard::findOrFail($reportCardId);
                $rc->update(['revision_score' => $validated['revision_score'] ?? null]);
                return $rc;
            }

            $termId  = (int) ($validated['term_id'] ?? 0);
            $student = (int) ($validated['student_user_id'] ?? 0);
            $scsId   = (int) ($validated['sub_classroom_subject_id'] ?? 0);
            if (!$termId || !$student || !$scsId) {
                throw new \InvalidArgumentException('term_id, student_user_id, and sub_classroom_subject_id are required when reportCardId is 0.');
            }

            $rc = ReportCard::firstOrCreate(
                ['term_id' => $termId, 'student_user_id' => $student, 'sub_classroom_subject_id' => $scsId],
                ['final_score' => null, 'revision_score' => null]
            );
            $rc->update(['revision_score' => $validated['revision_score'] ?? null]);
            return $rc;
        });
    }

    /* =========================
     * Notes import & helpers
     * ========================= */
    public function upsertNoteByKeys(array $data): ReportCard
    {
        $termId   = (int) $data['term_id'];
        $student  = (int) $data['student_user_id'];
        $scsId    = (int) $data['sub_classroom_subject_id'];
        $noteType = (string) $data['note_type'];
        $note     = $data['note'] ?? null;

        return DB::transaction(function () use ($termId, $student, $scsId, $noteType, $note) {
            $rc = $this->ensureReportCardByKeys($termId, $student, $scsId);
            $payload = match ($noteType) {
                'mid'   => ['mid_term_note'   => $note],
                'final' => ['final_term_note' => $note],
                default => throw new \InvalidArgumentException('note_type must be mid|final'),
            };
            $rc->update($payload + ['updated_by' => auth()->id()]);
            return $rc->fresh();
        });
    }

    public function importNotes(array $data): array
    {
        $termId = (int) $data['term_id'];
        $scsId  = (int) $data['sub_classroom_subject_id'];
        $type   = $data['note_type']; // 'mid' | 'final'
        $file   = $data['file'];

        $rows = Excel::toArray(null, $file)[0] ?? [];
        if (empty($rows)) return ['updated' => 0, 'skipped' => 0, 'errors' => ['File kosong']];

        $rows = array_map(function ($row) {
            return array_map(fn($cell) => $cell === null ? '' : trim(is_scalar($cell) ? (string) $cell : ''), $row);
        }, $rows);

        $header = array_map(fn($h) => Str::of($h)->lower()->trim()->toString(), $rows[0] ?? []);
        $findIndex = function (array $needles) use ($header): ?int {
            foreach ($header as $i => $h) foreach ($needles as $n) {
                if ($h === $n || Str::contains($h, $n)) return $i;
            }
            return null;
        };

        $studentIdIdx = $findIndex(['student_user_id', 'id siswa', 'id_siswa', 'id']);
        $noteIdx      = $findIndex(['catatan', 'note', 'notes']);
        $hasHeader    = $studentIdIdx !== null && $noteIdx !== null;

        if ($hasHeader) array_shift($rows);
        else {
            $studentIdIdx ??= 0;
            $noteIdx      ??= (isset($rows[0][2]) ? 2 : 1);
            $looksHeader   = Str::contains(Str::lower(implode(' ', $rows[0] ?? [])), ['id siswa', 'student', 'catatan', 'note', 'nama']);
            if ($looksHeader) array_shift($rows);
        }

        $updated = 0;
        $skipped = 0;
        $errors = [];

        DB::transaction(function () use ($rows, $termId, $scsId, $type, $studentIdIdx, $noteIdx, &$updated, &$skipped, &$errors) {

            $enrolledIds = DB::table('sub_classroom_subject_has_students')
                ->where('term_id', $termId)
                ->where('sub_classroom_subject_id', $scsId)
                ->whereNull('deleted_at')
                ->where(fn($q) => $q->whereNull('is_enrolled')->orWhere('is_enrolled', true))
                ->pluck('user_id')->all();

            $existing = ReportCard::query()
                ->where('term_id', $termId)
                ->where('sub_classroom_subject_id', $scsId)
                ->get(['id', 'term_id', 'student_user_id', 'sub_classroom_subject_id'])
                ->keyBy(fn($rc) => "{$rc->term_id}:{$rc->sub_classroom_subject_id}:{$rc->student_user_id}");

            foreach ($rows as $n => $r) {
                $cols = is_array($r) ? count($r) : 0;
                $rawStudentId = $r[$studentIdIdx] ?? ($cols > 0 ? $r[0] ?? '' : '');
                $rawNote      = $r[$noteIdx] ?? '';

                if ($rawNote === '' || $rawNote === $rawStudentId) {
                    if ($cols >= 3 && isset($r[2]) && $r[2] !== $rawStudentId) $rawNote = $r[2];
                    elseif ($cols >= 2 && isset($r[1]) && $r[1] !== $rawStudentId) $rawNote = $r[1];
                }

                if (trim($rawStudentId) === '' && trim($rawNote) === '') {
                    $skipped++;
                    continue;
                }
                if ($rawStudentId === '' || !ctype_digit((string) $rawStudentId)) {
                    $errors[] = "Baris " . ($n + 2) . ": ID Siswa tidak valid: '{$rawStudentId}'";
                    $skipped++;
                    continue;
                }
                if (trim($rawNote) === '') {
                    $skipped++;
                    continue;
                }

                $studentId = (int) $rawStudentId;
                $note      = (string) $rawNote;

                if (!in_array($studentId, $enrolledIds, true)) {
                    $errors[] = "Baris " . ($n + 2) . ": siswa {$studentId} tidak terdaftar di term/subject ini → skip.";
                    $skipped++;
                    continue;
                }

                $key = "{$termId}:{$scsId}:{$studentId}";
                $rc  = $existing->get($key);
                if (!$rc) {
                    $errors[] = "Baris " . ($n + 2) . ": report card belum ada untuk siswa {$studentId} → skip.";
                    $skipped++;
                    continue;
                }

                $payload = $type === 'mid'
                    ? ['mid_term_note'   => $note]
                    : ['final_term_note' => $note];

                try {
                    DB::table('report_cards')->where('id', $rc->id)->update($payload + [
                        'updated_by' => auth()->id(),
                        'updated_at' => now(),
                    ]);
                    $updated++;
                } catch (\Throwable $e) {
                    $errors[] = "Baris " . ($n + 2) . ": gagal update siswa {$studentId} → " . $e->getMessage();
                    $skipped++;
                }
            }
        });

        return ['updated' => $updated, 'skipped' => $skipped, 'errors' => $errors];
    }

    /* =========================
     * Helper lain yang kamu punya
     * ========================= */
    public function getStudentsForTermSubject(int $termId, int $scsId)
    {
        return DB::table('sub_classroom_subject_has_students as scshs')
            ->join('users as u', 'u.id', '=', 'scshs.user_id')
            ->join('user_school_roles as usr', 'usr.user_id', '=', 'u.id')
            ->where('usr.role_id', 4)
            ->where('scshs.term_id', $termId)
            ->where('scshs.sub_classroom_subject_id', $scsId)
            ->where(function ($q) {
                $q->whereNull('scshs.is_enrolled')->orWhere('scshs.is_enrolled', true);
            })
            ->whereNull('u.deleted_at')
            ->whereNull('scshs.deleted_at')
            ->select('u.id as student_user_id', 'u.name as student_name')
            ->distinct()
            ->orderBy('u.name')
            ->get();
    }

    public function markExportedByStudent(int $termId, int $studentId, string $stage): int
    {
        $now = now();
        $uid = auth()->id();

        if ($stage === 'mid') {
            return DB::table('report_cards')
                ->where('term_id', $termId)
                ->where('student_user_id', $studentId)
                ->update([
                    'mid_locked_score' => DB::raw('COALESCE(mid_locked_score, COALESCE(revision_score, final_score))'),
                    'mid_exported_at'  => $now,
                    'updated_by'       => $uid,
                ]);
        }

        return DB::table('report_cards')
            ->where('term_id', $termId)
            ->where('student_user_id', $studentId)
            ->update([
                'final_locked_score' => DB::raw('COALESCE(final_locked_score, COALESCE(revision_score, final_score))'),
                'final_exported_at'  => $now,
                'updated_by'         => $uid,
            ]);
    }

    private function getSchoolProfileForStudent(int $studentId): array
    {
        $row = DB::table('user_school_roles as usr')
            ->join('schools as s', 's.id', '=', 'usr.school_id')
            ->where('usr.user_id', $studentId)
            ->orderByDesc('usr.created_at')
            ->select([
                's.id',
                's.name',
                's.address',
                's.subdistrict',
                's.district',
                's.city',
                's.province',
                's.registration_number',
                's.phone_number',
                's.email',
                's.website',
                's.logo',
            ])->first();

        if (!$row) {
            return [
                'name'            => 'Nama Sekolah',
                'address'         => 'Alamat Sekolah',
                'city'            => 'Kota',
                'logo_url'        => null,
                'headmaster_name' => 'Kepala Sekolah',
                'registration'    => null,
            ];
        }

        $headmaster = DB::table('user_school_roles as usr')
            ->leftJoin('roles as r', 'r.id', '=', 'usr.role_id')
            ->join('users as u', 'u.id', '=', 'usr.user_id')
            ->where('usr.school_id', $row->id)
            ->where(function ($q) {
                $q->where('usr.role_id', 8)
                    ->orWhereRaw('LOWER(r.name) = ?', ['headmaster'])
                    ->orWhereRaw('LOWER(r.name) = ?', ['kepala sekolah']);
            })
            ->select('u.name')
            ->orderByDesc('usr.created_at')
            ->first();

        $logoUrl = !empty($row->logo) ? asset('storage/' . ltrim($row->logo, '/')) : null;

        return [
            'name'            => $row->name ?? 'Nama Sekolah',
            'address'         => $row->address ?? null,
            'city'            => $row->city ?? null,
            'website'         => $row->website ?? null,
            'email'           => $row->email ?? null,
            'logo_url'        => $logoUrl,
            'headmaster_name' => $headmaster->name ?? 'Kepala Sekolah',
            'registration'    => $row->registration_number ?? null,
        ];
    }

    public function buildReportData(int $termId, int $studentId, string $stage): array
    {
        $student   = DB::table('users')->where('id', $studentId)->first();
        $waliKelas = Auth::user()->name ?? 'Wali Kelas';

        // KELAS
        $classRow = DB::table('sub_classroom_subject_has_students as scshs')
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'scshs.sub_classroom_subject_id')
            ->join('sub_classrooms as sc', 'sc.id', '=', 'scs.sub_classroom_id')
            ->leftJoin('classrooms as c', 'c.id', '=', 'sc.classroom_id')
            ->where('scshs.user_id', $studentId)
            ->where('scshs.term_id', $termId)
            ->select('c.name as classroom_name', 'sc.sequence')
            ->first();
        $className = $classRow ? trim(($classRow->classroom_name ?? '') . ($classRow->sequence ? " - {$classRow->sequence}" : '')) : '-';

        // TERM
        $term = DB::table('terms')->where('id', $termId)->first();
        $academicYearText = $term?->academic_year ?? '2024/2025';
        $semesterText     = $term?->order == 1 ? 'Ganjil' : 'Genap';

        $periodeHeader = $stage === 'mid' ? 'TENGAH SEMESTER' : 'AKHIR SEMESTER';
        if ($term?->start_date && $term?->end_date) {
            Carbon::setLocale('id');
            $start = Carbon::parse($term->start_date);
            $end   = Carbon::parse($term->end_date);
            $periode = strtoupper($start->translatedFormat('F')) . ' - ' . strtoupper($end->translatedFormat('F')) . ' ' . $end->year;
            $periodeHeader = ($stage === 'mid' ? 'TENGAH' : 'AKHIR') . " SEMESTER PERIODE $periode";
        }

        // MAPEL + RAPOR (JOIN sekali, no N+1)
        $subjectRows = DB::table('sub_classroom_subject_has_students as scshs')
            ->join('sub_classroom_subjects as scs', 'scs.id', '=', 'scshs.sub_classroom_subject_id')
            ->join('subjects as s', 's.id', '=', 'scs.subject_id')
            ->leftJoin('report_cards as rc', function ($j) use ($termId, $studentId) {
                $j->on('rc.sub_classroom_subject_id', '=', 'scs.id')
                    ->where('rc.term_id', '=', $termId)
                    ->where('rc.student_user_id', '=', $studentId);
            })
            ->where('scshs.user_id', $studentId)
            ->where('scshs.term_id', $termId)
            ->whereNull('scshs.deleted_at')
            ->where(function ($q) {
                $q->whereNull('scshs.is_enrolled')->orWhere('scshs.is_enrolled', true);
            })
            ->orderBy('s.name')
            ->get([
                'scs.id as scs_id',
                's.name as subject_name',
                'rc.mid_locked_score',
                'rc.final_locked_score',
                'rc.revision_score',
                'rc.final_score',
                'rc.mid_term_note',
                'rc.final_term_note',
                'rc.class_teacher_note',
            ]);

        $rows = [];
        $classTeacherNote = null;
        foreach ($subjectRows as $row) {
            if ($classTeacherNote === null && isset($row->class_teacher_note)) {
                $classTeacherNote = $row->class_teacher_note;
            }
            $score = $stage === 'mid'
                ? ($row->mid_locked_score ?? $row->revision_score ?? $row->final_score ?? '-')
                : ($row->final_locked_score ?? $row->revision_score ?? $row->final_score ?? '-');
            $note = $stage === 'mid' ? ($row->mid_term_note ?? '-') : ($row->final_term_note ?? '-');

            $rows[] = [
                'nama'    => $row->subject_name,
                'nilai'   => is_numeric($score) ? (float) $score : $score,
                'capaian' => $note,
            ];
        }

        // EKSTRAKURIKULER
        $ekskulRows = DB::table('extracurricular_students as es')
            ->join('extracurriculars as e', 'e.id', '=', 'es.extracurricular_id')
            ->where('es.student_user_id', $studentId)
            ->where('es.term_id', $termId)
            ->whereNull('es.deleted_at')
            ->orderBy('e.name')
            ->get(['e.name as nama', 'es.predicate']);

        $ekstrakurikuler = [];
        foreach ($ekskulRows as $r) {
            $pred  = strtoupper(trim($r->predicate ?? '-'));
            $label = PredicateEnum::tryFrom($pred)?->label() ?? '-';
            $ekstrakurikuler[] = ['nama' => $r->nama, 'predikat' => $pred, 'keterangan' => $label];
        }
        $ekskuls = collect($ekstrakurikuler)->pluck('nama')->map(fn($v) => trim((string) $v))->filter()->unique()->values()->all();

        // KEHADIRAN
        $att  = DB::table('class_attendances')
            ->where('term_id', $termId)->where('student_user_id', $studentId)
            ->whereNull('deleted_at')->first();
        $sakit = (int)($att->sick  ?? 0);
        $izin  = (int)($att->leave ?? 0);
        $alpa  = (int)($att->alpha ?? 0);

        // SEKOLAH
        $school = $this->getSchoolProfileForStudent($studentId);

        return [
            'periode_header'     => $periodeHeader,
            'nama'               => $student->name ?? 'Nama Siswa',
            'nisn'               => $student->nisn ?? $student->registration_number ?? '-',
            'nama_sekolah'       => $school['name'] ?? 'Nama Sekolah',
            'alamat'             => $school['address'] ?? 'Alamat Sekolah',
            'sekolah_logo'       => $school['logo_url'] ?? null,
            'kelas'              => $className,
            'semester'           => $semesterText,
            'tahun_ajaran'       => $academicYearText,
            'kota'               => $school['city'] ?? 'Kota',
            'tanggal'            => now()->format('d F Y'),
            'wali_kelas'         => $waliKelas,
            'kepala_sekolah'     => $school['headmaster_name'] ?? 'Kepala Sekolah',
            'kode_sekolah'       => $school['registration'] ?? '',
            'ekskul_1'           => null,
            'ekskul_2'           => null,
            'sakit'              => $sakit,
            'izin'               => $izin,
            'alpa'               => $alpa,
            'ekstrakurikuler'    => $ekstrakurikuler,
            'ekskuls'            => $ekskuls,
            'mata_pelajaran'     => $rows,
            'class_teacher_note' => $classTeacherNote,
        ];
    }

    public function getClassTeacherNote(int $termId, int $studentUserId): ?string
    {
        return ReportCard::where('term_id', $termId)
            ->where('student_user_id', $studentUserId)
            ->value('class_teacher_note');
    }

    public function updateClassTeacherNote(int $termId, int $studentUserId, ?string $note): void
    {
        $rc = ReportCard::firstOrCreate(['term_id' => $termId, 'student_user_id' => $studentUserId]);
        $rc->update(['class_teacher_note' => $note]);
    }

    public function buildCoverData(int $studentId): array
    {
        Carbon::setLocale('id');

        $student = DB::table('users')->where('id', $studentId)->first();
        $school  = $this->getSchoolProfileForStudent($studentId);

        $nama  = $student->name ?? null;
        $nisn  = $student->registration_number ?? null;
        $birthPlace = $student->birth_place ?? 'Bekasi';
        $birthDate  = $student->birth_date ? Carbon::parse($student->birth_date)->translatedFormat('d F Y') : '14 Mei 2009';
        $religion   = $student->religion ?? 'Islam';
        $address    = $student->address ?? '-';
        $phone      = $student->phone ?? '-';
        $schoolOrigin = $student->school_origin ?? '—';

        $websiteUpper = Str::upper($school['website'] ?? 'https://smkyadika13.smarteschool.id');

        return [
            'nama_sekolah'    => $school['name'] ?? 'SMK YADIKA 13',
            'npsn'            => null,
            'tnis'            => null,
            'alamat_sekolah'  => $school['address'] ?? 'JL. RAYA VILLA BEKASI INDAH, JEJALENJAYA, KEC. TAMBUN UTARA, BEKASI, JAWA BARAT.',
            'kelurahan'       => Str::upper($school['village'] ?? 'JEJALENJAYA'),
            'kecamatan'       => Str::upper($school['district'] ?? 'TAMBUN UTARA'),
            'kota_kab'        => Str::upper($school['city'] ?? 'KAB.BEKASI'),
            'provinsi'        => Str::upper($school['province'] ?? 'JAWA BARAT'),
            'website'         => $websiteUpper,
            'email'           => Str::upper($school['email'] ?? '<EMAIL>'),

            'logo_url'        => $school['logo_url'] ?? null,
            'nama'            => $nama,
            'nisn'            => $nisn,
            'nis'             => null,
            'alamat_cover'    => $school['address_line1'] ?? 'JL. VILLA I, KP. KEBON, DS. JEJALEN JAYA',
            'kecamatan_cover' => Str::upper($school['district'] ?? 'TAMBUN UTARA'),
            'kota_cover'      => Str::upper($school['city'] ?? 'KAB. BEKASI'),
            'provinsi_cover'  => Str::upper($school['province'] ?? 'JAWA BARAT'),

            'tempat_lahir'    => $birthPlace,
            'tanggal_lahir'   => $birthDate,
            'jenis_kelamin'   => '-',
            'agama'           => $religion,
            'status_keluarga' => '-',
            'anak_ke'         => '-',
            'alamat_siswa'    => $address,
            'no_telepon'      => $phone,
            'sekolah_asal'    => $schoolOrigin,

            'kelas_diterima'   => '-',
            'fase'             => '-',
            'tanggal_diterima' => '-',

            'nama_ayah'        => '—',
            'nama_ibu'         => '—',
            'alamat_ortu'      => $address,
            'no_telepon_ortu'  => $phone,
            'pekerjaan_ayah'   => '—',
            'pekerjaan_ibu'    => '—',
            'nama_wali'        => '—',
            'alamat_wali'      => '—',
            'no_telepon_wali'  => '—',
            'pekerjaan_wali'   => '—',

            'kota_identitas'    => $school['city'] ?? 'Kab. Bekasi',
            'tanggal_identitas' => now()->translatedFormat('d F Y'),
            'kepala_sekolah'    => $school['headmaster_name'] ?? 'Kepala Sekolah',
            'kode_sekolah'      => $school['registration'] ?? '—',

            'nama_pindah'       => $student->name ?? null,
            'data_keluar'       => [],
            'data_masuk'        => [],
        ];
    }
}
