<?php

namespace App\Services;

use App\Http\Requests\Transaction\StoreTransactionRequest;
use App\Http\Requests\Transaction\UpdateTransactionRequest;
use App\Models\Transaction;
use App\Repositories\Transaction\TransactionRepository;
use App\Services\FinancialCoreService;
use Illuminate\Http\Request;

class TransactionService
{
    protected TransactionRepository $repo;
    protected FinancialCoreService $financialCoreService;

    public function __construct(TransactionRepository $repo, FinancialCoreService $financialCoreService)
    {
        $this->repo = $repo;
        $this->financialCoreService = $financialCoreService;
    }

    public function paginate(Request $request)
    {
        return $this->repo->paginate($request);
    }

    public function store(StoreTransactionRequest $request)
    {
        $transaction = $this->repo->store($request);
        
        // Record to FinancialCore
        $this->financialCoreService->createFromCanteenTransaction($transaction);
        
        return $transaction;
    }

    public function show(Transaction $model)
    {
        return $this->repo->show($model);
    }

    public function update(Transaction $model, UpdateTransactionRequest $request)
    {
        return $this->repo->update($model, $request);
    }

    public function delete(Transaction $model)
    {
        return $this->repo->delete($model);
    }
}
