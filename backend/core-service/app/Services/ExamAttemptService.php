<?php

namespace App\Services;

use App\Http\Requests\ExamAttempt\ExamAttemptAnswerRequest;
use App\Jobs\SyncAttemptToReportCardJob;
use App\Models\ExamAttempt;
use App\Models\ExamAttemptAnswer;
use App\Repositories\ExamAttempt\ExamAttemptRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;

class ExamAttemptService
{
    protected ExamAttemptRepository $repository;
    protected ExamService $examService;
    protected ExamQuestionService $examQuestionService;

    /**
     * Create a new service instance.
     *
     * @param ExamAttemptRepository $repository
     */
    public function __construct(ExamAttemptRepository $repository, ExamService $examService, ExamQuestionService $examQuestionService)
    {
        $this->repository = $repository;
        $this->examService = $examService;
        $this->examQuestionService = $examQuestionService;
    }

    /**
     * Show a specific exam attempt.
     *
     * @param ExamAttempt $examAttempt
     * @return ExamAttempt
     * @throws ModelNotFoundException
     */
    public function show(ExamAttempt $examAttempt)
    {
        $user = auth()->user();
        if ($examAttempt->user_id != $user->id) {
            throw new ModelNotFoundException("Exam attempt not for this user.");
        }
        return $this->repository->show($examAttempt);
    }

    public function showQuestion(ExamAttempt $examAttempt, ExamAttemptAnswer $examAttemptAnswer)
    {
        $user = auth()->user();
        if ($examAttempt->user_id != $user->id || $examAttemptAnswer->exam_attempt_id != $examAttempt->id) {
            throw new ModelNotFoundException("Exam attempt not for this user.");
        }
        return $this->repository->showQuestion($examAttempt, $examAttemptAnswer);
    }


    public function answer(
        ExamAttempt              $examAttempt,
        ExamAttemptAnswer        $examAttemptAnswer,
        ExamAttemptAnswerRequest $request
    ) {
        $user = auth()->user();
        if ($examAttempt->user_id != $user->id || $examAttemptAnswer->exam_attempt_id != $examAttempt->id) {
            throw new ModelNotFoundException("Exam attempt not for this user.");
        }
        $this->examService->isExamEnded($examAttempt->exam_id);
        return $this->repository->answer($examAttemptAnswer, $request);
    }

    public function gradeEssay(int $examAttemptId, array $answer)
    {
        return DB::transaction(function () use ($examAttemptId, $answer) {

            $this->repository->updateEssayScore($examAttemptId, $answer);

            $attempt = $this->repository->findWithAnswers($examAttemptId);

            $score = 0;
            $correctCount = 0;

            foreach ($attempt->answers as $ans) {
                if ($ans->examQuestion->type === 'multiple_choice') {
                    if ($ans->selectedOption && $ans->selectedOption->is_correct) {
                        $score += $ans->examQuestion->points;
                        $correctCount++;
                    }
                } elseif ($ans->examQuestion->type === 'essay') {
                    $score += $ans->points_awarded ?? 0;
                    if (($ans->points_awarded ?? 0) >= ($ans->examQuestion->points * 0.5)) {
                        $correctCount++;
                    }
                }
            }

            $attempt->update([
                'score' => $score,
                'correct_count' => $correctCount,
                'graded_at' => now(),
                'status' => 'graded',
                'updated_by' => auth()->id(),
            ]);

            if ($attempt && $attempt->status === 'graded') {
                SyncAttemptToReportCardJob::dispatchSync($attempt->id);
            }

            return $attempt;
        });
    }
}
