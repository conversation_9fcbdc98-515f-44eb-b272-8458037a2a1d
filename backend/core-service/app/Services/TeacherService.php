<?php

namespace App\Services;

use App\Exports\TeachersExport;
use App\Imports\Teacher\TeachersImportHandler;
use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class TeacherService
{
    public function paginate(Request $request)
    {
        $activeSchoolId = session('active_school_id');
        return School::find($activeSchoolId)->teachers()
            ->with('subClassroom')
            ->when($request->search, function ($query) use ($request) {
                $search = mb_strtolower($request->search);

                // Search by name
                $query->where(function ($q) use ($search) {
                    $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"]);
                });

                // Search by registration number
                $query->orWhereRaw('LOWER(registration_number) LIKE ?', ["%{$search}%"]);
            })
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function import(\Illuminate\Http\UploadedFile $file, int $schoolId): array
    {
        if (!$schoolId) {
            throw new \InvalidArgumentException('school_id wajib dikirim saat import.');
        }
        
        Excel::import(new TeachersImportHandler($schoolId, 'teacher'), $file);
        return ['status' => true, 'message' => 'Import data guru berhasil'];
    }

    public function export(): BinaryFileResponse
    {
        return Excel::download(new TeachersExport, 'teachers_export_' . now()->format('Ymd_His') . '.xlsx');
    }
}
