<?php

namespace App\Services;

use App\Exports\Exam\QuestionGridExport;
use App\Exports\QuestionsCardStackedExport;
use App\Http\Requests\Exam\StartExamRequest;
use App\Models\Exam;
use App\Repositories\Exam\ExamRepository;
use App\Repositories\ExamAttempt\ExamAttemptRepository;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use App\Http\Requests\Exam\StoreExamRequest;
use App\Http\Requests\Exam\UpdateExamRequest;
use App\Models\ExamAttempt;
use App\Models\ExamQuestion;
use App\Models\SubClassroomSubject;
use App\Models\Term;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;

class ExamService
{
    protected ExamRepository $repo;
    protected ExamAttemptRepository $examAttemptRepository;

    public function __construct(ExamRepository $repo, ExamAttemptRepository $examAttemptRepository)
    {
        $this->repo = $repo;
        $this->examAttemptRepository = $examAttemptRepository;
    }

    public function paginate(Request $request)
    {
        $user = auth()->user();

        if ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin')) {
            return $this->repo->paginateAll($request);
        } elseif ($user->hasRole('teacher')) {
            $request->merge(['user_id' => $user->id]);
            return $this->repo->paginateForTeacher($request);
        } elseif ($user->hasRole('student')) {
            $request->merge(['user_id' => $user->id]);
            return $this->repo->paginateForStudent($request);
        }
    }

    public function store(StoreExamRequest $req): Exam
    {
        return DB::transaction(function () use ($req) {
            $data = $req->validated();

            // Guard tambahan: pastikan range waktu di dalam term (redundan dgn rule, tapi aman)
            $term = Term::findOrFail($data['term_id']);
            if (
                $req->date('start_datetime')->lt($term->start_date->startOfDay()) ||
                $req->date('end_datetime')->gt($term->end_date->endOfDay())
            ) {
                throw ValidationException::withMessages(['term_id' => 'Waktu ujian di luar rentang term.']);
            }

            return Exam::create($data);
        });
    }

    public function show(Exam $model)
    {
        $user = auth()->user();

        if ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin')) {
            return $this->repo->showWithAllRelations($model->id);
        }

        if ($user->hasRole('teacher')) {
            return $this->repo->showForTeacher($model);
        } elseif ($user->hasRole('student')) {
            return $this->repo->showForStudent($model, $user->id);
        }
    }

    public function update(UpdateExamRequest $req, int $id): Exam
    {
        return DB::transaction(function () use ($req, $id) {
            $exam = Exam::findOrFail($id);
            $data = $req->validated();

            if (isset($data['term_id']) || isset($data['start_datetime']) || isset($data['end_datetime'])) {
                $termId = $data['term_id'] ?? $exam->term_id;
                $start  = isset($data['start_datetime']) ? $req->date('start_datetime') : $exam->start_datetime;
                $end    = isset($data['end_datetime'])   ? $req->date('end_datetime')   : $exam->end_datetime;

                $term = Term::findOrFail($termId);
                if ($start->lt($term->start_date->startOfDay()) || $end->gt($term->end_date->endOfDay())) {
                    throw ValidationException::withMessages(['term_id' => 'Waktu ujian di luar rentang term.']);
                }
            }

            $exam->update($data);
            return $exam;
        });
    }

    public
    function delete(Exam $model)
    {
        $this->validateExamAndTeacherAuthorization($model);
        $this->validateExamNotPublished($model);
        return $this->repo->delete($model);
    }

    public
    function publish(Exam $model)
    {
        $this->validateExamAndTeacherAuthorization($model);
        $this->validateExamNotPublished($model);
        return $this->repo->publish($model);
    }

    public
    function unpublish(Exam $model)
    {
        $this->validateExamAndTeacherAuthorization($model);
        if (!$model->is_published) {
            throw ValidationException::withMessages([
                'exam' => ['Cannot unpublish an exam that is not published.'],
            ]);
        }

        $now = now();
        $examStartTime = $model->start_datetime;
        if (!$examStartTime) {
            throw ValidationException::withMessages([
                'exam' => ['Cannot unpublish an exam with no specified start time.'],
            ]);
        }
        if ($now->gte($examStartTime)) {
            throw ValidationException::withMessages([
                'exam' => ['Cannot unpublish an exam that has already started.'],
            ]);
        }

        return $this->repo->unpublish($model);
    }


    public function validateExamAndTeacherAuthorization(Exam $model): void
    {
        $user = auth()->user();

        if (!$model || !$model->exists) {
            throw new ModelNotFoundException('Exam not found.');
        }

        if (method_exists($user, 'hasRole') && ($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            return;
        }

        if (!(method_exists($user, 'hasRole') && $user->hasRole('teacher'))) {
            throw new AuthorizationException('Only teachers can perform this action on exams.');
        }

        $teacherId = (method_exists($user, 'detail') && $user->detail())
            ? (int) $user->detail()->id
            : (int) $user->id;

        $isAssignedTeacher = DB::table('sub_classroom_subjects')
            ->whereNull('deleted_at')
            ->where('id', $model->sub_classroom_subject_id)
            ->where('teacher_user_id', $teacherId)
            ->exists();

        if (!$isAssignedTeacher) {
            throw new AuthorizationException(
                'You can only perform this action on exams for sub classroom subjects you are assigned to teach.'
            );
        }
    }

    protected function validateExamToken(string $token): void
    {
        $user = auth()->user();

        if (!$user->exam_token || !$user->exam_token_expiration) {
            throw new \Exception('You must have a valid exam token to start the exam.');
        }

        if ($user->exam_token !== $token) {
            throw new \Exception('Invalid exam token.');
        }

        if (now()->gt($user->exam_token_expiration)) {
            throw new \Exception('Your exam token has expired.');
        }
    }


    public function start(Exam $exam, StartExamRequest $request)
    {
        $this->validateExamIsPublished($exam);
        $this->isExamAttemptStartedForStudent($exam);
        $this->validateExamForStudent($exam);

        $examToken = $request->input('exam_token');
        $this->validateExamToken($examToken);
        return $this->examAttemptRepository->createAttempt($exam);
    }

    public function submit(Exam $exam)
    {
        $this->validateExamIsPublished($exam);
        $this->isExamAttemptFinishedForStudent($exam);
        $this->validateExamForStudent($exam);
        return $this->examAttemptRepository->submitAttempt($exam);
    }

    public function grade(Exam $exam)
    {
        $now = now();
        if ($now->lt($exam->end_datetime)) {
            throw new Exception('This exam is not over end datetime yet.');
        }
        return $this->repo->grade($exam);
    }

    public function paginateResult(Exam $exam, Request $request)
    {
        $request->merge(['exam_id' => $exam->id]);
        return $this->repo->paginateResult($request);
    }


    public function validateExamNotPublished(Exam $model, $user = null)
    {
        if ($user !== null && method_exists($user, 'hasRole') && (
            $user->hasRole('school_admin') ||
            $user->hasRole('superadmin') ||
            $user->hasRole('foundation_admin')
        )) {
            return;
        }

        if ($model->is_published) {
            throw new ModelNotFoundException("This exam is already published.");
        }
    }


    public function validateExamIsPublished(Exam $model)
    {
        if (!$model->is_published) {
            throw new ModelNotFoundException("This exam has not been published.");
        }
    }

    public function validateExamForStudent(Exam $exam)
    {
        $now = now();
        $user = auth()->user();

        if ($now->lt($exam->start_datetime)) {
            throw new Exception('This exam has not started yet.');
        }

        if ($now->gt($exam->end_datetime)) {
            throw new Exception('This exam has already ended.');
        }

        $isStudentEnrolled = DB::table('sub_classroom_subject_has_students')
            ->join('sub_classroom_subjects', 'sub_classroom_subject_has_students.sub_classroom_subject_id', '=', 'sub_classroom_subjects.id')
            ->where('sub_classroom_subject_has_students.user_id', $user->id)
            ->where('sub_classroom_subjects.id', $exam->sub_classroom_subject_id)
            ->exists();


        if (!$isStudentEnrolled) {
            throw new Exception('You are not enrolled in the subject for this exam.');
        }

        return true;
    }

    public function isExamAttemptStartedForStudent(Exam $exam)
    {
        $user = auth()->user();
        $existingAttempt = $this->examAttemptRepository->findByExamAndStudent($exam, $user->id);
        if ($existingAttempt) {
            throw new Exception('Exam Attempt has been started.');
        }
    }

    public function isExamAttemptFinishedForStudent(Exam $exam)
    {
        $user = auth()->user();
        $existingAttempt = $this->examAttemptRepository->findByExamAndStudent($exam, $user->id);
        if (!$existingAttempt) {
            throw new Exception('Exam Attempt not found.');
        }
        if ($existingAttempt->status == 'completed') {
            throw new Exception('Exam Attempt has been completed.');
        }
    }

    public function isExamEnded(int $examId)
    {
        $exam = $this->repo->showById($examId);
        if (now()->gt($exam->end_datetime)) {
            throw new Exception('This exam has already ended.');
        }
    }

    public function countStudentExam(Exam $model)
    {
        return $this->repo->countStudentExam($model);
    }

    public function resetStudentExam(Exam $exam, User $student): void
    {
        $attempt = $this->getValidatedStudentAttempt($exam, $student);

        $this->examAttemptRepository->resetAttemptStatus($attempt);
    }

    public function forceSubmitStudentExam(Exam $exam, User $student): void
    {
        $attempt = $this->getValidatedStudentAttempt($exam, $student);

        if ($attempt->status === 'completed') {
            abort(422, 'Exam has already been submitted.');
        }

        $this->examAttemptRepository->forceSubmit($attempt);
    }

    /**
     * Validasi hak akses dan ambil attempt siswa
     */
    private function getValidatedStudentAttempt(Exam $exam, User $student): ExamAttempt
    {
        if (!$student->hasRole('student')) {
            abort(422, 'Target user is not a student.');
        }

        if ($exam->created_by !== auth()->id()) {
            abort(403, 'You are not authorized to manage this exam.');
        }

        $attempt = $this->examAttemptRepository->getLatestAttempt($exam->id, $student->id);

        if (!$attempt) {
            abort(404, 'No exam attempt found.');
        }

        return $attempt;
    }

    public function getStudentAttempt(int $examId, int $studentId, int $attemptId)
    {
        return $this->examAttemptRepository->findAttemptDetails($examId, $studentId, $attemptId);
    }

    public function duplicateExam(Exam $exam, array $payload): Exam
    {
        $user = auth()->user();

        if (!($user->hasRole('teacher') || $user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            abort(403, 'Unauthorized');
        }

        return $this->repo->duplicateExamDeep($exam, $payload, $user->id);
    }

    public function assignDirectlyToSubClassroomSubject(Exam $exam, int $scsId): Exam
    {
        $user = auth()->user();

        if (!$user || !($user->hasRole('school_admin') || $user->hasRole('superadmin') || $user->hasRole('foundation_admin'))) {
            abort(403, 'Unauthorized');
        }

        $scs = SubClassroomSubject::findOrFail($scsId);

        $exam->sub_classroom_subject_id = $scs->id;
        $exam->created_by = $scs->teacher_user_id;
        $exam->updated_by = $user->id;
        $exam->save();

        return $exam->load([
            'subClassroomSubject.teacher',
            'subClassroomSubject.subject',
            'subClassroomSubject.subClassroom.classroom',

        ]);
    }


    private function quillHtmlToExportText(?string $html): string
    {
        $html = (string) $html;
        if ($html === '') return '';

        $out = preg_replace_callback(
            '/<span\b([^>]*)\bclass="([^"]*?\bql-formula\b[^"]*)"(.*?)>(.*?)<\/span>/is',
            function ($m) {
                $attrs = $m[1] . ' ' . $m[3];
                $classes = $m[2] ?? '';
                $innerHtml = $m[4] ?? '';

                $tex = null;
                if (preg_match('/\bdata-value="([^"]*)"/i', $attrs, $mm)) {
                    $tex = html_entity_decode($mm[1], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                }
                if ($tex === null || $tex === '') {
                    $tex = trim(html_entity_decode(strip_tags($innerHtml), ENT_QUOTES | ENT_HTML5, 'UTF-8'));
                }
                $isBlock = preg_match('/\bblock-math\b/i', $classes) === 1;
                return $isBlock ? '$$' . $tex . '$$' : '$' . $tex . '$';
            },
            $html
        );

        $out = preg_replace_callback('/<a\b[^>]*href="([^"]+)"[^>]*>(.*?)<\/a>/is', function ($m) {
            $href = html_entity_decode($m[1], ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $text = trim(html_entity_decode(strip_tags($m[2]), ENT_QUOTES | ENT_HTML5, 'UTF-8'));
            if ($text === '') $text = $href;
            return $text . ' (' . $href . ')';
        }, $out);

        $out = preg_replace_callback('/<sub>(.*?)<\/sub>/is', function ($m) {
            $t = trim(html_entity_decode(strip_tags($m[1]), ENT_QUOTES | ENT_HTML5, 'UTF-8'));
            return (mb_strlen($t) === 1) ? '_' . $t : '_{' . $t . '}';
        }, $out);

        $out = preg_replace_callback('/<sup>(.*?)<\/sup>/is', function ($m) {
            $t = trim(html_entity_decode(strip_tags($m[1]), ENT_QUOTES | ENT_HTML5, 'UTF-8'));
            return (mb_strlen($t) === 1) ? '^' . $t : '^{' . $t . '}';
        }, $out);

        $out = strip_tags($out);

        $out = html_entity_decode($out, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $out = preg_replace('/\s+/u', ' ', $out);
        return trim($out);
    }


    public function exportUnifiedByExamId(int $examId): BinaryFileResponse
    {
        $exam = Exam::with([
            'subClassroomSubject.subject',
            'subClassroomSubject.subClassroom.classroom'
        ])->findOrFail($examId);

        $rows = $this->repo->getUnifiedRowsForExport($examId)->values();

        $T = fn($v) => $this->quillHtmlToExportText($v);

        $dataRows = $rows->map(fn($r) => [
            $r['no'],
            $r['type'],
            $r['code'],
            $T($r['indicator']),
            $T($r['question']),
            $T($r['a']),
            $T($r['b']),
            $T($r['c']),
            $T($r['d']),
            $T($r['e']),
            $r['answer_key_option'],
            $r['answer_key_essay'],
            $r['cognitive_level'],
            $T($r['learning_outcome'] ?? ''),
        ])->all();

        $schoolName    = $exam->subClassroomSubject?->subClassroom?->classroom?->school->name ?? 'SMK Yadika 13';
        $className     = $exam->subClassroomSubject?->subClassroom?->classroom?->name ?? '-';
        $semester      = $exam->subClassroomSubject?->subClassroom?->semester ?? '-';
        $classSemester = trim($className . ' / ' . $semester, ' /-');
        $subjectName   = $exam->subClassroomSubject?->subject?->name ?? 'Mata Pelajaran';
        $teacherName   = $exam->subClassroomSubject?->teacher?->name ?? 'Guru Mapel';

        return Excel::download(
            new QuestionsCardStackedExport(
                sheetTitle: 'Kartu Soal',
                schoolName: $schoolName,
                classSemester: $classSemester,
                subjectName: $subjectName,
                teacherName: $teacherName,
                academicYear: '2024 / 2025',
                rows: $dataRows
            ),
            'kartu-soal-exam-' . $examId . '-sheet.xlsx'
        );
    }


    public function downloadGrid(Request $request, int $examId): BinaryFileResponse
    {
        $exam = Exam::with([
            'subClassroomSubject.subject',
            'subClassroomSubject.subClassroom.classroom',
            'subClassroomSubject.teacher', // guru mapel
            // 'school', // aktifkan jika ada relasi
        ])->findOrFail($examId);

        $questions = ExamQuestion::where('exam_id', $examId)
            ->orderBy('order_index')
            ->get();

        $normalizeType = function ($type): string {
            if ($type instanceof \BackedEnum && method_exists($type, 'label')) {
                return $type->label();
            }
            $s = trim(strtolower((string) $type));
            if ($s === '') return 'Tidak Diketahui';

            $map = [
                'multiple_choice' => 'Pilihan ganda',
                'essay'           => 'Esai',
            ];
            return $map[$s] ?? ucwords($s);
        };

        $totalQuestions = $questions->count();

        $perType = $questions->groupBy(function ($q) use ($normalizeType) {
            return $normalizeType($q->question_type);
        });

        $formSummary = $perType->isEmpty()
            ? '-'
            : $perType->map(fn($items, $label) => "{$label}: " . $items->count())
            ->values()->implode('; ');

        $start = $exam->start_datetime ? Carbon::parse($exam->start_datetime) : null;
        $end   = $exam->end_datetime ? Carbon::parse($exam->end_datetime) : null;
        $durationMinutes = ($start && $end && $end->gt($start)) ? $start->diffInMinutes($end) : null;

        $testDate = $start ? $start->timezone(config('app.timezone', 'Asia/Jakarta'))->format('d F Y H:i') : '-';

        $className = $exam->subClassroomSubject->subClassroom->classroom->name ?? '-';

        $examTypeLabel = $request->string('exam_type')->toString();
        if (!$examTypeLabel && !empty($exam->exams_type)) {
            if ($exam->exams_type instanceof \BackedEnum && method_exists($exam->exams_type, 'label')) {
                $examTypeLabel = $exam->exams_type->label();
            } else {
                $examTypeLabel = ucwords(Str::of((string) $exam->exams_type)->replace(['_', '-'], ' '));
            }
        }
        $classExam = trim(($className ?: '-') . ($examTypeLabel ? " / {$examTypeLabel}" : ''));

        $teacherName = $exam->subClassroomSubject->teacher->name
            ?? $request->string('teacher_name')->toString()
            ?: '-';

        $subjectName = $exam->subClassroomSubject->subject->name ?? '-';

        $rows = [];
        foreach ($questions as $i => $q) {
            $level = $q->level_kognitif;
            if ($level instanceof \BackedEnum && method_exists($level, 'label')) {
                $level = $level->label();
            } elseif ($level instanceof \BackedEnum) {
                $level = $level->value;
            } else {
                $level = (string) $level;
            }

            $number = $i + 1;
            if (is_numeric($q->order_index)) {
                $oi = (int) $q->order_index;
                if ($oi >= 1 && $oi <= ($i + 1) + 1) {
                    $number = $oi;
                }
            }
            $contentExport = $this->quillHtmlToExportText($q->content ?? '');

            $rows[] = [
                'no'                    => $i + 1,
                'kd_number'             => $q->kd_number,
                'learning_outcome'      => $q->learning_outcome,
                'competency_indicator'  => $q->competency_indicator,
                'content'               => $contentExport,
                'level_kognitif'        => $level,
                'question_type'         => $normalizeType($q->question_type),
                'tingkat_kesukaran'     => null,
                'sumber_buku'           => null,
                'number'                => $number,
            ];
        }

        // --- Meta header (kop + info) ---
        $govLine1   = $request->string('gov_line1')->toString() ?: 'PEMERINTAH DAERAH PROVINSI BEKASI';
        $govLine2   = $request->string('gov_line2')->toString() ?: 'DINAS PENDIDIKAN';
        $govAddress = $request->string('gov_address')->toString()
            ?: 'Jl. Raya Villa Bekasi Indah, Jejalenjaya, Kec. Tambun Utara, Bekasi, Jawa Barat  telp. -  Bekasi - -';

        $schoolName = $request->string('school_name')->toString()
            ?: ($exam->school->name ?? 'SMK Yadika 13');

        $meta = [
            'title'            => 'KISI-KISI SOAL',
            'school'           => $schoolName,
            'subject'          => $subjectName,
            'class_semester'   => $request->string('class_semester')->toString() ?: $className,
            'year'             => $request->string('academic_year')->toString()
                ?: now()->format('Y') . '/' . now()->addYear()->format('Y'),
            'major'            => $request->string('kompetensi_keahlian')->toString() ?: null,
            'exam_type'        => $examTypeLabel ?: null,

            // Header kanan (info ujian)
            'test_date'        => $testDate,
            'class_exam'       => $classExam ?: '-',
            'form_summary'     => $formSummary,
            'total_time'       => $totalQuestions . ' soal' . ($durationMinutes ? " / {$durationMinutes} menit" : ''),
            'teacher_name'     => $teacherName,

            // Kop instansi
            'gov_line1'        => $govLine1,
            'gov_line2'        => $govLine2,
            'gov_address'      => $govAddress,
        ];

        return Excel::download(new QuestionGridExport($rows, $meta), "kisi-kisi-exam-{$examId}.xlsx");
    }
}
