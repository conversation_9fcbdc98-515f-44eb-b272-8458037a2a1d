<?php

namespace App\Services;

use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\Auth\ChangeProfileRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\SendResetLinkRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Resources\UserResource;
use App\Models\School;
use App\Models\StudentParent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class AuthService
{
    /**
     * Generate a random password for new users.
     *
     * @return string
     */
    private function generateRandomPassword(): string
    {
        // Generate random password with 8 characters: letters and numbers
        return Str::random(8);
    }

    public function register(RegisterRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();

            // Generate random password for new users (except if password is explicitly provided)
            $randomPassword = $this->generateRandomPassword();
            $validated['default_password'] = $randomPassword;

            // Use random password as the actual password initially
            $validated['password'] = Hash::make($randomPassword);

            // Determine if user should be automatically activated
            $validated['is_active'] = $this->shouldAutoActivateUser($request, $validated);

            // Remove role and parent_user_id from user data as they're handled separately
            $role = $validated['role'] ?? null;
            $parentUserId = $validated['parent_user_id'] ?? null;
            unset($validated['role']);
            unset($validated['parent_user_id']);

            // Create the user
            $user = User::create($validated);

            // Assign role if provided
            $schoolId = $request->header('X-School-ID');
            if ($schoolId) {
                $user->assignSchoolRole(School::find($schoolId), $role);
            } else if ($role) {
                $user->assignRole($role);
            }

            // Link student to parent if both role is student and parent_user_id is provided
            if ($role === 'student' && $parentUserId) {
                // Validate parent exists and has parent role
                $parent = User::find($parentUserId);
                if ($parent && $parent->hasRole('parent')) {
                    // Additional validation: check if parent belongs to the same school (if school context exists)
                    $schoolId = $request->header('X-School-ID');
                    if (!$schoolId || $parent->schools()->where('school_id', $schoolId)->exists()) {
                        StudentParent::create([
                            'student_user_id' => $user->id,
                            'parent_user_id' => $parentUserId,
                        ]);
                    }
                }
            }

            return new UserResource($user);
        });
    }

    /**
     * Determine if a user should be automatically activated based on role and requester permissions.
     *
     * @param RegisterRequest $request
     * @param array $validated
     * @return bool
     */
    private function shouldAutoActivateUser(RegisterRequest $request, array $validated): bool
    {
        $role = $validated['role'] ?? null;
        $currentUser = $request->user();

        // Auto-activate if role is school_admin or parent
        if (in_array($role, ['school_admin', 'parent'])) {
            return true;
        }

        // Auto-activate if current user is superadmin
        if ($currentUser && $currentUser->is_superadmin) {
            return true;
        }

        // Auto-activate if current user is foundation_admin or school_admin
        if ($currentUser && ($currentUser->hasRole('school_admin') || $currentUser->hasRole('foundation_admin'))) {
            return true;
        }

        // Default to inactive for other cases
        return false;
    }

    public function login(LoginRequest $request)
    {
        $validated = $request->validated();

        $loginInput = $validated['login'];
        $password = $validated['password'];

        $fieldType = filter_var($loginInput, FILTER_VALIDATE_EMAIL) ? 'email' : 'registration_number';

        $credentials = [$fieldType => $loginInput, 'password' => $password];

        // Try normal login first
        if (!$token = JWTAuth::attempt($credentials)) {
            // If normal login fails, try with default password
            $user = User::where($fieldType, $loginInput)->first();

            if ($user && $user->default_password && $password === $user->default_password) {
                // Login with default password successful
                $token = JWTAuth::fromUser($user);
            } else {
                return [
                    'error' => true,
                    'message' => 'Invalid credentials',
                    'status' => 401,
                ];
            }
        }

        $user = Auth::user() ?? JWTAuth::user();

        // If user is not set (in case of default password login), get from token
        $user = JWTAuth::setToken($token)->toUser();

        if (!$user->is_active) {
            return [
                'error' => true,
                'message' => 'User account is inactive',
                'status' => 403,
            ];
        }

        return [
            'token' => $token,
            'user' => new UserResource($user->load('subClassroom')),
        ];
    }

    public function logout()
    {
        JWTAuth::invalidate(JWTAuth::getToken());
        return true;
    }

    public function refresh()
    {
        return JWTAuth::refresh(JWTAuth::getToken());
    }

    public function profile()
    {
        $user = Auth::user() ?? JWTAuth::user();
        return $user->load('subClassroom');
    }

    public function sendResetLinkEmail(SendResetLinkRequest $request)
    {
        $email = $request->input('email');

        $user = User::where('email', $email)->first();

        if (!$user) {
            return response()->json([
                'error' => true,
                'message' => 'Email not found',
            ], 404);
        }

        $status = Password::sendResetLink(['email' => $email]);

        if ($status === Password::RESET_LINK_SENT) {
            return response()->json([
                'error' => false,
                'message' => __($status),
            ], 200);
        }

        return response()->json([
            'error' => true,
            'message' => __($status),
        ], 400);
    }


    public function resetPassword(ResetPasswordRequest $request)
    {
        $email = $request->input('email', request()->query('email'));
        $token = $request->input('token', request()->query('token'));
        $password = $request->input('password');

        $status = Password::reset(
            compact('email', 'password', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->save();
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([
                'error' => false,
                'message' => 'Password has been reset successfully',
            ], 200);
        }

        return response()->json([
            'error' => true,
            'message' => __($status),
        ], 400);
    }


    public function changePassword(ChangePasswordRequest $request): bool|string
    {
        $user = $request->user() ?? JWTAuth::user();
        $validated = $request->validated();

        if (!$user) {
            throw new \Exception('User not authenticated');
        }

        $curr = $validated['current_password'];
        if (!(Hash::check($curr, $user->password) || $curr == $user->default_password)) {
            throw new \Exception('Current password is incorrect');
        }

        $user->password = Hash::make($validated['new_password']);

        $user->clearDefaultPassword();

        return true;
    }


    public function update(UpdateUserRequest $request, User $user)
    {
        $data = $request->validated();

        // Extract and handle parent assignment separately
        $parentUserId = $data['parent_user_id'] ?? null;
        unset($data['parent_user_id']);

        // Handle password hashing and track if changed
        $passwordChanged = $this->handlePasswordUpdate($data);

        // Handle parent assignment if provided
        if ($parentUserId) {
            $this->assignParentToUser($user, $parentUserId);
        }

        // Update user data and handle password clearing
        $this->updateUserData($user, $data, $passwordChanged, $request);

        return new UserResource($user->fresh());
    }

    public function getAllUsers(Request $request)
    {
        $query = User::query();

        if ($request->has('search')) {
            $search = strtolower($request->input('search'));

            $query->where(function ($q) use ($search) {
                $q->whereRaw('LOWER(name) LIKE ?', ["%{$search}%"])
                    ->orWhereRaw('LOWER(registration_number) LIKE ?', ["%{$search}%"]);
            });
        }


        if ($request->has('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->input('role'));
            });
        }

        return $query->with(['roles', 'subClassroom'])
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    /**
     * Handle password hashing and return whether password was changed
     *
     * @param array &$data Reference to data array to modify
     * @return bool Whether password was changed
     */
    private function handlePasswordUpdate(array &$data): bool
    {
        if (!isset($data['password'])) {
            return false;
        }

        $data['password'] = Hash::make($data['password']);
        return true;
    }

    /**
     * Assign a parent to a user with validation
     *
     * @param User $user
     * @param int $parentUserId
     * @throws \Exception
     */
    private function assignParentToUser(User $user, int $parentUserId): void
    {
        $parent = User::find($parentUserId);

        if (!$parent || !$parent->hasRole('parent')) {
            throw new \Exception('Selected user is not a parent');
        }

        // Skip if parent is already assigned
        if ($user->parent && $user->parent->contains($parent)) {
            return;
        }

        $user->assignParent($parent);
    }

    /**
     * Update user data and handle password clearing
     *
     * @param User $user
     * @param array $data
     * @param bool $passwordChanged
     * @param UpdateUserRequest $request
     */
    private function updateUserData(User $user, array $data, bool $passwordChanged, UpdateUserRequest $request): void
    {
        if ($user->exists) {
            $user->update($data);

            // Clear default password if password was changed
            if ($passwordChanged && method_exists($user, 'clearDefaultPassword')) {
                $user->clearDefaultPassword();
            }
        } else {
            // Handle case where user doesn't exist (update current user)
            $currentUser = $request->user();
            if ($currentUser) {
                $currentUser->update($data);

                if ($passwordChanged && method_exists($currentUser, 'clearDefaultPassword')) {
                    $currentUser->clearDefaultPassword();
                }
            }
        }
    }

    public function changePhotoProfile(ChangeProfileRequest $request): bool
    {
        $user = $request->user() ?? JWTAuth::user();
        $request->validated();

        if (!$user) {
            return false;
        }

        if ($request->hasFile('photo')) {
            if ($user->photo && Storage::disk('public')->exists($user->photo)) {
                Storage::disk('public')->delete($user->photo);
            }

            // Simpan foto baru
            $path = $request->file('photo')->store('photo_profile', 'public');
            $user->photo = $path;
            $user->save();

            return true;
        }

        return false;
    }
}
