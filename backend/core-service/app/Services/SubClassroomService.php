<?php

namespace App\Services;

use App\Models\SubClassroom;
use App\Repositories\SubClassroom\SubClassroomRepository;
use Illuminate\Http\Request;

class SubClassroomService
{
    protected SubClassroomRepository $repo;

    public function __construct(SubClassroomRepository $repo)
    {
        $this->repo = $repo;
    }

    public function paginate(Request $request)
    {
        return $this->repo->paginate($request);
    }

    public function store(array $data)
    {
        return $this->repo->store($data);
    }

    public function show(SubClassroom $subClassroom)
    {
        return $this->repo->show($subClassroom);
    }

    public function update(SubClassroom $subClassroom, array $data)
    {
        return $this->repo->update($subClassroom, $data);
    }

    public function delete(SubClassroom $subClassroom)
    {
        return $this->repo->delete($subClassroom);
    }

    public function assignStudents(SubClassroom $subClassroom, array $studentUserIds)
    {
        return $this->repo->assignStudents($subClassroom, $studentUserIds);
    }

    public function getStudents(SubClassroom $subClassroom, Request $request)
    {
        return $this->repo->getStudents($subClassroom, $request);
    }

    public function getSubjects(SubClassroom $subClassroom, Request $request)
    {
        return $this->repo->getSubjects($subClassroom, $request);
    }

    public function assignSubjectTeacher(SubClassroom $subClassroom, int $subjectId, int $teacherUserId)
    {
        return $this->repo->assignSubjectTeacher($subClassroom, $subjectId, $teacherUserId);
    }

    public function getAssignments(SubClassroom $subClassroom, Request $request)
    {
        return $this->repo->getAssignments($subClassroom, $request);
    }

    public function getAssignedSubClassrooms(int $teacherUserId, Request $request)
    {
        return $this->repo->getAssignedSubClassrooms($teacherUserId, $request);
    }

    public function assignTeacher(SubClassroom $subClassroom, int $teacherUserId)
    {
        return $this->repo->assignTeacher($subClassroom, $teacherUserId);
    }

    public function assignSubject(int $subClassroomId, array $subjectIds)
    {
        return $this->repo->assignSubject($subClassroomId, $subjectIds);
    }

    public function syncStudents(?int $schoolId)
    {
        return $this->repo->syncStudents($schoolId);
    }
}
