<?php

namespace App\Services;

use App\Exports\ClassAttendanceTemplateExport;
use App\Models\ClassAttendance;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\ClassAttendance\StoreClassAttendanceRequest;
use App\Http\Requests\ClassAttendance\UpdateClassAttendanceRequest;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Http\UploadedFile;
use Maatwebsite\Excel\Facades\Excel;

class ClassAttendanceService
{

    public function paginate(Request $request)
    {
        return ClassAttendance::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreClassAttendanceRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            return ClassAttendance::create($validated);
        });
    }

    public function show(ClassAttendance $model)
    {
        return $model;
    }

    public function update(ClassAttendance $model, UpdateClassAttendanceRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(ClassAttendance $model)
    {
        DB::transaction(fn() => $model->delete());
    }

    public function listRoster(int $termId, ?int $subClassroomId = null): array
    {
        // Ambil sub_classroom_id user yang sedang login
        $user = auth()->user();
        $userSubClassroomId = $user?->sub_classroom_id;

        // Jika parameter tidak diisi, pakai sub_classroom_id user login
        if (is_null($subClassroomId)) {
            $subClassroomId = $userSubClassroomId;
        }

        // Jika subClassroomId yang diminta tidak sama dengan milik user login, kembalikan array kosong
        if ($subClassroomId != $userSubClassroomId) {
            return [];
        }

        // Ambil scope sekolah (opsional)
        $schoolId = null;
        if (function_exists('app') && app()->bound('active_school_id')) {
            $schoolId = app('active_school_id');
        } elseif (request()->attributes->has('active_school_id')) {
            $schoolId = request()->attributes->get('active_school_id');
        }

        // Ambil roster siswa untuk subClassroom
        $studentsQ = User::query()
            ->select('users.id', 'users.name')
            ->join('user_school_roles as usr', 'usr.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'usr.role_id')
            ->where('roles.name', 'student')
            ->whereNull('users.deleted_at')
            ->where('users.sub_classroom_id', $subClassroomId);

        if (!empty($schoolId)) {
            $studentsQ->where('usr.school_id', $schoolId);
        }

        $students = $studentsQ->distinct()->orderBy('users.name')->get();

        // Ambil absensi yang ada untuk term tersebut
        $attend = DB::table('class_attendances as ca')
            ->where('ca.term_id', $termId)
            ->get()
            ->keyBy('student_user_id');

        // Gabungkan roster dan absensi
        return $students->map(function ($stu) use ($attend, $termId) {
            $r = $attend->get($stu->id);
            return [
                'id'               => $r->id ?? null,
                'term_id'          => $termId,
                'student_user_id'  => $stu->id,
                'student_name'     => $stu->name,
                'sick'             => (int)($r->sick ?? 0),
                'leave'            => (int)($r->leave ?? 0),
                'present'          => (int)($r->present ?? 0),
                'alpha'            => (int)($r->alpha ?? 0),
            ];
        })->values()->toArray();
    }


    public function upsertOne(int $termId, int $studentId, int $sick, int $leave, int $present, int $alpha, ?int $actorId = null): ClassAttendance
    {
        $row = ClassAttendance::updateOrCreate(
            ['term_id' => $termId, 'student_user_id' => $studentId],
            ['sick' => max(0, $sick), 'leave' => max(0, $leave), 'present' => max(0, $present), 'alpha' => max(0, $alpha)]
        );

        if ($actorId) {
            $row->updated_by = $actorId;
            $row->save();
        }
        return $row;
    }

    public function adjust(int $id, string $field, int $delta, ?int $actorId = null): ClassAttendance
    {
        if (!in_array($field, ['sick', 'leave', 'present', 'alpha'], true)) abort(422, 'Invalid field');

        $row = ClassAttendance::findOrFail($id);
        $row->{$field} = max(0, (int)$row->{$field} + (int)$delta);
        if ($actorId) $row->updated_by = $actorId;
        $row->save();

        return $row;
    }

    /**
     * $items: [{ term_id, student_user_id, sick?, leave?, present? }, ...]
     * Menambahkan ke data yang sudah ada (append). Nol/kosong tetap dihitung 0.
     */
    public function bulkAppend(array $items, ?int $actorId = null): void
    {
        foreach ($items as $r) {
            $termId = (int)$r['term_id'];
            $studentId = (int)$r['student_user_id'];
            $sick = max(0, (int)($r['sick'] ?? 0));
            $leave = max(0, (int)($r['leave'] ?? 0));
            $present = max(0, (int)($r['present'] ?? 0));
            $alpha = max(0, (int)($r['alpha'] ?? 0));

            $existing = ClassAttendance::where('term_id', $termId)
                ->where('student_user_id', $studentId)
                ->first();

            // Cek apakah data berubah dibanding existing
            if (
                $existing &&
                $existing->sick === $sick &&
                $existing->leave === $leave &&
                $existing->present === $present &&
                $existing->alpha === $alpha
            ) {
                continue; // Tidak ada perubahan, skip
            }

            // Tetap lakukan upsert (overwrite, bukan tambah)
            $this->upsertOne(
                $termId,
                $studentId,
                $sick,
                $leave,
                $present,
                $alpha,
                $actorId
            );
        }
    }


    /**
     * Generate template XLSX (term_id, student_user_id, student_name, sick, leave, present)
     */
    public function makeTemplateExcel(int $termId, ?int $subClassroomId = null)
    {
        $filename = "attendance_template_term_{$termId}.xlsx";
        return Excel::download(new ClassAttendanceTemplateExport($termId, $subClassroomId), $filename);
    }


    /**
     * Import XLSX dan append nilai ke data existing; baris kosong/0 dilewati.
     */
    public function importFromFile(UploadedFile $file, ?int $actorId = null): void
    {
        $spreadsheet = IOFactory::load($file->getRealPath());
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();

        // header di baris 1
        for ($row = 2; $row <= $highestRow; $row++) {
            $termId = (int)($sheet->getCell("A{$row}")->getCalculatedValue());
            $studentId = (int)($sheet->getCell("B{$row}")->getCalculatedValue());
            if (!$termId || !$studentId) continue;

            $sick = (int)($sheet->getCell("D{$row}")->getCalculatedValue());
            $leave = (int)($sheet->getCell("E{$row}")->getCalculatedValue());
            $present = (int)($sheet->getCell("F{$row}")->getCalculatedValue());
            $alpha = (int)($sheet->getCell("F{$row}")->getCalculatedValue());


            // Jika 0 semua -> skip
            if (($sick + $leave + $present + $alpha) === 0) continue;

            $existing = ClassAttendance::where('term_id', $termId)
                ->where('student_user_id', $studentId)
                ->first();

            $this->upsertOne(
                $termId,
                $studentId,
                ($existing->sick ?? 0) + max(0, $sick),
                ($existing->leave ?? 0) + max(0, $leave),
                ($existing->present ?? 0) + max(0, $present),
                ($existing->alpha ?? 0) + max(0, $alpha),

                $actorId
            );
        }
    }
}
