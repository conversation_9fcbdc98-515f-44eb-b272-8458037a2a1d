<?php

namespace App\Services;

use App\Models\Room;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\Room\StoreRoomRequest;
use App\Http\Requests\Room\UpdateRoomRequest;
use App\Models\Role;
use App\Models\School;
use App\Models\User;

class RoomService
{
    public function paginate(Request $request)
    {
        return Room::with('school', 'guardian', 'dormitory')->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreRoomRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            if (isset($validated['guardian_user_id'])) {
                $this->assignGuardian($validated['guardian_user_id']);
            }
            $created = Room::create($validated);
            return $created;
        });
    }

    public function show(Room $model)
    {
        return $model->load('school', 'guardian', 'dormitory');
    }

    public function update(Room $model, UpdateRoomRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            if (isset($validated['guardian_user_id'])) {
                if ($model->guardian_user_id) {
                    $this->removeGuardian($model->guardian_user_id);
                }
                $this->assignGuardian($validated['guardian_user_id']);
            }
            $model->update($validated);
            return $model;
        });
    }

    public function delete(Room $model)
    {
        DB::transaction(function () use ($model) {
            if ($model->guardian_user_id) {
                $this->removeGuardian($model->guardian_user_id);
            }
            $model->delete();
        });
    }

    private function assignGuardian(int $id)
    {
        $activeSchool = School::find(session('active_school_id'));
        $user = User::find($id);
        $user->assignSchoolRole($activeSchool, Role::ROOM_GUARDIAN);
    }

    private function removeGuardian(int $id)
    {
        $user = User::find($id);
        $user->removeRole(Role::ROOM_GUARDIAN);
    }
}
