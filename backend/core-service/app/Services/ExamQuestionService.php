<?php

namespace App\Services;

use App\Http\Requests\ExamQuestion\StoreMultipleExamQuestionsRequest;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Repositories\ExamQuestion\ExamQuestionRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use App\Http\Requests\ExamQuestion\StoreExamQuestionRequest;
use App\Http\Requests\ExamQuestion\UpdateExamQuestionRequest;
use App\Imports\Exam\ExamQuestionRowMapper;
use App\Imports\Readers\HeadingArrayReader;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;

class ExamQuestionService
{
    protected ExamQuestionRepository $repo;
    protected ExamService $examService;
    protected HeadingArrayReader $reader;
    protected ExamQuestionRowMapper $mapper;

    public function __construct(
        ExamQuestionRepository $repo,
        ExamService $examService,
        HeadingArrayReader $reader,
        ExamQuestionRowMapper $mapper
    ) {
        $this->repo = $repo;
        $this->examService = $examService;
        $this->reader = $reader;
        $this->mapper = $mapper;
    }

    public function paginate(Request $request, Exam $exam)
    {
        if (!$exam || !$exam->exists) {
            throw new ModelNotFoundException('Exam not found.');
        }
        $request->merge(['exam_id' => $exam->id]);
        return $this->repo->paginate($request);
    }

    public function store(StoreExamQuestionRequest $request, Exam $exam)
    {
        $this->examService->validateExamAndTeacherAuthorization($exam);
        $this->examService->validateExamNotPublished($exam);
        $request->merge(['exam_id' => $exam->id]);
        return $this->repo->store($request);
    }

    public function storeMultiple(StoreMultipleExamQuestionsRequest $request, Exam $exam)
    {
        $this->examService->validateExamAndTeacherAuthorization($exam);
        $this->examService->validateExamNotPublished($exam);
        $request->merge(['exam_id' => $exam->id]);
        return $this->repo->storeMultiple($request);
    }

    public function show(ExamQuestion $examQuestion, Exam $exam)
    {
        $this->examService->validateExamAndTeacherAuthorization($exam);
        $this->validateExamQuestion($examQuestion, $exam);

        return $this->repo->show($examQuestion, $exam);
    }

    public function update(Exam $exam, ExamQuestion $examQuestion, UpdateExamQuestionRequest $request)
    {
        $this->examService->validateExamAndTeacherAuthorization($exam);
        $this->examService->validateExamNotPublished($exam);
        $this->validateExamQuestion($examQuestion, $exam);
        return $this->repo->update($examQuestion, $request);
    }

    public function delete(Exam $exam, ExamQuestion $examQuestion)
    {
        $this->examService->validateExamAndTeacherAuthorization($exam);
        $this->examService->validateExamNotPublished($exam);
        $this->validateExamQuestion($examQuestion, $exam);
        return $this->repo->delete($examQuestion);
    }

    public function validateExamQuestion(ExamQuestion $examQuestion, Exam $exam)
    {

        $exists = $exam->questions()->where('id', $examQuestion->id)->exists();

        if (!$exists) {
            return [
                'success' => false,
                'message' => "Question ID {$examQuestion->id} does not belong to exam ID {$exam->id}."
            ];
        }

        return true;
    }


    public function import(Exam $exam, UploadedFile $file, string $mode = 'append'): array
    {
        $rows    = $this->reader->read($file);
        $errors  = [];
        $created = 0;

        DB::transaction(function () use ($exam, $mode) {
            if ($mode === 'replace') {
                $exam->questions()->delete();
            }
        });

        foreach ($rows as $i => $row) {
            $rowIndex = $i + 2;
            try {
                $data = $this->mapper->map($row, $rowIndex);

                DB::transaction(function () use ($exam, $data, &$created) {
                    $orderIndex = ($exam->questions()->max('order_index') ?? 0) + 1;

                    $q = $exam->questions()->create([
                        'question_type'        => $data['question_type'],
                        'content'              => $data['content'],
                        'answer_key_essay'     => $data['answer_key_essay'],
                        'points'               => $data['points'],
                        'order_index'          => $orderIndex,
                        'kd_number'            => $data['kd_number'],
                        'learning_outcome'     => $data['learning_outcome'],
                        'competency_indicator' => $data['competency_indicator'],
                        'level_kognitif'       => $data['level_kognitif'],
                        'media_questions'      => null,
                    ]);

                    if ($data['question_type'] === 'multiple_choice' && !empty($data['options'])) {
                        $q->answerOptions()->delete();
                        foreach ($data['options'] as $opt) {
                            $q->answerOptions()->create([
                                'content'             => $opt['content'],
                                'is_correct'          => $opt['is_correct'],
                                'order_index'         => $opt['order_index'],
                                'media_answer_options' => null,
                            ]);
                        }
                    }

                    $created++;
                });
            } catch (\Throwable $e) {
                $errors[] = [
                    'row_index' => $rowIndex,
                    'error'     => $e->getMessage(),
                    'raw'       => $row,
                ];
            }
        }

        return [
            [
                'total_rows' => count($rows),
                'created'    => $created,
                'skipped'    => count($errors),
            ],
            $errors
        ];
    }

    public function listSourceExams(?int $termId, ?int $subClassroomSubjectId, ?string $q): array
    {
        return $this->repo->listSourceExams($termId, $subClassroomSubjectId, $q);
    }

    public function listQuestionsOfSource(Exam $sourceExam)
    {
        $this->examService->validateExamAndTeacherAuthorization($sourceExam);
        return $this->repo->listQuestionsOfSource($sourceExam);
    }

    /**
     * @param array<int> $questionIds
     */
    public function copyFromExam(Exam $targetExam, int $sourceExamId, array $questionIds, string $mode = 'append', bool $deepCopyMedia = false): array
    {
        // otorisasi exam target + belum publish
        $this->examService->validateExamAndTeacherAuthorization($targetExam);
        $this->examService->validateExamNotPublished($targetExam);

        return $this->repo->copyFromExam($targetExam, $sourceExamId, $questionIds, $mode, $deepCopyMedia);
    }
}
