<?php

namespace App\Services;

use App\Enums\PaymentStatus;
use App\Http\Requests\Midtrans\HandleMidtransNotificationRequest;
use App\Http\Requests\Midtrans\RequestSnapTokenPayload;
use App\Models\NfcCardTransaction;
use App\Models\SystemConfig;
use App\Models\TuitionPayment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MidtransService
{
    protected NfcCardService $nfcCardService;

    public function __construct(NfcCardService $nfcCardService)
    {
        $this->nfcCardService = $nfcCardService;
    }

    public function requestSnapToken(array $params)
    {
        $baseUrl = 'https://app.sandbox.midtrans.com/snap/v1/transactions';
        $username = SystemConfig::firstWhere('key', 'MIDTRANS_SERVER_KEY')->value;

        $response = Http::withBasicAuth($username, '')
            ->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'X-Override-Notification' => request()->hasHeader('X-Override-Notification') ? request()->header('X-Override-Notification') : '',
            ])
            ->post($baseUrl, [
                'transaction_details' => [
                    'order_id' => $params['order_id'],
                    'gross_amount' => $params['gross_amount'],
                ],
                'credit_card' => [
                    'secure' => true,
                ],
            ]);

        return $response->json();
    }

    public function handleNotification(HandleMidtransNotificationRequest $request)
    {
        $validated = $request->validated();

        $status = PaymentStatus::PENDING;
        switch ($validated['transaction_status']) {
            case 'capture':
                $status = PaymentStatus::COMPLETED;
                break;
            case 'settlement':
                $status = PaymentStatus::COMPLETED;
                break;
            case 'pending':
                $status = PaymentStatus::PENDING;
                break;
            case 'deny':
                $status = PaymentStatus::FAILED;
                break;
            case 'expire':
                $status = PaymentStatus::FAILED;
                break;
            case 'cancel':
                $status = PaymentStatus::FAILED;
                break;
        }

        if (Str::startsWith($validated['order_id'], 'PAY-')) {
            $this->handleTuitionPaymentNotification(
                $validated['order_id'],
                $status,
                $validated['payment_type']
            );
        }

        if (Str::startsWith($validated['order_id'], 'NFC-')) {
            $this->handleTopUpNfcNotification(
                $validated['order_id'],
                $status,
                $validated['payment_type']
            );
        }
    }

    private function handleTuitionPaymentNotification(string $orderId, PaymentStatus $status, string $paymentType): void
    {
        $payment = TuitionPayment::where('payment_number', $orderId)->first();
        if (!$payment) {
            throw new \Exception("Payment with order ID {$orderId} not found.");
        }
        // Update payment method
        $payment->update(['payment_method' => $paymentType, 'status' => $status]);
    }

    private function handleTopUpNfcNotification(string $orderId, PaymentStatus $status): void
    {
        $transaction = NfcCardTransaction::where('transaction_number', $orderId)->first();
        switch ($status) {
            case PaymentStatus::COMPLETED:
                $this->nfcCardService->approveTransaction($transaction);
                break;
            case PaymentStatus::FAILED:
                $this->nfcCardService->rejectTransaction($transaction);
                break;
        }
    }
}
