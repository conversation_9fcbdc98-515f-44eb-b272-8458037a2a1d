<?php

namespace App\Services;

use App\Http\Requests\Foundation\AddFoundationSchoolRequest;
use App\Http\Requests\Foundation\AssignFoundationAdminRequest;
use App\Models\Foundation;
use Illuminate\Http\Request;
use App\Http\Requests\Foundation\StoreFoundationRequest;
use App\Http\Requests\Foundation\UpdateFoundationRequest;
use App\Models\FoundationAdmin;
use App\Models\Role;
use App\Models\School;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class FoundationService
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return Foundation::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreFoundationRequest $request): Foundation
    {
        $validated = $request->validated();
        return DB::transaction(fn() => Foundation::create($validated));
    }

    public function show(Foundation $model): Foundation
    {
        return $model;
    }

    public function update(Foundation $model, UpdateFoundationRequest $request): Foundation
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(Foundation $model): void
    {
        DB::transaction(fn() => $model->delete());
    }

    public function getAdmins(Foundation $model)
    {
        return $model->admins()->paginate(request()->limit ?? config('app.pagination.max'));
    }

    public function getSchools(Foundation $model)
    {
        return $model->schools()->paginate(request()->limit ?? config('app.pagination.max'));
    }

    public function assignAdmin(AssignFoundationAdminRequest $request, Foundation $model)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $user = User::find($validated['user_id']);

            // Check if user already has the role
            $exists = FoundationAdmin::where('admin_user_id', $user->id)
                ->where('foundation_id', $model->id)
                ->exists();
            if ($exists) {
                throw new \Exception('User already has the role of foundation admin.');
            }

            FoundationAdmin::create([
                'admin_user_id' => $user->id,
                'foundation_id' => $model->id,
            ]);

            $user->assignRole(Role::FOUNDATION_ADMIN);

            return $user;
        });
    }

    public function addSchool(AddFoundationSchoolRequest $request, Foundation $model)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $school = School::find($validated['school_id']);
            $school->foundation_id = $model->id;
            $school->save();
            return $school;
        });
    }
}
