<?php

namespace App\Services;

use App\Models\Subject;
use App\Repositories\Subject\SubjectRepository;
use Illuminate\Http\Request;
use App\Models\School;           
use Illuminate\Support\Facades\DB;

class SubjectService
{
    protected SubjectRepository $repo;

    public function __construct(SubjectRepository $repo)
    {
        $this->repo = $repo;
    }

   public function paginate(Request $request)
    {
        $activeSchoolId = session('active_school_id');
        if (!$activeSchoolId) {
            abort(422, 'Active school is not set.');
        }

        $q = School::findOrFail($activeSchoolId)->subjects();

        // ----- SEARCH -----
        if ($request->filled('search')) {
            $raw    = trim((string) $request->input('search', ''));
            $terms  = preg_split('/\s+/', $raw, -1, PREG_SPLIT_NO_EMPTY) ?: [];
            $driver = DB::getDriverName();

            if (!empty($terms)) {
                $q->where(function ($outer) use ($terms, $driver) {
                    foreach ($terms as $term) {
                        $pattern = "%{$term}%";

                        $outer->where(function ($inner) use ($pattern, $driver) {
                            if ($driver === 'pgsql') {
                                $inner->where('name', 'ILIKE', $pattern)
                                      ->orWhere('description', 'ILIKE', $pattern);
                            } else {
                                $l = mb_strtolower($pattern);
                                $inner->whereRaw('LOWER(name) LIKE ?', [$l])
                                      ->orWhereRaw('LOWER(description) LIKE ?', [$l]);
                            }
                        });
                    }
                });
            }
        }

        // Sorting default
        $q->orderBy('priority', 'asc')->orderBy('name', 'asc');

        $limit = (int) ($request->input('limit') ?? config('app.pagination.max', 15));
        return $q->paginate($limit);
    }


    public function store(array $data)
    {
        // Make sure school_id is explicitly set from session
        if (!isset($data['school_id']) && session()->has('active_school_id')) {
            $data['school_id'] = session('active_school_id');
        }
        
        return $this->repo->store($data);
    }

    public function show(Subject $subject)
    {
        return $this->repo->show($subject);
    }

    public function update(Subject $subject, array $data)
    {
        return $this->repo->update($subject, $data);
    }

    public function delete(Subject $subject)
    {
        return $this->repo->delete($subject);
    }

    public function getAssignedSubjects(int $teacherUserId, Request $request)
    {
        return $this->repo->getAssignedSubjects($teacherUserId, $request);
    }

    public function import(Request $request, ?int $schoolId = null)
    {
        // Use the provided schoolId parameter, or try to get it from the session/header
        if (!$schoolId) {
            $schoolId = session('active_school_id') ?: $request->header('X-School-ID');
        }
        
        // Store it in the session for any code that might need it
        if ($schoolId) {
            session(['active_school_id' => $schoolId]);
        }
        
        return $this->repo->import($request, $schoolId);
    }

    public function export()
    {
        return $this->repo->export();
    }
}
