<?php

namespace App\Services;

use App\Models\SchoolLevel;
use Illuminate\Http\Request;
use App\Http\Requests\SchoolLevel\StoreSchoolLevelRequest;
use App\Http\Requests\SchoolLevel\UpdateSchoolLevelRequest;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SchoolLevelService
{
    public function paginate(Request $request): LengthAwarePaginator
    {
        return SchoolLevel::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreSchoolLevelRequest $request): SchoolLevel
    {
        $validated = $request->validated();
        return DB::transaction(
            function () use ($validated) {
                $validated['slug'] = Str::slug($validated['name']);
                return SchoolLevel::create($validated);
            }
        );
    }

    public function show(SchoolLevel $model): SchoolLevel
    {
        return $model;
    }

    public function update(SchoolLevel $model, UpdateSchoolLevelRequest $request): SchoolLevel
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(SchoolLevel $model): void
    {
        DB::transaction(fn() => $model->delete());
    }
}
