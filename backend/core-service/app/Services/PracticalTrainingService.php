<?php

namespace App\Services;

use App\Models\PracticalTraining;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\PracticalTraining\StorePracticalTrainingRequest;
use App\Http\Requests\PracticalTraining\UpdatePracticalTrainingRequest;

class PracticalTrainingService
{
    public function paginate(Request $request)
    {
        $limit = (int) ($request->limit ?? config('app.pagination.max'));

        $query = PracticalTraining::query()
            ->with([
                'student.subClassroom',
                'term.academicYear',
            ])
            ->when(
                $request->filled('term_id'),
                fn($q) =>
                $q->where('term_id', $request->integer('term_id'))
            )
            ->when(
                $request->filled('student_user_id'),
                fn($q) =>
                $q->where('student_user_id', $request->integer('student_user_id'))
            )
            ->when($request->filled('search'), function ($q) use ($request) {
                $s = $request->get('search');
                $q->where(function ($w) use ($s) {
                    $w->where('place', 'like', "%{$s}%")
                        ->orWhereHas('student', fn($qs) => $qs->where('name', 'like', "%{$s}%"));
                });
            })
            ->orderByDesc('created_at');

        $result = $query->paginate($limit);

        $result->appends($request->all());

        return $result;
    }

    public function store(StorePracticalTrainingRequest $request)
    {
        $validated = $request->validated();

        return DB::transaction(function () use ($validated) {
            $model = PracticalTraining::create($validated);
            return $model->load(['student.subClassroom', 'term.academicYear']);
        });
    }

    public function show(PracticalTraining $model)
    {
        return $model->load(['student.subClassroom', 'term.academicYear']);
    }

    public function update(PracticalTraining $model, UpdatePracticalTrainingRequest $request)
    {
        $validated = $request->validated();

        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model->load(['student.subClassroom', 'term.academicYear']);
        });
    }

    public function delete(PracticalTraining $model)
    {
        DB::transaction(fn() => $model->delete());
    }
}
