<?php

namespace App\Services;

use App\Http\Requests\Attendance\ExportsAttendanceRequest;
use App\Http\Requests\Attendance\StoreAttendanceRequest;
use App\Models\Attendance;
use Illuminate\Http\Request;
use App\Http\Resources\AttendanceResource;
use App\Http\Resources\AttendanceSummaryResource;
use App\Models\Organization;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Http\Response;


class AttendanceService
{
    public function index(Request $request): LengthAwarePaginator
    {
        $attendances = Attendance::with(['user', 'schedule'])
            ->when($request->filled('user_id'), function ($query) use ($request) {
                $query->where('user_id', $request->user_id);
            })
            ->when($request->filled('schedule_id'), function ($query) use ($request) {
                $query->where('schedule_id', $request->schedule_id);
            })
            ->when($request->filled('type'), function ($query) use ($request) {
                $query->whereHas('user', function ($q) use ($request) {
                    $q->getByRole($request->type);
                });
            })
            ->whereHas(
                'user',
                fn($q) =>
                $q->when($request->id, fn($q) => $q->where('users.id', $request->id))
            )
            ->when($request->month, function ($query) use ($request) {
                $query->whereMonth('attendances.checked_in_at', '=', date('m', strtotime($request->month)))
                    ->whereYear('attendances.checked_in_at', '=', date('Y', strtotime($request->month)));
            })
            ->latest()
            ->paginate($request->limit ?? config('app.pagination.max'));

        return $attendances;
    }

    public function store(StoreAttendanceRequest $request, $user)
    {
        return DB::transaction(function () use ($request, $user) {
            $validated = $request->validated();
            $validated['checked_in_at'] = now();
            $validated['user_id'] = $user->id;
            $validated['is_present'] = true;

            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('attendance-photos', 'public');
            }

            $attendance = Attendance::create($validated);
            return new AttendanceResource($attendance);
        });
    }

    public function show(int $id)
    {
        $attendance = Attendance::with(['user', 'schedule'])->findOrFail($id);
        return new AttendanceResource($attendance);
    }

    public function update(Request $request, int $id)
    {
        return DB::transaction(function () use ($request, $id) {
            $attendance = Attendance::findOrFail($id);
            $validated = $request->validated();
            $validated['checked_in_at'] = now();
            $oldPhoto = $attendance->photo;

            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('attendance-photos', 'public');
            }

            $attendance->update($validated);

            if (isset($validated['photo']) && $oldPhoto) {
                Storage::disk('public')->delete($oldPhoto);
            }

            return new AttendanceResource($attendance);
        });
    }

    public function destroy(int $id)
    {
        return DB::transaction(function () use ($id) {
            $attendance = Attendance::findOrFail($id);
            $photo = $attendance->photo;

            $attendance->delete();

            if ($photo) {
                Storage::disk('public')->delete($photo);
            }

            return true;
        });
    }

    public function batchStore(array $data)
    {
        return DB::transaction(function () use ($data) {
            foreach ($data['students'] as $student) {
                Attendance::updateOrCreate(
                    [
                        'user_id' => $student['user_id'],
                        'schedule_id' => $data['schedule_id'],
                    ],
                    [
                        'checked_in_at' => now(),
                        'is_present' => $student['is_present'],
                        'notes' => $student['notes'] ?? null,
                    ]
                );
            }

            return true;
        });
    }

    public function getAttendanceSummaryByRole(string $role)
    {
        $attendances = Attendance::with('user')
            ->whereHas('user', fn($q) => $q->getByRole($role))
            ->selectRaw("user_id,
                SUM(CASE WHEN is_present = true THEN 1 ELSE 0 END) as total_present,
                SUM(CASE WHEN is_present = false THEN 1 ELSE 0 END) as total_absent")
            ->groupBy('user_id')
            ->paginate(10);

        return AttendanceSummaryResource::collection($attendances);
    }

    public function exportPdf(ExportsAttendanceRequest $request)
    {
        $id = $request->input('id');
        $bulanInput = $request->input('month');
        $type = $request->input('type');

        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 120);

        $query = Attendance::query()
            ->select('attendances.*', 'users.name')
            ->join('users', 'users.id', '=', 'attendances.user_id')
            ->with(['user', 'schedule.subClassroomSubject.subject']);

        if ($type) {
            $query->whereHas('user', function ($q) use ($type) {
                $q->getByRole($type);
            });
        }

        if ($id) {
            $query->where('users.id', $id);
        }

        if ($bulanInput) {
            $query->whereMonth('attendances.checked_in_at', '=', date('m', strtotime($bulanInput)))
                ->whereYear('attendances.checked_in_at', '=', date('Y', strtotime($bulanInput)));
        }

        $attendances = $query->orderBy('attendances.checked_in_at')->get();

        if ($attendances->isEmpty()) {
            return response()->json(['message' => 'Tidak ada data absensi ditemukan'], Response::HTTP_NOT_FOUND);
        }

        $sekolah = 'Nama Sekolah';
        $namaUser = $attendances->first()->user->name ?? '-';

        $organization = Organization::where('teacher_user_id', $attendances->first()->user_id)
            ->with('position')
            ->first();
        $jabatan = $organization?->position?->name ?? 'Guru';

        $tahunPelajaran = '2024 - 2025';

        $kepalaOrg = Organization::with(['position', 'teacher'])
            ->whereNotNull('teacher_user_id')
            ->join('positions', 'positions.id', '=', 'organizations.position_id')
            ->orderBy('positions.priority', 'ASC')
            ->first();

        $kepalaSekolah = $kepalaOrg?->teacher?->name ?? 'Kepala Sekolah';

        $tahunPelajaran = $attendances->first()->user->academicYear?->name ?? '-';

        $bulan = $bulanInput
            ? Carbon::parse($bulanInput)->translatedFormat('F Y')
            : Carbon::now()->translatedFormat('F Y');

        $tableRows = '';
        $counter = 1;

        foreach ($attendances as $a) {
            $tanggal = Carbon::parse($a->checked_in_at)->translatedFormat('d-m-Y');
            $subject = $a->schedule->subClassroomSubject->subject->name ?? '-';
            $jam = Carbon::parse($a->checked_in_at)->format('H:i');

            $relativePath = $a->photo ? 'storage/' . ltrim($a->photo, '/') : null;
            $fullPath = $relativePath ? public_path($relativePath) : null;

            if ($fullPath && file_exists($fullPath)) {
                $imgData = base64_encode(file_get_contents($fullPath));
                $foto = '<img src="data:image/png;base64,' . $imgData . '" width="80">';
            } else {
                $foto = '-';
            }

            $tableRows .= "<tr>
            <td>{$counter}</td>
            <td>{$tanggal}</td>
            <td class=\"mapel\">{$subject}</td>
            <td>{$jam}</td>
            <td>{$foto}</td>
        </tr>";

            $counter++;
        }

        $template = file_get_contents(resource_path('views/pdf/attendance_report.html'));

        $html = str_replace(
            ['{{NAMA_SEKOLAH}}', '{{KEPALA_SEKOLAH}}', '{{TAHUN_PELAJARAN}}', '{{NAMA_GURU}}', '{{JABATAN}}', '{{BULAN}}', '{{TABEL_DATA}}'],
            [$sekolah, $kepalaSekolah, $tahunPelajaran, $namaUser, $jabatan, $bulan, $tableRows],
            $template
        );

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);

        $dompdf = new Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return response($dompdf->output(), 200)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="absensi-guru.pdf"');
    }

    public function getTeacherAttendances(Request $request)
    {
        $teacher = $request->user();

        return Attendance::whereHas('schedule.subClassroomSubject', function ($query) use ($teacher) {
            $query->where(function ($q) use ($teacher) {
                $q->where('teacher_user_id', $teacher->id);

                if ($teacher->sub_classroom_id) {
                    $q->orWhere('sub_classroom_id', $teacher->sub_classroom_id);
                }
            });
        })
            ->with([
                'user.subClassroom',
                'schedule.subClassroomSubject.subClassroom',
                'schedule.subClassroomSubject.subject',
            ])
            ->orderBy('checked_in_at', 'desc')
            ->paginate($request->limit ?? 10);
    }

    public function getScheduleAttendanceSummary(Request $request)
    {
        $attendances =
            DB::table('schedules')
            ->join('sub_classroom_subjects', 'schedules.sub_classroom_subject_id', '=', 'sub_classroom_subjects.id')
            ->join('subjects', 'sub_classroom_subjects.subject_id', '=', 'subjects.id')
            ->join('attendances', 'attendances.schedule_id', '=', 'schedules.id')
            ->where('attendances.user_id', $request->input('user_id'))
            ->select([
                'subjects.name as subject',
                DB::raw("SUM(CASE WHEN attendances.is_present = true THEN 1 ELSE 0 END) AS total_present"),
                DB::raw("SUM(CASE WHEN attendances.notes = 'sick' THEN 1 ELSE 0 END) AS total_sick"),
                DB::raw("SUM(CASE WHEN attendances.notes = 'excused_absent' THEN 1 ELSE 0 END) AS total_excused_absent"),
                DB::raw("SUM(CASE WHEN attendances.notes = 'no_status' THEN 1 ELSE 0 END) AS total_no_status"),
            ])
            ->groupBy('subjects.name')
            ->get();
        return $attendances;
    }
}
