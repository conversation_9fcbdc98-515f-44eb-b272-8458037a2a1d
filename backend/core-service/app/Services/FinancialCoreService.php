<?php

namespace App\Services;

use App\Enums\FinancialCoreReferenceType;
use App\Enums\FinancialCoreStatus;
use App\Models\FinancialCore;
use App\Repositories\FinancialCore\FinancialCoreRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\FinancialCore\StoreFinancialCoreRequest;
use App\Http\Requests\FinancialCore\UpdateFinancialCoreRequest;
use Illuminate\Support\Str;

class FinancialCoreService
{
    protected FinancialCoreRepository $repository;

    public function __construct(FinancialCoreRepository $repository)
    {
        $this->repository = $repository;
    }

    public function paginate(Request $request)
    {
        return $this->repository->paginate($request);
    }

    public function store(StoreFinancialCoreRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            // Ensure debit_amount and credit_amount are never null
            $validated['debit_amount'] = $validated['debit_amount'] ?? 0;
            $validated['credit_amount'] = $validated['credit_amount'] ?? 0;
            
            // Generate transaction number
            $validated['transaction_number'] = $this->generateTransactionNumber($validated['reference_type']);
            
            // Add detailed timestamp information
            $now = now();
            $validated['transaction_date'] = $validated['transaction_date'] ?? $now->format('Y-m-d');
            
            // Use provided transaction_time if available, otherwise use current time with microseconds
            if (!isset($validated['transaction_time']) || empty($validated['transaction_time'])) {
                $validated['transaction_time'] = $now->format('H:i:s.u');
            } else {
                // Clean and normalize the time input
                $timeInput = $validated['transaction_time'];
                
                // Remove microseconds if present (e.g., "14:30:15.123456" -> "14:30:15")
                $timeInput = explode('.', $timeInput)[0];
                
                // Ensure the time format includes seconds if not provided
                if (substr_count($timeInput, ':') === 1) {
                    // If only HH:MM provided, add :00 for seconds
                    $timeInput .= ':00';
                }
                
                $validated['transaction_time'] = $timeInput;
            }
            
            $validated['transaction_timezone'] = $validated['transaction_timezone'] ?? 'Asia/Jakarta';
            
            return FinancialCore::create($validated);
        });
    }

    public function show(FinancialCore $model)
    {
        return $model->load(['student', 'parent', 'school']);
    }

    public function update(FinancialCore $model, UpdateFinancialCoreRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            // Ensure debit_amount and credit_amount are never null when provided
            if (isset($validated['debit_amount'])) {
                $validated['debit_amount'] = $validated['debit_amount'] ?? 0;
            }
            if (isset($validated['credit_amount'])) {
                $validated['credit_amount'] = $validated['credit_amount'] ?? 0;
            }
            
            // Normalize transaction_time if provided
            if (isset($validated['transaction_time']) && !empty($validated['transaction_time'])) {
                $timeInput = $validated['transaction_time'];
                
                // Convert to string and handle different formats
                $timeInput = trim((string) $timeInput);
                
                // Remove microseconds if present
                $timeInput = explode('.', $timeInput)[0];
                
                // Ensure HH:MM:SS format
                if (substr_count($timeInput, ':') === 1) {
                    $timeInput .= ':00';
                }
                
                $validated['transaction_time'] = $timeInput;
            }
            
            $model->update($validated);
            return $model;
        });
    }

    public function delete(FinancialCore $model)
    {
        DB::transaction(fn () => $model->delete());
    }

    /**
     * Record a financial transaction from external system.
     */
    public function recordTransaction(
        FinancialCoreReferenceType $referenceType,
        string $referenceId,
        int $debitAmount = 0,
        int $creditAmount = 0,
        ?int $schoolId = null,
        ?int $studentUserId = null,
        ?int $parentUserId = null,
        ?string $notes = null
    ): FinancialCore {
        return DB::transaction(function () use (
            $referenceType, $referenceId, $debitAmount, $creditAmount,
            $schoolId, $studentUserId, $parentUserId, $notes
        ) {
            return FinancialCore::create([
                'school_id' => $schoolId,
                'student_user_id' => $studentUserId,
                'parent_user_id' => $parentUserId,
                'transaction_number' => $this->generateTransactionNumber($referenceType->value),
                'reference_type' => $referenceType,
                'reference_id' => $referenceId,
                'debit_amount' => $debitAmount,
                'credit_amount' => $creditAmount,
                'notes' => $notes,
                'status' => 'completed',
                'transaction_date' => now(),
            ]);
        });
    }

    /**
     * Generate unique transaction number.
     */
    protected function generateTransactionNumber(string $referenceType): string
    {
        $prefix = match ($referenceType) {
            'tuition' => 'TUI',
            'canteen' => 'CNT',
            'cashless' => 'CLS',
            'others' => 'OTH',
            default => 'TXN',
        };

        do {
            $number = $prefix . '-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while ($this->repository->findByTransactionNumber($number));

        return $number;
    }

    /**
     * Create FinancialCore record from TuitionPayment approval.
     */
    public function createFromTuitionPayment($payment): FinancialCore
    {
        $totalAmount = 0;
        foreach ($payment->tuitionInvoices as $invoice) {
            $totalAmount += $invoice->amount;
        }

        // Get school_id from payment, student, or tuition invoice
        $schoolId = $payment->school_id;
        if (!$schoolId && $payment->student_user_id) {
            $student = \App\Models\User::find($payment->student_user_id);
            $schoolId = $student?->school_id;
        }
        if (!$schoolId && $payment->tuitionInvoices->isNotEmpty()) {
            $schoolId = $payment->tuitionInvoices->first()->school_id;
        }
        if (!$schoolId && auth()->user()) {
            $schoolId = auth()->user()->school_id;
        }

        $now = now();
        $data = [
            'school_id' => $schoolId,
            'student_user_id' => $payment->student_user_id,
            'parent_user_id' => $payment->parent_user_id,
            'reference_type' => FinancialCoreReferenceType::TUITION->value,
            'reference_id' => $payment->id,
            'credit_amount' => $totalAmount, // SPP payment = credit (income)
            'debit_amount' => 0,
            'notes' => 'Pembayaran SPP - ' . $payment->payment_number,
            'status' => FinancialCoreStatus::COMPLETED->value,
            'transaction_date' => $now,
            'transaction_time' => $now->format('H:i:s.u'),
            'transaction_timezone' => 'Asia/Jakarta',
        ];

        return DB::transaction(function () use ($data) {
            $data['transaction_number'] = $this->generateTransactionNumber($data['reference_type']);
            return $this->repository->store($data);
        });
    }

    /**
     * Create FinancialCore record from Canteen Transaction.
     */
    public function createFromCanteenTransaction($transaction): FinancialCore
    {
        // Get canteen's school_id if available
        $canteen = $transaction->canteen;
        $schoolId = $canteen ? $canteen->school_id : (auth()->user()->school_id ?? null);
        
        $transactionDate = $transaction->transacted_at ?? $transaction->created_at ?? now();
        $now = now();
        
        $data = [
            'school_id' => $schoolId,
            'student_user_id' => $transaction->student_user_id ?? null, // Will be null for general canteen transactions
            'reference_type' => FinancialCoreReferenceType::CANTEEN->value,
            'reference_id' => $transaction->id,
            'credit_amount' => $transaction->total_price, // Canteen transaction = credit (income for school)
            'debit_amount' => 0,
            'notes' => 'Transaksi Kantin - ' . $transaction->code,
            'status' => FinancialCoreStatus::COMPLETED->value,
            'transaction_date' => $transactionDate,
            'transaction_time' => $now->format('H:i:s.u'),
            'transaction_timezone' => 'Asia/Jakarta',
        ];

        return DB::transaction(function () use ($data) {
            $data['transaction_number'] = $this->generateTransactionNumber($data['reference_type']);
            return $this->repository->store($data);
        });
    }

    /**
     * Create FinancialCore record from NFC Card Top Up.
     */
    public function createFromNfcTopUp($nfcTransaction): FinancialCore
    {
        // Get school_id from NFC card's owner or transaction
        $schoolId = null;
        if ($nfcTransaction->nfcCard && $nfcTransaction->nfcCard->user) {
            $schoolId = $nfcTransaction->nfcCard->user->school_id;
        }
        if (!$schoolId && auth()->user()) {
            $schoolId = auth()->user()->school_id;
        }

        $transactionDate = $nfcTransaction->created_at ?? now();
        $now = now();
        
        $data = [
            'school_id' => $schoolId,
            'student_user_id' => $nfcTransaction->nfcCard->user_id ?? null,
            'reference_type' => FinancialCoreReferenceType::CASHLESS->value,
            'reference_id' => $nfcTransaction->id,
            'credit_amount' => $nfcTransaction->amount, // Top up = credit (income)
            'debit_amount' => 0,
            'notes' => 'Top Up Transaksi NFC - ' . ($nfcTransaction->nfcCard->uid ?? 'N/A'),
            'status' => FinancialCoreStatus::COMPLETED->value,
            'transaction_date' => $transactionDate,
            'transaction_time' => $now->format('H:i:s.u'),
            'transaction_timezone' => 'Asia/Jakarta',
        ];

        return DB::transaction(function () use ($data) {
            $data['transaction_number'] = $this->generateTransactionNumber($data['reference_type']);
            return $this->repository->store($data);
        });
    }

    /**
     * Create FinancialCore record from Cashless Card Transaction (spending).
     */
    public function createFromCashlessTransaction($nfcTransaction): FinancialCore
    {
        // Get school_id from NFC card's owner or transaction
        $schoolId = null;
        if ($nfcTransaction->nfcCard && $nfcTransaction->nfcCard->user) {
            $schoolId = $nfcTransaction->nfcCard->user->school_id;
        }
        if (!$schoolId) {
            $schoolId = $nfcTransaction->school_id ?? auth()->user()->school_id;
        }

        $transactionDate = $nfcTransaction->created_at ?? now();
        $now = now();
        
        $data = [
            'school_id' => $schoolId,
            'student_user_id' => $nfcTransaction->nfcCard->user_id ?? $nfcTransaction->user_id,
            'reference_type' => FinancialCoreReferenceType::CASHLESS->value,
            'reference_id' => $nfcTransaction->id,
            'credit_amount' => $nfcTransaction->amount, // Cashless spending = credit (income for school)
            'debit_amount' => 0,
            'notes' => 'Pembayaran Transaksi NFC - ' . ($nfcTransaction->nfcCard->uid ?? 'N/A'),
            'status' => FinancialCoreStatus::COMPLETED->value,
            'transaction_date' => $transactionDate,
            'transaction_time' => $now->format('H:i:s.u'),
            'transaction_timezone' => 'Asia/Jakarta',
        ];

        return DB::transaction(function () use ($data) {
            $data['transaction_number'] = $this->generateTransactionNumber($data['reference_type']);
            return $this->repository->store($data);
        });
    }
}
