<?php

namespace App\Services;

use App\Models\Dormitory;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\Dormitory\StoreDormitoryRequest;
use App\Http\Requests\Dormitory\UpdateDormitoryRequest;

class DormitoryService
{
    public function paginate(Request $request)
    {
        return Dormitory::with('school', 'guardian')->paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreDormitoryRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            $created = Dormitory::create($validated);
            return $created;
        });
    }

    public function show(Dormitory $model)
    {
        return $model->load('school', 'guardian', 'rooms', 'rooms.guardian');
    }

    public function update(Dormitory $model, UpdateDormitoryRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(Dormitory $model)
    {
        DB::transaction(fn() => $model->delete());
    }
}
