<?php

namespace App\Services;

use App\Models\PracticalTrainingData;
use Illuminate\Support\Facades\DB;
use App\Repositories\PracticalTrainingData\PracticalTrainingDataRepository;
use Illuminate\Http\Request;
use App\Http\Requests\PracticalTrainingData\StorePracticalTrainingDataRequest;
use App\Http\Requests\PracticalTrainingData\UpdatePracticalTrainingDataRequest;

class PracticalTrainingDataService
{
    public function paginate(Request $request)
    {
        $limit = (int) ($request->limit ?? config('app.pagination.max'));

        $query = PracticalTrainingData::query()
            ->when(
                $request->filled('practical_training_id'),
                fn($q) => $q->where('practical_training_id', $request->integer('practical_training_id'))
            )
            ->orderByDesc('created_at');

        $result = $query->paginate($limit);
        $result->appends($request->all());

        return $result;
    }


    public function store(StorePracticalTrainingDataRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            $created = PracticalTrainingData::create($validated);
            return $created;
        });
    }

    public function show(PracticalTrainingData $model)
    {
        return $model;
    }

    public function update(PracticalTrainingData $model, UpdatePracticalTrainingDataRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(PracticalTrainingData $model)
    {
        DB::transaction(fn () => $model->delete());
    }
}
