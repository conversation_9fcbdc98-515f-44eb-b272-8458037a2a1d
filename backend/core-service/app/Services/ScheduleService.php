<?php

namespace App\Services;

use App\Http\Requests\Schedule\GetAttendanceStatusRequest;
use App\Models\Schedule;
use App\Repositories\Schedule\ScheduleRepository;

class ScheduleService
{
    protected ScheduleRepository $repo;

    public function __construct(ScheduleRepository $repo)
    {
        $this->repo = $repo;
    }

    public function getSchedules($user, $day, $subClassroomId = null, $schoolId = null)
    {
        if ($user->hasRole('teacher')) {
            return $this->repo->getSchedulesByTeacher($user->id, $day);
        }
        return $this->repo->getSchedulesByDayAndSubClassroom($day, $subClassroomId, $schoolId);
    }

    public function storeSchedule(array $data)
    {
        return $this->repo->createSchedule($data);
    }

    public function updateSchedule(Schedule $schedule, array $data)
    {
        return $this->repo->updateSchedule($schedule, $data);
    }

    public function destroySchedule(Schedule $schedule)
    {
        return $this->repo->deleteSchedule($schedule);
    }

    public function getCurrentSchedule($user, int $day)
    {
        return $user->hasRole('teacher')
            ? $this->repo->getTeacherCurrentSchedule($user->id, $day)
            : $this->repo->getStudentCurrentSchedule($user->subClassroom->id ?? request('sub_classroom_id'), $day);
    }

    public function getAttendances(int $scheduleId, ?string $date = null)
    {
        return $this->repo->getStudentAttendances($scheduleId, $date);
    }

    public function getAttendanceStatus(GetAttendanceStatusRequest $request, Schedule $schedule)
    {
        return $schedule->getLatestAttendance($request->user_id, $request->date);
    }
}
