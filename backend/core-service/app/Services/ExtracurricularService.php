<?php

namespace App\Services;

use App\Http\Requests\Extracurricular\CheckInExtracurricularRequest;
use App\Http\Requests\Extracurricular\StoreExtracurricularRequest;
use App\Http\Requests\Extracurricular\UpdateExtracurricularRequest;
use App\Http\Requests\ExtracurricularActivity\ApproveExtracurricularActivityRequest;
use App\Http\Requests\ExtracurricularActivity\StoreExtracurricularActivityRequest;
use App\Http\Resources\ExtracurricularActivityResource;
use App\Models\Extracurricular;
use App\Models\ExtracurricularActivity;
use App\Models\ExtracurricularAttendance;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExtracurricularService
{
    /**
     * Paginate extracurricular activities.
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function paginate(Request $request)
    {
        return Extracurricular::orderBy('updated_at', 'desc')->paginate($request->limit ?? config('app.pagination.max'));
    }

    /**
     * Store a new extracurricular activity.
     *
     * @param StoreExtracurricularRequest $request
     * @return Extracurricular
     */
    public function store(StoreExtracurricularRequest $request): Extracurricular
    {
        return DB::transaction(function () use ($request) {
            $validated = $request->validated();
            $extracurricular = Extracurricular::create($validated);
            return $extracurricular;
        });
    }

    /**
     * Update an existing extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @param UpdateExtracurricularRequest $request
     * @return Extracurricular
     */
    public function update(Extracurricular $extracurricular, UpdateExtracurricularRequest $request): Extracurricular
    {
        return DB::transaction(function () use ($extracurricular, $request) {
            $extracurricular->update($request->validated());
            return $extracurricular;
        });
    }

    /**
     * Delete an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @return void
     */
    public function delete(Extracurricular $extracurricular): void
    {
        DB::transaction(fn() => $extracurricular->delete());
    }

    /**
     * Check in a user for an extracurricular activity.
     *
     * @param CheckInExtracurricularRequest $request
     * @param $user
     * @return ExtracurricularAttendance
     */
    public function checkIn(CheckInExtracurricularRequest $request, $user)
    {
        return DB::transaction(function () use ($request, $user) {
            $validated = $request->validated();
            $validated['user_id'] = $user->id;
            $validated['checked_in_at'] = now();
            $validated['is_present'] = true;
            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('extracurricular-photos', 'public');
            }
            $result = ExtracurricularAttendance::create($validated);
            return $result;
        });
    }

    /**
     * Get check-in history for a user.
     *
     * @param Request $request
     * @param $user
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCheckInHistory(Request $request, $user)
    {
        return ExtracurricularAttendance::where('user_id', $user->id)
            ->when($request->has('extracurricular_id'), function ($query) use ($request) {
                $query->where('extracurricular_id', $request->extracurricular_id);
            })
            ->orderBy('checked_in_at', 'desc')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    /**
     * Get students assigned to an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getStudents(Extracurricular $extracurricular)
    {
        return $extracurricular->students()->orderBy('updated_at', 'desc')->paginate(request()->limit ?? config('app.pagination.max'));
    }

    /**
     * Get extracurricular activities assigned to a student.
     *
     * @param int $userId
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAssigned(Request $request)
    {
        $user = $request->user();
        $requestUserId = $request->input('user_id');
        // If user is a teacher, return all extracurriculars
        if ($user->hasRole('teacher')) {
            return Extracurricular::where('teacher_user_id', $user->id)
                ->orderBy('updated_at', 'desc')
                ->paginate($request->limit ?? config('app.pagination.max'));
        }
        return Extracurricular::whereHas('students', fn($q) => $q->where('users.id', $requestUserId ?? $user->id))
            ->orderBy('updated_at', 'desc')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    /**
     * Get attendance records for an extracurricular activity.
     *
     * @param int $id
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAttendances(int $id)
    {
        return ExtracurricularAttendance::with(['user', 'extracurricular'])
            ->where('extracurricular_id', $id)
            ->when(request('date'), function ($query) {
                $date = Carbon::createFromTimestamp(request('date'))->toDateString(); // convert epoch to Y-m-d
                $query->whereDate('checked_in_at', $date);
            })
            ->orderBy('checked_in_at', 'desc')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }

    /**
     * Assign a student to an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @param int $studentUserId
     * @return Extracurricular
     */
    public function assignStudent(Extracurricular $extracurricular, int $studentUserId): Extracurricular
    {
        DB::transaction(fn() => $extracurricular->students()->attach($studentUserId));
        return $extracurricular->load('students');
    }

    /**
     * Unassign a student from an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @param int $studentUserId
     * @return Extracurricular
     */
    public function unassignStudent(Extracurricular $extracurricular, int $studentUserId): Extracurricular
    {
        DB::transaction(fn() => $extracurricular->students()->detach($studentUserId));
        return $extracurricular->load('students');
    }

    /**
     * Assign multiple students to an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @param array $studentUserIds
     * @return Extracurricular
     */
    public function assignStudents(Extracurricular $extracurricular, array $studentUserIds): Extracurricular
    {
        DB::transaction(fn() => $extracurricular->students()->sync($studentUserIds));
        return $extracurricular->load('students');
    }

    /**
     * Unassign multiple students from an extracurricular activity.
     *
     * @param Extracurricular $extracurricular
     * @param array $studentUserIds
     * @return Extracurricular
     */
    public function unassignStudents(Extracurricular $extracurricular, array $studentUserIds): Extracurricular
    {
        DB::transaction(fn() => $extracurricular->students()->detach($studentUserIds));
        return $extracurricular->load('students');
    }

    /**
     * Submit an extracurricular activity.
     *
     * @param StoreExtracurricularActivityRequest $request
     * @return ExtracurricularActivity
     */
    public function submitActivity(StoreExtracurricularActivityRequest $request): ExtracurricularActivity
    {
        return DB::transaction(function () use ($request) {
            $data = $request->validated();
            $data['extracurricular_id'] = $request->extracurricular_id;
            $data['user_id'] = $request->user()->id;

            if (isset($data['file']) && $data['file']->isValid()) {
                $data['file'] = $data['file']->store('extracurricular_activities', 'public');
            }

            return ExtracurricularActivity::create($data);
        });
    }

    /**
     * Approve an extracurricular activity.
     *
     * @param ApproveExtracurricularActivityRequest $request
     * @param ExtracurricularActivity $activity
     * @return ExtracurricularActivityResource
     */
    public function approveActivity(ApproveExtracurricularActivityRequest $request, ExtracurricularActivity $activity): ExtracurricularActivityResource
    {
        return DB::transaction(function () use ($request, $activity) {
            $activity->update([
                'grade' => $request->grade,
            ]);
            return new ExtracurricularActivityResource($activity->fresh());
        });
    }

    /**
     * Get all extracurricular activities with optional filters.
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllActivity(Request $request)
    {
        return ExtracurricularActivity::with('extracurricular', 'user')
            ->when($request->has('extracurricular_id'), function ($query) use ($request) {
                $query->where('extracurricular_id', $request->extracurricular_id);
            })
            ->when($request->has('user_id'), function ($query) use ($request) {
                $query->where('user_id', $request->user_id);
            })
            ->orderBy('updated_at', 'desc')
            ->paginate($request->limit ?? config('app.pagination.max'));
    }
}
