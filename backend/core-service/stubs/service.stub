<?php

namespace App\Services;

use App\Models\Dummy;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\Dummy\StoreDummyRequest;
use App\Http\Requests\Dummy\UpdateDummyRequest;

class DummyService
{
    public function paginate(Request $request)
    {
        return Dummy::paginate($request->limit ?? config('app.pagination.max'));
    }

    public function store(StoreDummyRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($validated) {
            $created = Dummy::create($validated);
            return $created;
        });
    }

    public function show(Dummy $model)
    {
        return $model;
    }

    public function update(Dummy $model, UpdateDummyRequest $request)
    {
        $validated = $request->validated();
        return DB::transaction(function () use ($model, $validated) {
            $model->update($validated);
            return $model;
        });
    }

    public function delete(Dummy $model)
    {
        DB::transaction(fn () => $model->delete());
    }
}
