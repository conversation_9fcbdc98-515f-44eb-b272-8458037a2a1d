parameters:
	ignoreErrors:
		-
			message: '#^PHPDoc tag @param references unknown parameter\: \$name$#'
			identifier: parameter.notFound
			count: 1
			path: app/Console/Commands/MakeApiController.php

		-
			message: '#^Method App\\Console\\Commands\\MakeService\:\:handle\(\) should return bool\|null but return statement is missing\.$#'
			identifier: return.missing
			count: 1
			path: app/Console/Commands/MakeService.php

		-
			message: '#^PHPDoc tag @param references unknown parameter\: \$name$#'
			identifier: parameter.notFound
			count: 1
			path: app/Console/Commands/MakeService.php

		-
			message: '#^Variable \$subClassroomId in empty\(\) always exists and is not falsy\.$#'
			identifier: empty.variable
			count: 1
			path: app/Exports/ClassAttendanceTemplateExport.php

		-
			message: '#^Comparison operation "\>" between int\<1, max\> and 0 is always true\.$#'
			identifier: greater.alwaysTrue
			count: 1
			path: app/Exports/Exam/QuestionGridExport.php

		-
			message: '#^Comparison operation "\>" between int\<1, max\> and 0 is always true\.$#'
			identifier: greater.alwaysTrue
			count: 1
			path: app/Exports/QuestionsCardStackedExport.php

		-
			message: '#^Offset int\<0, 4\> on array\{string, string, string, string, string\} on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.offset
			count: 1
			path: app/Exports/QuestionsCardStackedExport.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$academicYear\.$#'
			identifier: property.notFound
			count: 1
			path: app/Exports/StudentsClassSheet.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$guardian\.$#'
			identifier: property.notFound
			count: 1
			path: app/Exports/StudentsClassSheet.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$mother\.$#'
			identifier: property.notFound
			count: 1
			path: app/Exports/StudentsClassSheet.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$schools\.$#'
			identifier: property.notFound
			count: 1
			path: app/Exports/StudentsExport.php

		-
			message: '#^Relation ''academicYear'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Exports/StudentsExport.php

		-
			message: '#^Relation ''role'' is not found in App\\Models\\UserSchoolRole model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Exports/StudentsExport.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Exports/StudentsExport.php

		-
			message: '#^Access to an undefined property App\\Models\\SubClassroom\:\:\$fullName\.$#'
			identifier: property.notFound
			count: 1
			path: app/Exports/SubClassroomExport.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Database\\Eloquent\\Collection\<int,App\\Models\\SubClassroom\>\:\:map\(\) contains unresolvable type\.$#'
			identifier: argument.unresolvableType
			count: 1
			path: app/Exports/SubClassroomExport.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Exports/TeachersExport.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Exports/TeachersExport.php

		-
			message: '#^Call to an undefined method Illuminate\\Http\\Request\:\:validated\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Controllers/AssignmentSubmissionController.php

		-
			message: '#^Call to an undefined method Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection\:\:total\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Controllers/Controller.php

		-
			message: '#^Method App\\Services\\ExtracurricularService\:\:submitActivity\(\) invoked with 2 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: app/Http/Controllers/ExtracurricularController.php

		-
			message: '#^Call to an undefined method Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection\:\:total\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Controllers/ExtracurricularStudentController.php

		-
			message: '#^Expression on left side of \?\? is not nullable\.$#'
			identifier: nullCoalesce.expr
			count: 1
			path: app/Http/Controllers/ExtracurricularStudentController.php

		-
			message: '#^Call to method count\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Controllers/FacilityController.php

		-
			message: '#^Call to method first\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Controllers/FacilityController.php

		-
			message: '#^Access to an undefined property App\\Models\\LessonPlan\:\:\$program\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Controllers/LessonPlanController.php

		-
			message: '#^Access to an undefined property App\\Models\\LessonPlan\:\:\$topic\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Controllers/LessonPlanController.php

		-
			message: '#^Access to an undefined property App\\Models\\Information\:\:\$is_read\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Controllers/MobileInformationController.php

		-
			message: '#^Call to an undefined method Illuminate\\Contracts\\Pagination\\LengthAwarePaginator\:\:getCollection\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Controllers/MobileInformationController.php

		-
			message: '#^Call to an undefined method App\\Services\\PracticalTrainingService\:\:prepareReportDataPracticalTraining\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Controllers/PracticalTrainingController.php

		-
			message: '#^Call to static method loadView\(\) on an unknown class Barryvdh\\DomPDF\\Facade\\Pdf\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Controllers/PracticalTrainingController.php

		-
			message: '#^Parameter \#2 \$day of method App\\Services\\ScheduleService\:\:getCurrentSchedule\(\) expects int, string given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Controllers/ScheduleController.php

		-
			message: '#^Call to method count\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Controllers/SubjectController.php

		-
			message: '#^Call to method first\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Controllers/SubjectController.php

		-
			message: '#^Call to method where\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Middleware/SetActiveSchool.php

		-
			message: '#^Method App\\Http\\Requests\\Auth\\ResetPasswordRequest\:\:rules\(\) has invalid return type App\\Http\\Requests\\Auth\\ValidationRule\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Requests/Auth/ResetPasswordRequest.php

		-
			message: '#^Method App\\Http\\Requests\\Auth\\SendResetLinkRequest\:\:rules\(\) has invalid return type App\\Http\\Requests\\Auth\\ValidationRule\.$#'
			identifier: class.notFound
			count: 1
			path: app/Http/Requests/Auth/SendResetLinkRequest.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''detail'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Http/Requests/Exam/UpdateExamRequest.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$points\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Requests/ExamAttempt/GradeEssayRequest.php

		-
			message: '#^Left side of \|\| is always true\.$#'
			identifier: booleanOr.leftAlwaysTrue
			count: 1
			path: app/Http/Requests/School/StoreSchoolRequest.php

		-
			message: '#^Parameter \#1 \$role of method App\\Models\\User\:\:hasRole\(\) expects string, true given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Requests/School/StoreSchoolRequest.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$schools\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Requests/School/UpdateSchoolRequest.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$end_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$is_current\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$start_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AcademicYearResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AcademicYearResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$due_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$subClassroom\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$subject\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\AssignmentResource\:\:getTeacher\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Resources/AssignmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$assignment\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$file\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$grade\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$student\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AssignmentSubmissionResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AssignmentSubmissionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$checked_in_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$is_present\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$photo\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$schedule_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceSummaryResource\:\:\$total_absent\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceSummaryResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceSummaryResource\:\:\$total_present\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceSummaryResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceSummaryResource\:\:\$user\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceSummaryResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\AttendanceSummaryResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/AttendanceSummaryResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$user\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenAdminResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenAdminResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CanteenResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CanteenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$user\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\CashierResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/CashierResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ClassroomResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ClassroomResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ClassroomResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ClassroomResource\:\:\$subClassrooms\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ClassroomResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$file\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$link\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\DocumentResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/DocumentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$created_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$deleted_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$deleted_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$file_path\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$file_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventFileResource\:\:\$updated_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^PHPDoc tag @return with type array\|Illuminate\\Contracts\\Support\\Arrayable\|JsonSerializable is not subtype of native type array\.$#'
			identifier: return.phpDocType
			count: 1
			path: app/Http/Resources/EventFileResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$created_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$deleted_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$deleted_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$end_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$event_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$excerpt\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$is_published\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$location\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$start_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$thumbnail_file\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\EventResource\:\:\$updated_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^PHPDoc tag @return with type array\|Illuminate\\Contracts\\Support\\Arrayable\|JsonSerializable is not subtype of native type array\.$#'
			identifier: return.phpDocType
			count: 1
			path: app/Http/Resources/EventResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$essay_answer\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$examQuestion\.$#'
			identifier: property.notFound
			count: 7
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$exam_attempt_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$media_answer\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$order_index\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$points_awarded\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptAnswerResource\:\:\$selected_option_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\ExamAttemptAnswerResource\:\:relationLoaded\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptAnswerResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$exam\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$exam_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$start_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$student\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$submit_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptDetailResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\ExamAttemptDetailResource\:\:relationLoaded\(\)\.$#'
			identifier: method.notFound
			count: 2
			path: app/Http/Resources/ExamAttemptDetailResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$correct_count\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$exam\.$#'
			identifier: property.notFound
			count: 5
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$exam_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$graded_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$questions_with_status\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$score\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$start_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$student\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$submit_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamAttemptResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\ExamAttemptResource\:\:relationLoaded\(\)\.$#'
			identifier: method.notFound
			count: 2
			path: app/Http/Resources/ExamAttemptResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$exam_question_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$is_correct\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$media_answer_options\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$order_index\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$question_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionHasAnswerOptionResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionHasAnswerOptionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$answer_key_essay\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$competency_indicator\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$exam_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$kd_number\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$learning_outcome\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$level_kognitif\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$media_questions\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$order_index\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$points\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$question_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamQuestionResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamQuestionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$attempts\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$creator\.$#'
			identifier: property.notFound
			count: 3
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$end_datetime\.$#'
			identifier: property.notFound
			count: 3
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$exams_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$is_published\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$is_shuffled\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$start_datetime\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$subClassroomSubject\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$term\.$#'
			identifier: property.notFound
			count: 6
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\ExamResource\:\:relationLoaded\(\)\.$#'
			identifier: method.notFound
			count: 4
			path: app/Http/Resources/ExamResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$attempt_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$correct_count\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$score\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$start_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$student_email\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$student_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$student_name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamResultResource\:\:\$submit_datetime\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamResultResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamTokenResource\:\:\$exam_token\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamTokenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamTokenResource\:\:\$exam_token_expiration\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamTokenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExamTokenResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExamTokenResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularActivityResource\:\:\$file\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$checked_in_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$extracurricular_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$is_present\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$photo\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularAttendanceResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularAttendanceResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$fee\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$teacher\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$extracurricular_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$predicate\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$student\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$student_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ExtracurricularStudentResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Using nullsafe property access "\?\-\>full_name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Using nullsafe property access "\?\-\>sequence" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Http/Resources/ExtracurricularStudentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$good_condition\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$image\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$major_damage\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$minor_damage\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$stock\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$unit\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\FacilityResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/FacilityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$assignment_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$exam_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$report_card_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$score\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$student_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\GradeResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/GradeResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$information_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$read_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationReadResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationReadResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$created_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$deleted_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$deleted_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$end_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$is_read\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$message\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$sent_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$start_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationResource\:\:\$updated_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$information_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$target_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$target_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\InformationTargetResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/InformationTargetResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$balance\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$daily_limit\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$is_active\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$uid\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$amount\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$file\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$nfc_card_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$transaction_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NfcCardTransactionResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NfcCardTransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$sent_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\NotificationResource\:\:\$user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/NotificationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\OrganizationResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/OrganizationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\OrganizationResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/OrganizationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\OrganizationResource\:\:\$position\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/OrganizationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\OrganizationResource\:\:\$teacher\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/OrganizationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\OrganizationResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/OrganizationResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$learning_objective\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$practical_training_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingDataResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingDataResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$alpha\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$class_teacher_note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$instructor_name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$leave\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$mentor_name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$period_end\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$period_start\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$place\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$present\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$program_of_study\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$sick\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$specialization\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$student_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PracticalTrainingResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PracticalTrainingResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$image\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$price\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$stock\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProductResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProductResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$academic_year_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$duration_weeks\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$end_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$month\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$parent_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$semester\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$source_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$start_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$teacher_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$topic\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ProgramResource\:\:\$week\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ProgramResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$canteen\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$canteenAdminUser\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$canteen_admin_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$product\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$product_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$quantity\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseLogResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseLogResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$canteen\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$canteenAdminUser\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$canteen_admin_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$product\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$product_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$quantity\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\PurchaseResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/PurchaseResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$class_teacher_note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$final_exported_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$final_locked_score\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$final_score\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$final_term_note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$grade\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$mid_exported_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$mid_locked_score\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$mid_term_note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$revision_score\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$student_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$sub_classroom_subject_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ReportCardResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ReportCardResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\RoleResource\:\:\$guard_name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/RoleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\RoleResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/RoleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\RoleResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/RoleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$created_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$deleted_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$deleted_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$description\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$image\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$schedule_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$title\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleActivityResource\:\:\$updated_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$end_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$period\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$start_time\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchedulePeriodResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchedulePeriodResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$day\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$schedulePeriod\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$subClassroomSubject\.$#'
			identifier: property.notFound
			count: 6
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\ScheduleResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\ScheduleResource\:\:getLatestAttendance\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Resources/ScheduleResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolLevelResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolLevelResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolLevelResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolLevelResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolLevelResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolLevelResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolLevelResource\:\:\$slug\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolLevelResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolLevelResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolLevelResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$address\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$city\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$district\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$email\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$foundation_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$logo\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$phone_number\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$province\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$registration_number\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$school_level_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$subdistrict\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$website\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SchoolResource\:\:\$year_founded\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Call to an undefined method App\\Http\\Resources\\SchoolResource\:\:headmaster\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Http/Resources/SchoolResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\StudentDailyActivityResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/StudentDailyActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\StudentDailyActivityResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/StudentDailyActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\StudentDailyActivityResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/StudentDailyActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\StudentDailyActivityResource\:\:\$order\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/StudentDailyActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\StudentDailyActivityResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/StudentDailyActivityResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$classroom\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$classroom_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$sequence\.$#'
			identifier: property.notFound
			count: 2
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$subClassroom\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$subject\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$teacher\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubClassroomSubjectResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubClassroomSubjectResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$enrolled_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$is_enrolled\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$student_user_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$sub_classroom_subject_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$unenrolled_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\SubjectEnrollmentResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/SubjectEnrollmentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$academic_year_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$end_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$start_date\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TermResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TermResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$price\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$product\.$#'
			identifier: property.notFound
			count: 3
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$product_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$quantity\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionItemResource\:\:\$transaction_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionItemResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$canteen_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$cashier_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$change_amount\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$code\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$paid_amount\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$payment_method\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$total_price\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TransactionResource\:\:\$transacted_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TransactionResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$amount\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$created_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$notes\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$payment_method\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$payment_number\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$photo\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$status\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$token\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\TuitionPaymentResource\:\:\$updated_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/TuitionPaymentResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\UserResource\:\:\$default_password\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/UserResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\UserResource\:\:\$photo\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/UserResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\UserResource\:\:\$roles\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/UserResource.php

		-
			message: '#^Access to an undefined property App\\Http\\Resources\\UserResource\:\:\$schools\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Resources/UserResource.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Imports/Parent/ParentsImport.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignSchoolRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Imports/Parent/ParentsImport.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 3
			path: app/Imports/Student/StudentsImport.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignSchoolRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 2
			path: app/Imports/Student/StudentsImport.php

		-
			message: '#^Call to method syncWithoutDetaching\(\) on an unknown class App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Imports/Student/StudentsImport.php

		-
			message: '#^Variable \$percent on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.variable
			count: 1
			path: app/Imports/Student/StudentsImport.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 2
			path: app/Imports/Teacher/TeachersImport.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignSchoolRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Imports/Teacher/TeachersImport.php

		-
			message: '#^Expression on left side of \?\? is not nullable\.$#'
			identifier: nullCoalesce.expr
			count: 1
			path: app/Imports/Teacher/TeachersImport.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$exams_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Jobs/SyncAttemptToReportCardJob.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$points_awarded\.$#'
			identifier: property.notFound
			count: 1
			path: app/Jobs/SyncAttemptToReportCardJob.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$selectedOption\.$#'
			identifier: property.notFound
			count: 1
			path: app/Jobs/SyncAttemptToReportCardJob.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$sub_classroom_subject_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Jobs/SyncAttemptToReportCardJob.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$term_id\.$#'
			identifier: property.notFound
			count: 3
			path: app/Jobs/SyncAttemptToReportCardJob.php

		-
			message: '#^PHPDoc type array of property App\\Models\\AcademicYear\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/AcademicYear.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Article\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Article.php

		-
			message: '#^Access to an undefined property App\\Models\\SubClassroomSubject\:\:\$teacher\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/Assignment.php

		-
			message: '#^Method App\\Models\\Assignment\:\:getTeacher\(\) has invalid return type App\\Models\\Teacher\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/Assignment.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Assignment\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Assignment.php

		-
			message: '#^PHPDoc tag @var above a method has no effect\.$#'
			identifier: varTag.misplaced
			count: 2
			path: app/Models/AssignmentSubmission.php

		-
			message: '#^PHPDoc tag @var does not specify variable name\.$#'
			identifier: varTag.noVariable
			count: 2
			path: app/Models/AssignmentSubmission.php

		-
			message: '#^PHPDoc type array of property App\\Models\\AssignmentSubmission\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/AssignmentSubmission.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Attendance\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Attendance.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Canteen\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Canteen.php

		-
			message: '#^PHPDoc type array of property App\\Models\\CanteenAdmin\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/CanteenAdmin.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Cashier\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Cashier.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Category\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Category.php

		-
			message: '#^PHPDoc tag @var above a method has no effect\.$#'
			identifier: varTag.misplaced
			count: 1
			path: app/Models/Classroom.php

		-
			message: '#^PHPDoc tag @var does not specify variable name\.$#'
			identifier: varTag.noVariable
			count: 1
			path: app/Models/Classroom.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Classroom\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Classroom.php

		-
			message: '#^PHPDoc type string of property App\\Models\\Classroom\:\:\$guarded is not covariant with PHPDoc type array\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$guarded\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Classroom.php

		-
			message: '#^Property App\\Models\\Classroom\:\:\$guarded \(string\) does not accept default value of type array\<int, string\>\.$#'
			identifier: property.defaultValue
			count: 1
			path: app/Models/Classroom.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$essay_answer\.$#'
			identifier: property.notFound
			count: 2
			path: app/Models/ExamAttempt.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ExamAttempt.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$media_answer\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ExamAttempt.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$questions\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ExamAttempt.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$selected_option_id\.$#'
			identifier: property.notFound
			count: 2
			path: app/Models/ExamAttempt.php

		-
			message: '#^Using nullsafe property access "\?\-\>order_index" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Models/ExamAttempt.php

		-
			message: '#^PHPDoc tag @return with type Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\|null is not subtype of native type Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\.$#'
			identifier: return.phpDocType
			count: 2
			path: app/Models/ExamAttemptAnswer.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ExamAttemptAnswer\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ExamAttemptAnswer.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ExamAttemptAnswer\:\:\$fillable is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$fillable\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ExamAttemptAnswer.php

		-
			message: '#^Access to an undefined property App\\Models\\ExamQuestion\:\:\$image\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ExamQuestion.php

		-
			message: '#^Access to an undefined property App\\Models\\ExamQuestionHasAnswerOption\:\:\$image\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/ExamQuestionHasAnswerOption.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Extracurricular\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Extracurricular.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ExtracurricularActivity\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ExtracurricularActivity.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ExtracurricularAttendance\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ExtracurricularAttendance.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ExtracurricularStudent\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ExtracurricularStudent.php

		-
			message: '#^Method App\\Models\\Facility\:\:school\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/Facility.php

		-
			message: '#^Method App\\Models\\Facility\:\:school\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\School, \$this\(App\\Models\\Facility\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Facility.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Facility\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Facility.php

		-
			message: '#^Access to an undefined property App\\Models\\Foundation\:\:\$schools\.$#'
			identifier: property.notFound
			count: 4
			path: app/Models/Foundation.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$read_at\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/Information.php

		-
			message: '#^Method App\\Models\\Information\:\:targets\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\InformationTarget, \$this\(App\\Models\\Information\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Information.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Information\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Information.php

		-
			message: '#^PHPDoc type array of property App\\Models\\InformationRead\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/InformationRead.php

		-
			message: '#^PHPDoc type array of property App\\Models\\InformationTarget\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/InformationTarget.php

		-
			message: '#^PHPDoc type array of property App\\Models\\LessonPlan\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/LessonPlan.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Notification\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Notification.php

		-
			message: '#^PHPDoc tag @var above a method has no effect\.$#'
			identifier: varTag.misplaced
			count: 2
			path: app/Models/Organization.php

		-
			message: '#^PHPDoc tag @var does not specify variable name\.$#'
			identifier: varTag.noVariable
			count: 2
			path: app/Models/Organization.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Organization\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Organization.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Position\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Position.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Product\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Product.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Purchase\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Purchase.php

		-
			message: '#^PHPDoc type array of property App\\Models\\PurchaseLog\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/PurchaseLog.php

		-
			message: '#^Method App\\Models\\Schedule\:\:attendances\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Attendance, \$this\(App\\Models\\Schedule\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Schedule.php

		-
			message: '#^Method App\\Models\\Schedule\:\:getLatestAttendance\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Model\|null\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Schedule.php

		-
			message: '#^Method App\\Models\\Schedule\:\:scheduleActivities\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\ScheduleActivity, \$this\(App\\Models\\Schedule\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Schedule.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Schedule\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Schedule.php

		-
			message: '#^PHPDoc type array of property App\\Models\\ScheduleActivity\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/ScheduleActivity.php

		-
			message: '#^PHPDoc tag @var above a method has no effect\.$#'
			identifier: varTag.misplaced
			count: 1
			path: app/Models/SchedulePeriod.php

		-
			message: '#^PHPDoc tag @var does not specify variable name\.$#'
			identifier: varTag.noVariable
			count: 1
			path: app/Models/SchedulePeriod.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\AcademicYear\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Classroom\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Extracurricular\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Program\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SchedulePeriod\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SubClassroom\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Subject\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:academicYears\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\AcademicYear\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\AcademicYear, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:classrooms\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Classroom\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Classroom, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:extracurriculars\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Extracurricular\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Extracurricular, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:foundation\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Foundation, App\\Models\\School\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Foundation, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:programs\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Program\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Program, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:schedulePeriods\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SchedulePeriod\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SchedulePeriod, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:schoolLevel\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\SchoolLevel, App\\Models\\School\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\SchoolLevel, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:subClassrooms\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SubClassroom\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\SubClassroom, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:subjects\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Subject\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Subject, \$this\(App\\Models\\School\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^Method App\\Models\\School\:\:users\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\User, Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\User, \$this\(App\\Models\\School\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/School.php

		-
			message: '#^PHPDoc type array\<int, string\> of property App\\Models\\School\:\:\$fillable is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$fillable\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/School.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 5
			path: app/Models/School.php

		-
			message: '#^Type string in generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\User, Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\> in PHPDoc tag @return is not subtype of template type TPivotModel of Illuminate\\Database\\Eloquent\\Relations\\Pivot \= Illuminate\\Database\\Eloquent\\Relations\\Pivot of class Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\.$#'
			identifier: generics.notSubtype
			count: 5
			path: app/Models/School.php

		-
			message: '#^PHPDoc type array of property App\\Models\\StudentActivityReport\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/StudentActivityReport.php

		-
			message: '#^PHPDoc type array of property App\\Models\\StudentDailyActivity\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/StudentDailyActivity.php

		-
			message: '#^PHPDoc type array of property App\\Models\\StudentDailyActivityReport\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/StudentDailyActivityReport.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/SubClassroom.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Models/SubClassroom.php

		-
			message: '#^PHPDoc type array of property App\\Models\\SubClassroomSubjectHasStudent\:\:\$fillable is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$fillable\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/SubClassroomSubjectHasStudent.php

		-
			message: '#^Method App\\Models\\Subject\:\:subClassroomSubjects\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\SubClassroom, \$this\(App\\Models\\Subject\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Subject.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Subject\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Subject.php

		-
			message: '#^PHPDoc type string of property App\\Models\\SystemConfig\:\:\$fillable is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$fillable\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/SystemConfig.php

		-
			message: '#^Property App\\Models\\SystemConfig\:\:\$fillable \(string\) does not accept default value of type array\<int, string\>\.$#'
			identifier: property.defaultValue
			count: 1
			path: app/Models/SystemConfig.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Tag\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^PHPDoc type array of property App\\Models\\Transaction\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/Transaction.php

		-
			message: '#^PHPDoc type array of property App\\Models\\TransactionItem\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/TransactionItem.php

		-
			message: '#^Access to an undefined property App\\Models\\TuitionFee\:\:\$frequency\.$#'
			identifier: property.notFound
			count: 2
			path: app/Models/TuitionFee.php

		-
			message: '#^PHPDoc type array of property App\\Models\\TuitionFee\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/TuitionFee.php

		-
			message: '#^Method App\\Models\\TuitionInvoice\:\:tuitionPayments\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\TuitionPayment, \$this\(App\\Models\\TuitionInvoice\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/TuitionInvoice.php

		-
			message: '#^PHPDoc type array of property App\\Models\\TuitionInvoice\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/TuitionInvoice.php

		-
			message: '#^Method App\\Models\\TuitionPayment\:\:tuitionInvoices\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\TuitionInvoice, \$this\(App\\Models\\TuitionPayment\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/TuitionPayment.php

		-
			message: '#^PHPDoc type array of property App\\Models\\TuitionPayment\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/TuitionPayment.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$schools\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$subClassroom\.$#'
			identifier: property.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to function method_exists\(\) with \$this\(App\\Models\\User\) and ''hasRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to method attach\(\) on an unknown class App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to method attach\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 2
			path: app/Models/User.php

		-
			message: '#^Call to method detach\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to method where\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Call to method wherePivot\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^If condition is always true\.$#'
			identifier: if.alwaysTrue
			count: 2
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:academicYear\(\) has invalid return type App\\Models\\Collection\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:academicYear\(\) should return App\\Models\\Collection but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\AcademicYear, \$this\(App\\Models\\User\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:assignRole\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:assignRole\(\) should return App\\Models\\BelongsTo but return statement is missing\.$#'
			identifier: return.missing
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:attendances\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:attendances\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Attendance, \$this\(App\\Models\\User\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:canteenAdmin\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:canteenAdmin\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasOne\<App\\Models\\CanteenAdmin, \$this\(App\\Models\\User\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:cashier\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:cashier\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\HasOne\<App\\Models\\Cashier, \$this\(App\\Models\\User\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:children\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:children\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\User, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:extracurriculars\(\) has invalid return type App\\Models\\HasMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:extracurriculars\(\) should return App\\Models\\HasMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Extracurricular, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:foundations\(\) has invalid return type App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:foundations\(\) should return App\\Models\\BelongsToMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Foundation, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:getAssignments\(\) has invalid return type App\\Models\\Collection\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:getAssignments\(\) should return App\\Models\\Collection but returns Illuminate\\Database\\Eloquent\\Collection\<int, App\\Models\\Assignment\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:getRoleInSchool\(\) has invalid return type App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:getSubjects\(\) has invalid return type App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:getSubjects\(\) should return App\\Models\\BelongsToMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Subject, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:parent\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:parent\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\User, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:roles\(\) has invalid return type App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:roles\(\) should return App\\Models\\BelongsToMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Role, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:schools\(\) has invalid return type App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:schools\(\) should return App\\Models\\BelongsToMany but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\School, \$this\(App\\Models\\User\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:sendPasswordResetNotification\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:sendPasswordResetNotification\(\) should return App\\Models\\BelongsTo but return statement is missing\.$#'
			identifier: return.missing
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:subClassroom\(\) has invalid return type App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:subClassroom\(\) should return App\\Models\\BelongsTo but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\SubClassroom, \$this\(App\\Models\\User\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/User.php

		-
			message: '#^PHPDoc type array of property App\\Models\\User\:\:\$casts is not covariant with PHPDoc type array\<string, string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$casts\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/User.php

		-
			message: '#^PHPDoc type array\<string\> of property App\\Models\\User\:\:\$hidden is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$hidden\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/User.php

		-
			message: '#^Relation ''schools'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Models/User.php

		-
			message: '#^Relation ''canteenAdmins'' is not found in App\\Models\\Canteen model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Canteen/CanteenRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\CanteenAdmin\:\:\$user\.$#'
			identifier: property.notFound
			count: 4
			path: app/Repositories/CanteenAdmin/CanteenAdminRepositoryImpl.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignSchoolRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Repositories/CanteenAdmin/CanteenAdminRepositoryImpl.php

		-
			message: '#^Relation ''canteen'' is not found in App\\Models\\CanteenAdmin model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/CanteenAdmin/CanteenAdminRepositoryImpl.php

		-
			message: '#^Relation ''user'' is not found in App\\Models\\CanteenAdmin model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/CanteenAdmin/CanteenAdminRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\Cashier\:\:\$user\.$#'
			identifier: property.notFound
			count: 4
			path: app/Repositories/Cashier/CashierRepositoryImpl.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''assignSchoolRole'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Repositories/Cashier/CashierRepositoryImpl.php

		-
			message: '#^Relation ''canteen'' is not found in App\\Models\\Cashier model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Cashier/CashierRepositoryImpl.php

		-
			message: '#^Relation ''user'' is not found in App\\Models\\Cashier model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Cashier/CashierRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\Classroom\:\:\$subClassrooms\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Classroom/ClassroomRepositoryImpl.php

		-
			message: '#^Relation ''subClassrooms'' is not found in App\\Models\\Classroom model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Classroom/ClassroomRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$file_path\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Event/EventRepositoryImpl.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Relations\\HasMany\:\:onlyTrashed\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Repositories/Event/EventRepositoryImpl.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Relations\\HasMany\:\:withTrashed\(\)\.$#'
			identifier: method.notFound
			count: 2
			path: app/Repositories/Event/EventRepositoryImpl.php

		-
			message: '#^Method App\\Repositories\\Event\\EventRepositoryImpl\:\:uploadEventFile\(\) should return App\\Models\\EventFile but returns Illuminate\\Database\\Eloquent\\Model\.$#'
			identifier: return.type
			count: 1
			path: app/Repositories/Event/EventRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$answerOptions\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$created_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$deleted_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$examQuestion\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$exam_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$is_correct\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$media_questions\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$points\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$selectedOption\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$updated_by\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Called ''env'' outside of the config directory which returns null when the config is cached, use ''config''\.$#'
			identifier: larastan.noEnvCallsOutsideOfConfig
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Expression on left side of \?\? is not nullable\.$#'
			identifier: nullCoalesce.expr
			count: 4
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Left side of && is always false\.$#'
			identifier: booleanAnd.leftAlwaysFalse
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Relation ''subClassroomSubject'' is not found in App\\Models\\Exam model\.$#'
			identifier: larastan.relationExistence
			count: 5
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Strict comparison using \!\=\= between \*NEVER\* and '''' will always evaluate to true\.$#'
			identifier: notIdentical.alwaysTrue
			count: 1
			path: app/Repositories/Exam/ExamRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$answerOptions\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamAttempt/ExamAttemptRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$question_type\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamAttempt/ExamAttemptRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:transform\(\) expects callable\(Illuminate\\Database\\Eloquent\\Model, int\)\: App\\Models\\ExamAttemptAnswer, Closure\(App\\Models\\ExamAttemptAnswer\)\: App\\Models\\ExamAttemptAnswer given\.$#'
			identifier: argument.type
			count: 3
			path: app/Repositories/ExamAttempt/ExamAttemptRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$message of static method Illuminate\\Log\\Logger\:\:info\(\) expects array\|Illuminate\\Contracts\\Support\\Arrayable\|Illuminate\\Contracts\\Support\\Jsonable\|Illuminate\\Support\\Stringable\|string, int given\.$#'
			identifier: argument.type
			count: 3
			path: app/Repositories/ExamAttempt/ExamAttemptRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$content\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$id\.$#'
			identifier: property.notFound
			count: 2
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$is_correct\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$media_answer_options\.$#'
			identifier: property.notFound
			count: 3
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$name\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Access to an undefined property Illuminate\\Database\\Eloquent\\Model\:\:\$order_index\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Model\:\:answerOptions\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Call to function method_exists\(\) with Illuminate\\Filesystem\\FilesystemAdapter and ''copy'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Called ''env'' outside of the config directory which returns null when the config is cached, use ''config''\.$#'
			identifier: larastan.noEnvCallsOutsideOfConfig
			count: 2
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Method App\\Repositories\\ExamQuestion\\ExamQuestionRepositoryImpl\:\:duplicateMedia\(\) never returns null so it can be removed from the return type\.$#'
			identifier: return.unusedType
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Offset 2 on array\{non\-falsy\-string, non\-falsy\-string, string\} on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.offset
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Database\\Eloquent\\Collection\<int,App\\Models\\Exam\>\:\:map\(\) contains unresolvable type\.$#'
			identifier: argument.unresolvableType
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$callback of static method Illuminate\\Database\\Connection\:\:transaction\(\) contains unresolvable type\.$#'
			identifier: argument.unresolvableType
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$q of method App\\Repositories\\ExamQuestion\\ExamQuestionRepositoryImpl\:\:addMediaUrlsToQuestion\(\) expects App\\Models\\ExamQuestion, Illuminate\\Database\\Eloquent\\Model given\.$#'
			identifier: argument.type
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Relation ''subClassroomSubject'' is not found in App\\Models\\Exam model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Strict comparison using \=\=\= between mixed~''multiple_choice'' and ''multiple_choice'' will always evaluate to false\.$#'
			identifier: identical.alwaysFalse
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Strict comparison using \=\=\= between resource\|null and false will always evaluate to false\.$#'
			identifier: identical.alwaysFalse
			count: 1
			path: app/Repositories/ExamQuestion/ExamQuestionRepositoryImpl.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/ExamToken/ExamTokenRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$canteens\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$classrooms\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Call to an undefined method Illuminate\\Contracts\\Database\\Eloquent\\Builder\:\:withTrashed\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Relation ''creator'' is not found in App\\Models\\Information model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Relation ''deleter'' is not found in App\\Models\\Information model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Relation ''updater'' is not found in App\\Models\\Information model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Information/InformationRepositoryImpl.php

		-
			message: '#^Relation ''program'' is not found in App\\Models\\LessonPlan model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Repositories/LessonPlan/LessonPlanRepositoryImpl.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Repositories/LessonPlan/LessonPlanRepositoryImpl.php

		-
			message: '#^Anonymous function has an unused use \$request\.$#'
			identifier: closure.unusedUse
			count: 1
			path: app/Repositories/Module/ModuleRepositoryImpl.php

		-
			message: '#^Undefined variable\: \$validated$#'
			identifier: variable.undefined
			count: 1
			path: app/Repositories/Module/ModuleRepositoryImpl.php

		-
			message: '#^Relation ''position'' is not found in App\\Models\\Organization model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Organization/OrganizationRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\Program\:\:\$lessonPlan\.$#'
			identifier: property.notFound
			count: 2
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Property App\\Models\\Program\:\:\$semester \(App\\Enums\\SemesterType\|null\) does not accept int\.$#'
			identifier: assign.propertyType
			count: 2
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Relation ''academicYear'' is not found in App\\Models\\Program model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Relation ''school'' is not found in App\\Models\\Program model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Relation ''subTopics'' is not found in App\\Models\\Program model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Strict comparison using \=\=\= between App\\Enums\\SemesterType\|null and ''semester_1'' will always evaluate to false\.$#'
			identifier: identical.alwaysFalse
			count: 1
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Strict comparison using \=\=\= between App\\Enums\\SemesterType\|null and ''semester_2'' will always evaluate to false\.$#'
			identifier: identical.alwaysFalse
			count: 1
			path: app/Repositories/Program/ProgramRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\Purchase\:\:\$note\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Purchase/PurchaseRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\Purchase\:\:\$product\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Purchase/PurchaseRepositoryImpl.php

		-
			message: '#^Anonymous function has an unused use \$toAttach\.$#'
			identifier: closure.unusedUse
			count: 1
			path: app/Repositories/SubClassroom/SubClassroomRepositoryImpl.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 4
			path: app/Repositories/SubClassroom/SubClassroomRepositoryImpl.php

		-
			message: '#^Relation ''subject'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/SubClassroom/SubClassroomRepositoryImpl.php

		-
			message: '#^Relation ''teacher'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/SubClassroom/SubClassroomRepositoryImpl.php

		-
			message: '#^Variable \$studentUserIds on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.variable
			count: 1
			path: app/Repositories/SubClassroom/SubClassroomRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$isSuperadmin\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Subject/SubjectRepositoryImpl.php

		-
			message: '#^Expression on left side of \?\? is not nullable\.$#'
			identifier: nullCoalesce.expr
			count: 1
			path: app/Repositories/Subject/SubjectRepositoryImpl.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Subject/SubjectRepositoryImpl.php

		-
			message: '#^Relation ''subject'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Subject/SubjectRepositoryImpl.php

		-
			message: '#^Relation ''teacher'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Subject/SubjectRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\SubClassroomSubject\:\:\$subject\.$#'
			identifier: property.notFound
			count: 2
			path: app/Repositories/SubjectEnrollment/SubjectEnrollmentRepositoryImpl.php

		-
			message: '#^Relation ''subject'' is not found in App\\Models\\SubClassroomSubject model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/SubjectEnrollment/SubjectEnrollmentRepositoryImpl.php

		-
			message: '#^Relation ''academicYear'' is not found in App\\Models\\Term model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Repositories/Term/TermRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$cashier\.$#'
			identifier: property.notFound
			count: 1
			path: app/Repositories/Transaction/TransactionRepositoryImpl.php

		-
			message: '#^Relation ''items'' is not found in App\\Models\\Transaction model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/Transaction/TransactionRepositoryImpl.php

		-
			message: '#^Access to an undefined property App\\Models\\ExtracurricularStudent\:\:\$extracurricular\.$#'
			identifier: property.notFound
			count: 6
			path: app/Repositories/TuitionInvoice/TuitionInvoiceRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$attributes of static method Illuminate\\Database\\Eloquent\\Builder\<App\\Models\\TuitionInvoice\>\:\:create\(\) expects array\<string, mixed\>, App\\Http\\Requests\\TuitionInvoice\\StoreTuitionInvoiceRequest given\.$#'
			identifier: argument.type
			count: 1
			path: app/Repositories/TuitionInvoice/TuitionInvoiceRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$string of function str_pad expects string, int given\.$#'
			identifier: argument.type
			count: 1
			path: app/Repositories/TuitionInvoice/TuitionInvoiceRepositoryImpl.php

		-
			message: '#^Relation ''extracurricular'' is not found in App\\Models\\ExtracurricularStudent model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Repositories/TuitionInvoice/TuitionInvoiceRepositoryImpl.php

		-
			message: '#^Argument of an invalid type Illuminate\\Database\\Eloquent\\Model supplied for foreach, only iterables are supported\.$#'
			identifier: foreach.nonIterable
			count: 2
			path: app/Repositories/TuitionPayment/TuitionPaymentRepositoryImpl.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\:\:attach\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Repositories/TuitionPayment/TuitionPaymentRepositoryImpl.php

		-
			message: '#^Parameter \#1 \$string of function str_pad expects string, int given\.$#'
			identifier: argument.type
			count: 1
			path: app/Repositories/TuitionPayment/TuitionPaymentRepositoryImpl.php

		-
			message: '#^Unreachable statement \- code above always terminates\.$#'
			identifier: deadCode.unreachable
			count: 1
			path: app/Repositories/TuitionPayment/TuitionPaymentRepositoryImpl.php

		-
			message: '#^Result of method App\\Repositories\\AcademicYear\\AcademicYearRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/AcademicYearService.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Builder\<Illuminate\\Database\\Eloquent\\Model\>\:\:getByRole\(\)\.$#'
			identifier: method.notFound
			count: 3
			path: app/Services/AttendanceService.php

		-
			message: '#^Call to an undefined method Illuminate\\Http\\Request\:\:validated\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Services/AttendanceService.php

		-
			message: '#^Relation ''position'' is not found in App\\Models\\Organization model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/AttendanceService.php

		-
			message: '#^Relation ''teacher'' is not found in App\\Models\\Organization model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/AttendanceService.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 3
			path: app/Services/AttendanceService.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$parent\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Access to an undefined property PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:\$default_password\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Access to an undefined property PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:\$password\.$#'
			identifier: property.notFound
			count: 2
			path: app/Services/AuthService.php

		-
			message: '#^Access to an undefined property PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:\$photo\.$#'
			identifier: property.notFound
			count: 2
			path: app/Services/AuthService.php

		-
			message: '#^Call to an undefined method PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:clearDefaultPassword\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Call to an undefined method PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:load\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Call to an undefined method PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\:\:save\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''clearDefaultPassword'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 2
			path: app/Services/AuthService.php

		-
			message: '#^Call to method where\(\) on an unknown class App\\Models\\BelongsToMany\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Cannot access property \$is_active on PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\|false\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Cannot call method load\(\) on PHPOpenSourceSaver\\JWTAuth\\Contracts\\JWTSubject\|false\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Negated boolean expression is always false\.$#'
			identifier: booleanNot.alwaysFalse
			count: 2
			path: app/Services/AuthService.php

		-
			message: '#^Parameter \#1 \$forceForever of static method PHPOpenSourceSaver\\JWTAuth\\JWT\:\:invalidate\(\) expects bool, PHPOpenSourceSaver\\JWTAuth\\Token\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Parameter \#1 \$forceForever of static method PHPOpenSourceSaver\\JWTAuth\\JWT\:\:refresh\(\) expects bool, PHPOpenSourceSaver\\JWTAuth\\Token\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/AuthService.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/AuthService.php

		-
			message: '#^Result of method App\\Repositories\\CanteenAdmin\\CanteenAdminRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/CanteenAdminService.php

		-
			message: '#^Result of method App\\Repositories\\Canteen\\CanteenRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/CanteenService.php

		-
			message: '#^Result of method App\\Repositories\\Cashier\\CashierRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/CashierService.php

		-
			message: '#^Result of method App\\Repositories\\Category\\CategoryRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/CategoryService.php

		-
			message: '#^Result of method App\\Repositories\\Classroom\\ClassroomRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ClassroomService.php

		-
			message: '#^Result of method App\\Repositories\\Document\\DocumentRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Relation ''school'' is not found in App\\Models\\Dormitory model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/DormitoryService.php

		-
			message: '#^Result of method App\\Repositories\\Event\\EventRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/EventService.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Model\:\:answerOptions\(\)\.$#'
			identifier: method.notFound
			count: 2
			path: app/Services/ExamQuestionService.php

		-
			message: '#^Negated boolean expression is always false\.$#'
			identifier: booleanNot.alwaysFalse
			count: 1
			path: app/Services/ExamQuestionService.php

		-
			message: '#^Result of method App\\Repositories\\ExamQuestion\\ExamQuestionRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamQuestionService.php

		-
			message: '#^Result of method App\\Repositories\\ExamQuestion\\ExamQuestionRepository\:\:storeMultiple\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamQuestionService.php

		-
			message: '#^Call to function is_numeric\(\) with int will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Call to function method_exists\(\) with App\\Enums\\CognitiveLevel and ''label'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Call to function method_exists\(\) with App\\Enums\\ExamType and ''label'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Call to function method_exists\(\) with App\\Models\\User and ''detail'' will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Cannot cast App\\Enums\\ExamType to string\.$#'
			identifier: cast.string
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Instanceof between App\\Enums\\ExamType and BackedEnum will always evaluate to true\.$#'
			identifier: instanceof.alwaysTrue
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Negated boolean expression is always false\.$#'
			identifier: booleanNot.alwaysFalse
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Offset 2 on array\{non\-falsy\-string, string, non\-falsy\-string, string, string\} on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.offset
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Offset 4 on array\{non\-falsy\-string, string, non\-falsy\-string, string, string\} on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.offset
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Relation ''subClassroomSubject'' is not found in App\\Models\\Exam model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/ExamService.php

		-
			message: '#^Result of method App\\Repositories\\Exam\\ExamRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Result of method App\\Repositories\\Exam\\ExamRepository\:\:grade\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Result of method App\\Repositories\\Exam\\ExamRepository\:\:publish\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Result of method App\\Repositories\\Exam\\ExamRepository\:\:unpublish\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 3
			path: app/Services/ExamService.php

		-
			message: '#^Using nullsafe property access "\?\-\>semester" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Services/ExamService.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$schools\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/ExamTokenService.php

		-
			message: '#^Result of method App\\Repositories\\ExamToken\\ExamTokenRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ExamTokenService.php

		-
			message: '#^PHPDoc tag @param references unknown parameter\: \$userId$#'
			identifier: parameter.notFound
			count: 1
			path: app/Services/ExtracurricularService.php

		-
			message: '#^Relation ''extracurricular'' is not found in App\\Models\\ExtracurricularActivity model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ExtracurricularService.php

		-
			message: '#^Relation ''extracurricular'' is not found in App\\Models\\ExtracurricularAttendance model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ExtracurricularService.php

		-
			message: '#^Relation ''user'' is not found in App\\Models\\ExtracurricularAttendance model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ExtracurricularService.php

		-
			message: '#^Variable \$request on left side of \?\? is never defined\.$#'
			identifier: nullCoalesce.variable
			count: 1
			path: app/Services/ExtracurricularService.php

		-
			message: '#^Relation ''student'' is not found in App\\Models\\ExtracurricularStudent model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/ExtracurricularStudentService.php

		-
			message: '#^Result of method App\\Repositories\\Facility\\FacilityRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/FacilityService.php

		-
			message: '#^Parameter \#1 \$req of method App\\Repositories\\Grade\\GradeRepository\:\:store\(\) expects App\\Http\\Requests\\Grade\\StoreGradeRequest, array\<string, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: app/Services/GradeService.php

		-
			message: '#^Result of method App\\Repositories\\Grade\\GradeRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/GradeService.php

		-
			message: '#^Result of method App\\Repositories\\LessonPlan\\LessonPlanRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/LessonPlanService.php

		-
			message: '#^Method App\\Services\\MidtransService\:\:handleTopUpNfcNotification\(\) invoked with 3 parameters, 2 required\.$#'
			identifier: arguments.count
			count: 1
			path: app/Services/MidtransService.php

		-
			message: '#^Result of method App\\Repositories\\Module\\ModuleRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ModuleService.php

		-
			message: '#^Access to an undefined property App\\Models\\NfcCard\:\:\$user\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Access to an undefined property App\\Models\\NfcCardTransaction\:\:\$nfcCard\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Parameter \#1 \$string of function str_pad expects string, int given\.$#'
			identifier: argument.type
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Property App\\Models\\NfcCardTransaction\:\:\$status \(string\) does not accept App\\Enums\\PaymentStatus\:\:COMPLETED\.$#'
			identifier: assign.propertyType
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Property App\\Models\\NfcCardTransaction\:\:\$status \(string\) does not accept App\\Enums\\PaymentStatus\:\:FAILED\.$#'
			identifier: assign.propertyType
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Relation ''nfcCard'' is not found in App\\Models\\NfcCardTransaction model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Relation ''user'' is not found in App\\Models\\NfcCard model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/NfcCardService.php

		-
			message: '#^Call to method with\(\) on an unknown class App\\Models\\BelongsTo\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/ParentService.php

		-
			message: '#^Relation ''children'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/ParentService.php

		-
			message: '#^Relation ''roles'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ParentService.php

		-
			message: '#^Relation ''schools'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ParentService.php

		-
			message: '#^Relation ''student'' is not found in App\\Models\\PracticalTraining model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/PracticalTrainingService.php

		-
			message: '#^Result of method App\\Repositories\\Product\\ProductRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ProductService.php

		-
			message: '#^Method App\\Repositories\\Program\\ProgramRepository\:\:exportPdf\(\) invoked with 3 parameters, 0 required\.$#'
			identifier: arguments.count
			count: 1
			path: app/Services/ProgramService.php

		-
			message: '#^Result of method App\\Repositories\\Program\\ProgramRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ProgramService.php

		-
			message: '#^Result of method App\\Repositories\\Purchase\\PurchaseRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/PurchaseService.php

		-
			message: '#^Access to an undefined property App\\Models\\User\:\:\$school_id\.$#'
			identifier: property.notFound
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Call to function is_array\(\) with array\<string\> will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Expression on left side of \?\? is not nullable\.$#'
			identifier: nullCoalesce.expr
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Parameter \#1 \$import of static method Maatwebsite\\Excel\\Facades\\Excel\:\:toArray\(\) expects object, null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Relation ''grades'' is not found in App\\Models\\ReportCard model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Relation ''subClassroomSubject'' is not found in App\\Models\\ReportCard model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Using nullsafe property access "\?\-\>academic_year" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Using nullsafe property access on non\-nullable type object\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Services/ReportCardService.php

		-
			message: '#^Relation ''school'' is not found in App\\Models\\Room model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/RoomService.php

		-
			message: '#^Result of method App\\Repositories\\ScheduleActivity\\ScheduleActivityRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/ScheduleActivityService.php

		-
			message: '#^Method App\\Repositories\\Schedule\\ScheduleRepository\:\:getSchedulesByDayAndSubClassroom\(\) invoked with 3 parameters, 2 required\.$#'
			identifier: arguments.count
			count: 1
			path: app/Services/ScheduleService.php

		-
			message: '#^Result of method App\\Repositories\\StudentActivityReport\\StudentActivityReportRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/StudentActivityReportService.php

		-
			message: '#^Result of method App\\Repositories\\StudentDailyActivity\\StudentDailyActivityRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/StudentDailyActivityService.php

		-
			message: '#^Relation ''academicYear'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/StudentService.php

		-
			message: '#^Relation ''parent'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 2
			path: app/Services/StudentService.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/StudentService.php

		-
			message: '#^Result of method App\\Repositories\\SubClassroom\\SubClassroomRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/SubClassroomService.php

		-
			message: '#^Result of method App\\Repositories\\Subject\\SubjectRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/SubjectService.php

		-
			message: '#^Result of method App\\Repositories\\Tag\\TagRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TagService.php

		-
			message: '#^Relation ''subClassroom'' is not found in App\\Models\\User model\.$#'
			identifier: larastan.relationExistence
			count: 1
			path: app/Services/TeacherService.php

		-
			message: '#^Result of method App\\Repositories\\Term\\TermRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TermService.php

		-
			message: '#^Result of method App\\Repositories\\TransactionItem\\TransactionItemRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TransactionItemService.php

		-
			message: '#^Result of method App\\Repositories\\Transaction\\TransactionRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TransactionService.php

		-
			message: '#^Result of method App\\Repositories\\TuitionFee\\TuitionFeeRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TuitionFeeService.php

		-
			message: '#^Result of method App\\Repositories\\TuitionInvoice\\TuitionInvoiceRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TuitionInvoiceService.php

		-
			message: '#^Result of method App\\Repositories\\TuitionPayment\\TuitionPaymentRepository\:\:delete\(\) \(void\) is used\.$#'
			identifier: method.void
			count: 1
			path: app/Services/TuitionPaymentService.php

		-
			message: '#^Method App\\Services\\UserService\:\:store\(\) should return App\\Models\\User but returns App\\Models\\BelongsTo\.$#'
			identifier: return.type
			count: 1
			path: app/Services/UserService.php
