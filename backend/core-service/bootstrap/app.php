<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\CssSelector\Exception\InternalErrorException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->group('auth:api', [
            \PHPOpenSourceSaver\JWTAuth\Http\Middleware\Authenticate::class,
            \App\Http\Middleware\SetActiveSchool::class,
        ]);
        $middleware->alias([
            'role' => \App\Http\Middleware\CheckRole::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // 401: Unauthorized
        $exceptions->render(function (UnauthorizedHttpException $e, Request $request) {
            return response()->json([
                'status' => 401,
                'message' => $e->getMessage(),
            ], 401);
        });

        // 403: Forbidden
        $exceptions->render(function (UnauthorizedException $e, Request $request) {
            return response()->json([
                'status' => 403,
                'message' => 'Forbidden',
            ], 403);
        });

        // 404: Not Found
        $exceptions->render(function (NotFoundHttpException $e, Request $request) {
            return response()->json([
                'status' => 404,
                'message' => $e->getMessage(),
            ], 404);
        });

        // 405: Method Not Allowed
        $exceptions->render(function (MethodNotAllowedHttpException $e, Request $request) {
            return response()->json([
                'status' => 405,
                'message' => $e->getMessage(),
            ], 405);
        });

        // 500: Internal Server Error
        $exceptions->render(function (InternalErrorException $e, Request $request) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage(),
            ], 500);
        });
    })->create();
