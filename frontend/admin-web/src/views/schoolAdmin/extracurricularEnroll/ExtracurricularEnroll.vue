<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import { watch } from 'vue'

import Row from '@/components/common/Row.vue'
import SelectField from '@/components/common/SelectField.vue'
import Table from '@/components/common/Table.vue'
import type { classExtracurricularEnroll } from '@/types/classExtracurricularEnroll'

import { useExtracurricularViewModel } from './useExtracurricularEnrollViewModel'

interface Props {
  termId: number
  classes?: Array<classExtracurricularEnroll>
}
const props = withDefaults(defineProps<Props>(), { classes: () => [] })

const vm = useExtracurricularViewModel()
const state = vm.state

const classOptions = vm.effectiveClassOptions

watch(
  () => [props.termId, props.classes] as const,
  async ([termId, classes]) => {
    await vm.init({ termId, classes })
  },
  { immediate: true, deep: true },
)

const predicateOptions = [
  { label: 'SB: Sangat Baik', value: 'SB' },
  { label: 'B: Baik', value: 'B' },
  { label: 'C: Cukup', value: 'C' },
  { label: 'K: Kurang', value: 'K' },
]
</script>

<template>
  <div class="space-y-4">
    <Row class="gap-4">
      <SelectField
        v-model="state.selectedTermId"
        :options="state.termOptions"
        label="Semester/Tahun"
        name="termId"
        option-label="label"
        option-value="value"
        placeholder="Pilih Semester/Tahun"
        filter
        required
      />
      <Button label="Daftarkan Siswa ke Ekskul" icon="pi pi-user-plus" @click="vm.openEnroll" />
    </Row>

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.rows"
      :fetch="vm.fetchEnrollments"
      :total-records="state.totalRecords"
      :has-view="false"
      :has-delete="true"
      @delete="vm.removeRow($event.id)"
    >
      <template #action="{ data }">
        <Button label="Nilai" icon="pi pi-star" severity="help" @click="vm.openScore(data)" />
      </template>
    </Table>

    <!-- Dialog daftar -->
    <Dialog
      v-model:visible="state.showEnroll"
      header="Daftarkan Siswa"
      modal
      :style="{ width: '520px' }"
    >
      <div class="flex flex-col gap-3">
        <div>
          <label class="block mb-1">Kelas</label>
          <Dropdown
            v-model="state.selectedClassId"
            :options="classOptions"
            optionLabel="name"
            optionValue="id"
            placeholder="Pilih kelas"
            class="w-full"
          />
        </div>

        <div>
          <label class="block mb-1">Nama Siswa</label>
          <Dropdown
            v-model="state.selectedStudentId"
            :options="state.studentsOptions"
            optionLabel="text"
            optionValue="id"
            placeholder="Pilih siswa (Nama - NISN)"
            class="w-full"
            :disabled="!state.selectedClassId"
            filter
          />
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <Button label="Batal" severity="secondary" @click="state.showEnroll = false" />
          <Button label="Daftarkan" icon="pi pi-check" @click="vm.submitEnroll" />
        </div>
      </div>
    </Dialog>

    <!-- Dialog nilai -->
    <Dialog
      v-model:visible="state.showScore"
      header="Nilai Perkembangan Ekskul"
      modal
      :style="{ width: '520px' }"
    >
      <div class="flex flex-col gap-3">
        <div>
          <label class="block mb-1">Predikat</label>
          <Dropdown
            v-model="state.predicate"
            :options="predicateOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Pilih predikat"
            class="w-full"
          />
        </div>
        <div class="flex justify-end gap-2 mt-2">
          <Button label="Batal" severity="secondary" @click="state.showScore = false" />
          <Button label="Simpan" icon="pi pi-save" @click="vm.saveScore" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
