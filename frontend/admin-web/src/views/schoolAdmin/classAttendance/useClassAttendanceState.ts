import { reactive } from 'vue'

import type { ClassAttendanceRow } from '@/types/classAttendance.type'
import type { Term } from '@/types/term.type'

export const useClassAttendanceState = () => {
  const state = reactive({
    // props-bound (diisi dari komponen)
    terms: [] as Term[],
    termOptions: [] as Array<{ label: string; value: number }>,
    selectedTermId: undefined as number | undefined,

    // ui
    loading: false,
    showImport: false,
    importFile: null as File | null,

    // data
    rows: [] as ClassAttendanceRow[],
  })
  return { state }
}
