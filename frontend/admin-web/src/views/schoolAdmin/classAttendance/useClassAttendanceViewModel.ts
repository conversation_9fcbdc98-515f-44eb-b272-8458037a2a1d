import { useToast } from 'primevue/usetoast'

import ClassAttendanceService from '@/services/classAttendance.service'
import { useAuthStore } from '@/stores/auth'
import { getTerms } from '@/utils/term.utils'
import { showToast } from '@/utils/toast.util'

import { useClassAttendanceState } from './useClassAttendanceState'

export const useClassAttendanceViewModel = () => {
  const { state } = useClassAttendanceState()
  const toast = useToast()
  const auth = useAuthStore()

  // ===== Terms =====
  const loadTerms = async () => {
    const { terms, termOptions, currentTerm } = await getTerms()

    state.terms = terms
    state.termOptions = termOptions
    if (!state.selectedTermId) {
      state.selectedTermId = currentTerm?.id ?? terms[0]?.id ?? null
    }
  }

  // ===== Roster + Attendance by term & subClassroom =====
  const loadRoster = async () => {
    if (!state.selectedTermId) return
    const subClassroomId = auth.getUser?.subClassroom?.id
    state.loading = true
    try {
      const { data } = await ClassAttendanceService.list(state.selectedTermId, subClassroomId)

      state.rows = data?.data ?? []
    } catch (e) {
      console.error(e)
      showToast(toast, 'error', 'Gagal mengambil daftar siswa')
    } finally {
      state.loading = false
    }
  }

  // ===== Adjust (+/-), Upsert total, Bulk append =====
  const upsertRow = async (row: any) => {
    const res = await ClassAttendanceService.upsert({
      term_id: row.termId,
      student_user_id: row.studentUserId,
      sick: Math.max(0, row.sick),
      leave: Math.max(0, row.leave),
      present: Math.max(0, row.present),
      alpha: Math.max(0, row.alpha),
    })
    row.id = res?.data?.data?.id ?? res?.data?.id ?? row.id
  }

  const adjust = async (row: any, field: 'sick' | 'leave' | 'present' | 'alpha', delta: number) => {
    if (!row.id) await upsertRow(row)
    const res = await ClassAttendanceService.adjust(row.id, field, delta)
    const payload = res?.data?.data ?? res?.data
    if (payload) {
      row.sick = payload.sick
      row.leave = payload.leave
      row.present = payload.present
      row.alpha = payload.alpha
      row.id = payload.id
    }
  }

  const saveRow = async (row: any) => {
    await upsertRow(row)
  }

  const bulkAppend = async () => {
    await ClassAttendanceService.bulkAppend(
      state.rows.map((r) => ({
        term_id: r.termId,
        student_user_id: r.studentUserId,
        sick: r.sick,
        leave: r.leave,
        present: r.present,
        alpha: r.alpha,
      })),
    )
    showToast(toast, 'success', 'Bulk append success')
    await loadRoster()
  }

  const downloadTemplate = async () => {
    const res = await ClassAttendanceService.downloadTemplate(
      state.selectedTermId!,
      auth.getUser?.subClassroom?.id ?? 0,
    )
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `attendance_template_term_${state.selectedTermId}.xlsx`
    a.click()
    URL.revokeObjectURL(url)
  }

  const importExcel = async (file: File) => {
    await ClassAttendanceService.importExcel(file)
    showToast(toast, 'success', 'Imported')
    await loadRoster()
  }

  // ===== Lifecycle orchestration (dipanggil komponen) =====
  const initPage = async () => {
    await loadTerms()
    await loadRoster()
  }

  const onTermChanged = async () => {
    if (!state.selectedTermId) return
    await loadRoster()
  }

  return {
    state,
    adjust,
    saveRow,
    bulkAppend,
    downloadTemplate,
    importExcel,
    initPage,
    onTermChanged,
  }
}
