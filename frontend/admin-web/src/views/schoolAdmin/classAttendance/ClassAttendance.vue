<script setup lang="ts">
import Button from 'primevue/button'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import InputNumber from 'primevue/inputnumber'
import { ref, onMounted, watch } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Row from '@/components/common/Row.vue'
import SelectField from '@/components/common/SelectField.vue'
import Table from '@/components/common/Table.vue'

import { useClassAttendanceViewModel } from './useClassAttendanceViewModel'

const vm = useClassAttendanceViewModel()
const state = vm.state

const showImport = ref(false)
const importFile = ref<File | null>(null)

watch(
  () => state.selectedTermId,
  async () => {
    await vm.onTermChanged()
  },
)

onMounted(async () => {
  await vm.initPage()
})

const doImport = async () => {
  if (!importFile.value) return
  await vm.importExcel(importFile.value)
  showImport.value = false
  importFile.value = null
}
</script>

<template>
  <AdminSection title="Absensi Kelas">
    <Row class="gap-4">
      <SelectField
        v-model="state.selectedTermId"
        :options="state.termOptions"
        label="Semester/Tahun"
        name="termId"
        option-label="label"
        option-value="value"
        placeholder="Pilih Semester/Tahun"
        filter
        required
      />
    </Row>

    <div class="flex gap-2 my-3">
      <Button label="Download Template" @click="vm.downloadTemplate" />
      <Button label="Import Excel" severity="secondary" @click="showImport = true" />
      <Button label="Bulk Append" severity="success" @click="vm.bulkAppend" />
    </div>

    <div v-if="state.selectedTermId">
      <Table :data="state.rows" :loading="state.loading" :paginator="false">
        <Column field="studentName" header="Nama Siswa" frozen class="bg-white z-50" />

        <Column header="Hadir">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <Button icon="pi pi-minus" text @click="vm.adjust(data, 'present', -1)" />
              <InputNumber v-model="data.present" :min="0" @blur="vm.saveRow(data)" />
              <Button icon="pi pi-plus" text @click="vm.adjust(data, 'present', 1)" />
            </div>
          </template>
        </Column>

        <Column header="Sakit">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <Button icon="pi pi-minus" text @click="vm.adjust(data, 'sick', -1)" />
              <InputNumber v-model="data.sick" :min="0" @blur="vm.saveRow(data)" />
              <Button icon="pi pi-plus" text @click="vm.adjust(data, 'sick', 1)" />
            </div>
          </template>
        </Column>

        <Column header="Izin">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <Button icon="pi pi-minus" text @click="vm.adjust(data, 'leave', -1)" />
              <InputNumber v-model="data.leave" :min="0" @blur="vm.saveRow(data)" />
              <Button icon="pi pi-plus" text @click="vm.adjust(data, 'leave', 1)" />
            </div>
          </template>
        </Column>

        <Column header="Tidak Hadir">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <Button icon="pi pi-minus" text @click="vm.adjust(data, 'alpha', -1)" />
              <InputNumber v-model="data.alpha" :min="0" @blur="vm.saveRow(data)" />
              <Button icon="pi pi-plus" text @click="vm.adjust(data, 'alpha', 1)" />
            </div>
          </template>
        </Column>
      </Table>
    </div>

    <Dialog
      v-model:visible="showImport"
      header="Import Absensi"
      :modal="true"
      :style="{ width: '30rem' }"
    >
      <div class="space-y-3">
        <input
          type="file"
          accept=".xlsx,.xls"
          @change="(e) => (importFile = (e.target as HTMLInputElement).files?.[0] ?? null)"
        />
        <div class="flex justify-end gap-2">
          <Button label="Batal" severity="secondary" @click="showImport = false" />
          <Button label="Import" severity="success" @click="doImport" :disabled="!importFile" />
        </div>
      </div>
    </Dialog>
  </AdminSection>
</template>

<style scoped>
/* optional */
</style>
