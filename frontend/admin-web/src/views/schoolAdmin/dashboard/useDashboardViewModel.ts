import { useToast } from 'primevue'

import AttendanceServices from '@/services/attendance.service'
import StudentServices from '@/services/student.service'
import SubClassroomServices from '@/services/subClassroom.service'
import TeacherServices from '@/services/teacher.service'
import UserServices from '@/services/user.service'
import type { QueryParams } from '@/types/queryParams.type'
import { showToast } from '@/utils/toast.util'

import { useDashboardState } from './useDashboardState'

export const useDashboardViewModel = () => {
  const toast = useToast()
  const state = useDashboardState()

  const getSummaryStudents = async ({ page }: { page: number }) => {
    return AttendanceServices.getAttendanceSummaryStudents({ page })
  }

  const getSummaryTeachers = async ({ page }: { page: number }) => {
    return AttendanceServices.getAttendanceSummaryTeachers({ page })
  }

  const getAllStudents = async () => {
    try {
      const { data } = await StudentServices.getCount()
      state.studentCount = data.data
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data siswa')
    }
  }

  const getAllTeachers = async () => {
    try {
      const { data } = await TeacherServices.getCount()
      state.teacherCount = data.data
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data guru')
    }
  }

  const getAllClassrooms = async () => {
    try {
      const { data } = await SubClassroomServices.getCount()
      state.classroomCount = data.data
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data kelas')
    }
  }

  const getInactiveUsers = async (params?: QueryParams) => {
    try {
      const { data } = await UserServices.getInactiveUsers(params)
      state.inactiveUsers = data.data
      state.totalRecords = data.totalRecords
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data pengguna tidak aktif')
    }
  }

  const approveUser = async (id: number) => {
    try {
      await UserServices.activateUser(id)
      resetState()
      showToast(toast, 'success', 'Pengguna berhasil diaktifkan')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengaktifkan pengguna')
    }
  }

  const resetState = () => {
    state.showActivateDialog = false
    state.selectedUser = undefined
    state.tableKey++
  }

  getSummaryStudents({ page: 1 })
  getSummaryTeachers({ page: 1 })
  getAllStudents()
  getAllTeachers()
  getAllClassrooms()

  return {
    state: state,
    getSummaryStudents,
    getSummaryTeachers,
    getAllStudents,
    getAllTeachers,
    getAllClassrooms,
    getInactiveUsers,
    approveUser,
    resetState,
  }
}
