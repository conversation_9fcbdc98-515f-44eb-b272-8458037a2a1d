<script setup lang="ts">
import { Button } from 'primevue'
import { onMounted } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import DialogForm from '@/components/common/DialogForm.vue'
import Table from '@/components/common/Table.vue'
import TextField from '@/components/common/TextField.vue'

import { useCategoryViewModel } from './useCategoryViewModel'

const viewModel = useCategoryViewModel()
const state = viewModel.state

const onAddClick = () => {
  state.showDialog = true
}

const onCloseDialog = () => {
  viewModel.closeDialog()
}

const onSubmit = () => {
  if (state.selectedData) {
    viewModel.updateData(state.selectedData)
  } else {
    viewModel.addData(state.formData)
  }
}

onMounted(() => {
  viewModel.getAllCategory()
})
</script>

<template>
  <AdminSection title="Kategori">
    <Button @click="onAddClick" label="Tambah" icon="pi pi-plus" class="bg-green-500 w-min" />
    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.categoriesData"
      :fetch="viewModel.getAllCategory"
      has-delete
      has-edit
      @delete="viewModel.deleteData($event)"
      @edit="viewModel.showEditDialog($event)"
    />
    <DialogForm
      :visible="state.showDialog"
      @close="onCloseDialog"
      @submit="onSubmit"
      title="Tambah Kategori"
    >
      <TextField v-model="state.formData" label="Nama Kategori" name="category" />
    </DialogForm>
  </AdminSection>
</template>
