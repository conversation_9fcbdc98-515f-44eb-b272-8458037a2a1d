import { useToast } from 'primevue/usetoast'

import type { FacilitiesRequestBody } from '@/dto/facilitiesRequestBody'
import FacilitiesServices from '@/services/facilities.service'
import type { Facilities } from '@/types/facilities.type'
import type { QueryParams } from '@/types/queryParams.type'
import { showToast } from '@/utils/toast.util'

import { useFacilitieState } from './useFacilitieState'

export const useFacilitieViewModel = () => {
  const toast = useToast()
  const state = useFacilitieState()

  const showAddDialog = () => {
    state.selectedData = undefined
    state.formType = 'add'
    state.showDialog = true
  }

  const showEditDialog = (data: Facilities) => {
    state.selectedData = data
    state.formType = 'edit'
    state.showDialog = true
  }

  const closeDialog = () => {
    state.selectedData = undefined
    state.showDialog = false
  }

  const addData = async (data: FacilitiesRequestBody) => {
    try {
      const form = new FormData()
      form.append('name', data.name || '')
      form.append('unit', data.unit || '')
      form.append('good_condition', String(data.goodCondition || 0))
      form.append('minor_damage', String(data.minorDamage || 0))
      form.append('major_damage', String(data.majorDamage || 0))

      if (data.image instanceof File) {
        form.append('image', data.image)
      }

      await FacilitiesServices.createFacility(form)

      state.tableKey++
      state.showDialog = false
      showToast(toast, 'success', 'Data fasilitas berhasil ditambahkan')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal menambahkan data fasilitas')
    }
  }

  const updateData = async (id: number, data: FacilitiesRequestBody) => {
    try {
      const form = new FormData()
      form.append('name', data.name || '')
      form.append('unit', data.unit || '')
      form.append('good_condition', String(data.goodCondition || 0))
      form.append('minor_damage', String(data.minorDamage || 0))
      form.append('major_damage', String(data.majorDamage || 0))

      if (data.image instanceof File) {
        form.append('image', data.image)
      }

      await FacilitiesServices.updateFacility(id, form)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Fasilitas berhasil diperbarui')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Fasilitas gagal diperbarui')
    }
  }

  const deleteData = async (id: number) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus data ini?')) {
      try {
        await FacilitiesServices.deleteFacility(id)
        state.tableKey++
        showToast(toast, 'success', 'Data fasilitas berhasil dihapus')
      } catch (error) {
        console.error(error)
        showToast(toast, 'error', 'Gagal menghapus data fasilitas')
      }
    }
  }

  const getAllFacility = async (params?: QueryParams) => {
    try {
      const { data } = await FacilitiesServices.getAllFacilities(params)
      state.facilitiesData = data.data
      state.totalRecords = data.totalRecords
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data fasilitas')
    }
  }

  const showImportDialog = () => {
    state.formType = 'import'
    state.selectedData = undefined
    state.showDialog = true
  }

  const importDataFacilities = async (file: File) => {
    try {
      await FacilitiesServices.importFacility(file)
      state.showDialog = false
      state.tableKey++
      
      // Fetch updated data after successful import
      await getAllFacility()

      showToast(toast, 'success', 'Data fasilitas berhasil diimport')
    } catch (error: any) {
      console.error(error)
      showToast(toast, 'error', error.response?.data?.message || 'Gagal import data fasilitas')
    }
  }

  const exportDataFacilities = async () => {
    try {
      const res = await FacilitiesServices.exportFacility()
      const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `fasilitas_${new Date().toISOString().split('T')[0]}.xlsx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      showToast(toast, 'success', 'Data fasilitas berhasil diexport')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal export data fasilitas')
    }
  }

  const exportTemplateFacilities = async () => {
    try {
      const res = await FacilitiesServices.getImportTemplate();
      const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'template_import_fasilitas.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      showToast(toast, 'success', 'Berhasil download template data fasilitas');
    } catch (error) {
      console.error(error);
      showToast(toast, 'error', 'Gagal download template data fasilitas');
    }
  }

  return {
    state,
    showAddDialog,
    showEditDialog,
    closeDialog,
    addData,
    updateData,
    deleteData,
    getAllFacility,
    showImportDialog,
    importDataFacilities,
    exportDataFacilities,
    exportTemplateFacilities
  }
}
