<script setup lang="ts">
import { Column, useToast } from 'primevue'
import { ref } from 'vue'

import Dialog from '@/components/common/Dialog.vue'
import Table from '@/components/common/Table.vue'
import ClassroomServices from '@/services/classroom.service'
import SubClassroomServices from '@/services/subClassroom.service'
import type { Classroom, SubClassroom } from '@/types/classroom.type'
import { showToast } from '@/utils/toast.util'

import AssignStudentDialogForm from './AssignStudentDialogForm.vue'
import SequenceDialog from './subClassroom/SequenceDialog.vue'
import SubClassroomDetailDialog from './subClassroom/SubClassroomDetailDialog.vue'

interface Props {
  showDialog: boolean
  classroom?: Classroom
}

interface Emits {
  close: []
}

defineEmits<Emits>()

const props = defineProps<Props>()

const toast = useToast()

const tableKey = ref<number>(0)

const subClassroomsData = ref<SubClassroom[]>([])

const showSubClassroomDetailDialog = ref<boolean>(false)

const showAssignStudentDialog = ref<boolean>(false)

const showEditSequenceDialog = ref<boolean>(false)

const selectedSubClassroom = ref<SubClassroom>()

const getSubClassrooms = async () => {
  try {
    const { data } = await ClassroomServices.getClassroomById(props.classroom?.id as number)
    subClassroomsData.value = data.data.subClassrooms
    console.log(subClassroomsData.value)
  } catch (error) {
    console.error(error)
  }
}

const assignStudent = async (subClassroomId: number, studentUserIds: number[]) => {
  try {
    await SubClassroomServices.assignStudentToSubClassroom(subClassroomId, studentUserIds)
  } catch (error) {
    console.error(error)
    showToast(toast, 'error', 'Data siswa gagal diupdate')
  }
}

const assignTeacher = async (subClassroomId: number, teacherUserId: number) => {
  try {
    await SubClassroomServices.assignTeacher(subClassroomId, teacherUserId)
    tableKey.value++
  } catch (error) {
    console.error(error)
    showToast(toast, 'error', 'Wali kelas gagal diupdate')
  }
}

const updateData = (subClassroomId: number, studentUserIds: number[], teacherUserId?: number) => {
  assignStudent(subClassroomId, studentUserIds)
  if (teacherUserId && teacherUserId !== 0) {
    assignTeacher(subClassroomId, teacherUserId)
  }
  showAssignStudentDialog.value = false
  tableKey.value++
}

// Fungsi untuk mengedit sequence
const editSequence = (subClassroom: SubClassroom) => {
  selectedSubClassroom.value = subClassroom
  showEditSequenceDialog.value = true // Menampilkan dialog sequence
}
</script>

<template>
  <!-- Dialog Tambah Data -->
  <Dialog :visible="showDialog" @close="$emit('close')" :title="classroom?.name!" size="large">
    <Table
      :key="tableKey"
      :data="subClassroomsData"
      :fetch="getSubClassrooms"
      has-view
      @view="((showSubClassroomDetailDialog = true), (selectedSubClassroom = $event))"
      has-edit
      @edit="((showAssignStudentDialog = true), (selectedSubClassroom = $event))"
    >
      <Column header="Nama" field="name">
        <template #body="{ data }">
          <a @click="editSequence(data)" class="cursor-pointer hover:text-blue-500">{{
            data.name
          }}</a>
        </template>
      </Column>
      <Column header="Wali Kelas" field="teacher">
        <template #body="{ data }">
          {{ data.teacher?.name ?? '-' }}
        </template>
      </Column>
    </Table>
  </Dialog>

  <SubClassroomDetailDialog
    :show-dialog="showSubClassroomDetailDialog"
    :sub-classroom="selectedSubClassroom"
    @close="((showSubClassroomDetailDialog = false), (selectedSubClassroom = undefined))"
  />

  <AssignStudentDialogForm
    :show-dialog="showAssignStudentDialog"
    :sub-classroom="selectedSubClassroom"
    @submit="
      updateData(selectedSubClassroom?.id as number, $event.selectedIds, $event.teacherUserId)
    "
    @close="((showAssignStudentDialog = false), (selectedSubClassroom = undefined))"
  />
  <!-- Dialog untuk Edit Sequence -->
  <SequenceDialog
    :show-dialog="showEditSequenceDialog"
    :sub-classroom="selectedSubClassroom"
    :table-key="tableKey"
    @close="((showEditSequenceDialog = false), (selectedSubClassroom = undefined))"
    @refresh="tableKey++"
  />
</template>
