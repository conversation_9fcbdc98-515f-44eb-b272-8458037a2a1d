<script setup lang="ts">
import { Button } from 'primevue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { ClassroomRequestBody } from '@/dto/classroomRequestBody'

import ClassroomDetailDialog from './ClassroomDetailDialog.vue'
import ClassroomDialogForm from './ClassroomDialogForm.vue'
import { useClassroomDataViewModel } from './useClassroomDataViewModel'

const viewModel = useClassroomDataViewModel()
const state = viewModel.state

const onAddClick = () => {
  viewModel.showAddDialog()
}

const onCloseDialog = () => {
  viewModel.closeDialog()
}

const onSubmit = (data: ClassroomRequestBody) => {
  if (state.formType === 'add') {
    viewModel.addData(data)
  } else {
    viewModel.updateData(state.selectedData?.id as number, data)
  }
}
</script>

<template>
  <AdminSection title="Data Kelas">
    <Button label="Tambah" icon="pi pi-plus" @click="onAddClick" class="w-min" />
    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.classroomsData"
      :fetch="viewModel.getAllClassroom"
      :total-records="state.totalRecords"
      has-view
      has-delete
      has-edit
      @view="viewModel.showViewDialog"
      @edit="viewModel.showEditDialog"
      @delete="viewModel.deleteData($event.id)"
    />

    <ClassroomDialogForm
      :classroom="state.selectedData"
      :show-dialog="state.showDialog"
      :type="state.formType"
      @close="onCloseDialog"
      @submit="onSubmit"
    />

    <ClassroomDetailDialog
      :show-dialog="state.showDetailDialog"
      :classroom="state.selectedData"
      @close="onCloseDialog"
    />
  </AdminSection>
</template>
