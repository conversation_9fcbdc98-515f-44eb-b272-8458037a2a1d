<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b } from 'primevue'
import { onUpdated, ref } from 'vue'

import Dialog from '@/components/common/Dialog.vue'
import Table from '@/components/common/Table.vue'
import SubClassroomServices from '@/services/subClassroom.service'
import type { SubClassroom } from '@/types/classroom.type'
import type { QueryParams } from '@/types/queryParams.type'
import type { SubClassroomSubject } from '@/types/subClassroomSubject.type'
import type { User } from '@/types/user.type'

import AssignSubjectTeacherDialogForm from './AssignSubjectTeacherDialogForm.vue'

interface Props {
  showDialog: boolean
  subClassroom?: SubClassroom
}

interface Emits {
  close: []
}

defineEmits<Emits>()
const props = defineProps<Props>()

const students = ref<User[]>([])

const subjects = ref<SubClassroomSubject[]>([])

const selectedSubject = ref<SubClassroomSubject>()

const showAssignSubjectTeacherDialog = ref<boolean>(false)

const tableKey = ref<number>(0)

const subjectTotalRecords = ref<number>(0)

const studentTotalRecords = ref<number>(0)

const getStudents = async (params?: QueryParams) => {
  try {
    const { data } = await SubClassroomServices.getStudentsBySubClassroomId(
      props.subClassroom?.id as number,
      params,
    )
    students.value = data.data
    studentTotalRecords.value = data.totalRecords
  } catch (error) {
    console.error(error)
  }
}

const getSubjects = async () => {
  try {
    const { data } = await SubClassroomServices.getSubjectsBySubClassroomId(
      props.subClassroom?.id as number,
    )
    subjects.value = data.data
    subjectTotalRecords.value = data.totalRecords
  } catch (error) {
    console.error(error)
  }
}

const updateData = async (subClassroomId: number, teacherUserId: number, subjectId: number) => {
  try {
    await SubClassroomServices.assignSubjectTeacher(subClassroomId, teacherUserId, subjectId)
    tableKey.value++
    showAssignSubjectTeacherDialog.value = false
  } catch (error) {
    console.error(error)
  }
}

onUpdated(() => {
  if (props.showDialog && props.subClassroom && !showAssignSubjectTeacherDialog.value) {
    getSubjects()
  }
})
</script>

<template>
  <!-- Dialog Tambah Data -->
  <Dialog
    :visible="showDialog"
    @close="$emit('close')"
    :title="subClassroom?.name as string"
    size="large"
  >
    <Tabs value="0">
      <TabList>
        <Tab value="0">Siswa</Tab>
        <Tab value="1">Mata Pelajaran</Tab>
      </TabList>
      <TabPanels>
        <TabPanel value="0">
          <Table :data="students" :total-records="studentTotalRecords" :fetch="getStudents">
            <Column field="registrationNumber" header="NISN">
              <template #body="{ data }">
                {{ data.registrationNumber ?? '-' }}
              </template>
            </Column>
            <Column field="name" header="Nama" />
          </Table>
        </TabPanel>
        <TabPanel value="1">
          <Table
            :key="tableKey"
            :data="subjects"
            :total-records="subjectTotalRecords"
            :fetch="getSubjects"
            :paginator="false"
            has-edit
            @edit="((showAssignSubjectTeacherDialog = true), (selectedSubject = $event))"
          >
            <Column field="name" header="Nama">
              <template #body="{ data }">
                {{ data.subject.name ?? '-' }}
              </template>
            </Column>
            <Column field="teacher" header="Guru">
              <template #body="{ data }">
                {{ data.teacher?.name ?? '-' }}
              </template>
            </Column>
          </Table>
        </TabPanel>
      </TabPanels>
    </Tabs>
  </Dialog>

  <!-- Dialog Assign Guru ke Mata Pelajaran -->
  <AssignSubjectTeacherDialogForm
    :showDialog="showAssignSubjectTeacherDialog"
    :subject="selectedSubject"
    @close="showAssignSubjectTeacherDialog = false"
    @submit="updateData(subClassroom?.id as number, $event.teacherUserId, $event.subjectId)"
  />
</template>
