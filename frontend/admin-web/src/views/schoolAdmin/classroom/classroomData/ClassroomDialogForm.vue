<script setup lang="ts">
import { watch, ref, nextTick } from 'vue'

import type { DialogFormType } from '@/components/common/DialogForm.vue'
import DialogForm from '@/components/common/DialogForm.vue'
import MultiSelect from '@/components/common/MultiSelect.vue'
import TextField from '@/components/common/TextField.vue'
import type { ClassroomRequestBody } from '@/dto/classroomRequestBody'
import ClassroomServices from '@/services/classroom.service'
import SubjectServices from '@/services/subject.service'
import type { Classroom } from '@/types/classroom.type'
import type { Subject } from '@/types/subject.type'

interface Props {
  type: DialogFormType
  showDialog: boolean
  classroom?: Classroom // data dari list (mungkin TANPA subject_ids)
}
interface Emits {
  submit: [data: ClassroomRequestBody]
  close: []
}
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const subjectOptions = ref<Subject[]>([])
const formData = ref<ClassroomRequestBody>({
  name: '',
  subClassroomCount: '',
  subjectIds: [],
})

async function getAllSubjects() {
  const { data } = await SubjectServices.getAllSubjects()
  subjectOptions.value = (data?.data ?? []).map((s: any) => ({
    ...s,
    id: Number(s.id),
  }))
}

async function getClassroomDetail(id: number) {
  const { data } = await ClassroomServices.getClassroomById(id)
  return data?.data ?? {}
}

async function openAndPrefill() {
  if (!props.showDialog) return
  await getAllSubjects()
  if (props.type === 'edit' && props.classroom) {
    let detail: any = {}
    try {
      detail = await getClassroomDetail(props.classroom.id)
    } catch (e) {
      console.error('fetch classroom detail error', e)
    }

    const raw = detail.subject_ids ?? detail.subjectIds ?? []
    const preselected: number[] = (raw as Array<number | string>).map((v) => Number(v))

    formData.value = {
      name: detail.name ?? props.classroom.name ?? '',
      subClassroomCount: String(
        detail.sub_classrooms?.length ?? props.classroom.subClassrooms?.length ?? '',
      ),
      subjectIds: preselected,
    }

    await nextTick()
    console.log('MultiSelect opts[0]:', subjectOptions.value?.[0])
    console.log('Preselected subjectIds:', formData.value.subjectIds)
  } else {
    formData.value = { name: '', subClassroomCount: '', subjectIds: [] }
  }
}

watch(
  () => props.showDialog,
  async (visible) => {
    if (visible) await openAndPrefill()
  },
)

watch(
  () => props.classroom?.id,
  async (id) => {
    if (props.showDialog && id) await openAndPrefill()
  },
)

const onSubmit = () => {
  emit('submit', { ...formData.value })
}
</script>

<template>
  <DialogForm
    :visible="showDialog"
    @close="emit('close')"
    @submit="onSubmit"
    :title="props.type === 'add' ? 'Tambah Kelas' : 'Edit Kelas'"
  >
    <TextField v-model="formData.name" label="Nama Kelas" name="name" />
    <TextField
      v-model="formData.subClassroomCount"
      label="Jumlah Kelas"
      name="sub_classroom_count"
      type="number"
    />
    <MultiSelect
      v-model="formData.subjectIds"
      :options="subjectOptions"
      display="chip"
      label="Pilih Mata Pelajaran"
      name="subjectIds"
      placeholder="Pilih Mata Pelajaran"
      option-label="name"
      option-value="id"
      filter
    />
  </DialogForm>
</template>
