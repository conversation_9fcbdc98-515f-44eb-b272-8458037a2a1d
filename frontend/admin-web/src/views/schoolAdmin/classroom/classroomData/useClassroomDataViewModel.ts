import { useToast } from 'primevue/usetoast'

import type { ClassroomRequestBody } from '@/dto/classroomRequestBody'
import ClassroomServices from '@/services/classroom.service'
import type { QueryParams } from '@/types/queryParams.type'
import { showToast } from '@/utils/toast.util'

import { useClassroomDataState } from './useClassroomDataState'

export const useClassroomDataViewModel = () => {
  const toast = useToast()
  const state = useClassroomDataState()

  const showAddDialog = () => {
    state.selectedData = undefined
    state.formType = 'add'
    state.showDialog = true
  }

  const showEditDialog = (data: any) => {
    state.formType = 'edit'
    state.selectedData = data
    state.showDialog = true
  }

  const showViewDialog = (data: any) => {
    state.selectedData = data
    state.showDetailDialog = true
  }

  const closeDialog = () => {
    state.selectedData = undefined
    state.showDialog = false
    state.showDetailDialog = false
  }

  const addData = async (body: ClassroomRequestBody) => {
    try {
      await ClassroomServices.createClassroom(body)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Data kelas berhasil ditambahkan')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal menambahkan data kelas')
    }
  }

  const updateData = async (id: number, body: ClassroomRequestBody) => {
    try {
      await ClassroomServices.updateClassroom(id, body)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Data kelas berhasil diperbarui')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal memperbarui data kelas')
    }
  }

  const deleteData = async (id: number) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus data ini?')) {
      try {
        await ClassroomServices.deleteClassroom(id)
        state.tableKey++
        showToast(toast, 'success', 'Data kelas berhasil dihapus')
      } catch (error) {
        console.error(error)
        showToast(toast, 'error', 'Gagal menghapus data kelas')
      }
    }
  }

  const getAllClassroom = async (params?: QueryParams) => {
    try {
      const { data } = await ClassroomServices.getAllClassrooms(params)
      state.classroomsData = data.data
      state.totalRecords = data.totalRecords
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Gagal mengambil data kelas')
    }
  }

  return {
    state,
    showAddDialog,
    showEditDialog,
    showViewDialog,
    closeDialog,
    addData,
    updateData,
    deleteData,
    getAllClassroom,
  }
}
