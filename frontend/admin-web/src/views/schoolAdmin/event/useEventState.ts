import { reactive } from 'vue'

import type { DialogFormType } from '@/components/common/DialogForm.vue'
import type { TableColumn } from '@/components/common/Table.vue'
import type { Event } from '@/types/event.type'
import { formatDateOnly, formatDateTimeNoSeconds } from '@/utils/date.util'
import { getPlaceholderImageUrl } from '@/utils/image.util'

export const useEventState = () => {
  const state = reactive({
    tableKey: 0,
    showDialog: false,
    showEventFile: false,
    selectedThumbnail: null as string | null,
    formType: 'add' as DialogFormType,
    selectedData: undefined as Event | undefined,
    totalRecords: 0,
    eventsData: [] as Event[],
    columns: [
      {
        header: 'Judul',
        field: 'title',
      },
      {
        header: 'Ringkasan',
        field: 'excerpt',
      },
      {
        header: 'Lokasi',
        field: 'location',
      },
      {
        header: 'Tanggal Acara',
        field: 'eventDate',
        bodyComponent: (data: Event) => ({
          component: 'span',
          props: {
            innerHTML: formatDateOnly(data.eventDate),
          },
        }),
      },
      {
        header: 'Waktu <PERSON>',
        field: 'startTime',
        bodyComponent: (data: Event) => ({
          component: 'span',
          props: {
            innerHTML: formatDateTimeNoSeconds(data.startTime),
          },
        }),
      },
      {
        header: 'Waktu Akhir Acara',
        field: 'endTime',
        bodyComponent: (data: Event) => ({
          component: 'span',
          props: {
            innerHTML: formatDateTimeNoSeconds(data.endTime),
          },
        }),
      },
      {
        header: 'Publikasi',
        field: 'isPublished',
        bodyComponent: (data: Event) => {
          const publish = data.isPublished ? 'Ya' : 'Tidak'
          return {
            component: 'span',
            props: {
              class: 'text-black',
              innerHTML: publish,
            },
          }
        },
      },
      {
        header: 'Thumbnail',
        field: 'thumbnailUrl',
        bodyComponent: (data: Event) => {
          const thumbnailUrl = data.thumbnailUrl ? data.thumbnailUrl : getPlaceholderImageUrl()

          return {
            component: 'img',
            props: {
              src: thumbnailUrl,
              alt: data.title,
              class: 'w-32 h-32 object-cover rounded cursor-pointer',
              onClick: () => {
                console.log('thumbnail clicked:', thumbnailUrl)
                state.selectedThumbnail = thumbnailUrl
              },
            },
          }
        },
      },
    ] as TableColumn[],
  })

  return state
}
