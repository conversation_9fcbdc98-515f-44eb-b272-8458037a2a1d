<script setup lang="ts">
import { DatePicker, FileUpload, ToggleSwitch } from 'primevue'
import { computed, ref, watch } from 'vue'

import DialogForm, { type DialogFormType } from '@/components/common/DialogForm.vue'
import InfoText from '@/components/common/InfoText.vue'
import InputFile from '@/components/common/InputFile.vue'
import TextArea from '@/components/common/TextArea.vue'
import TextField from '@/components/common/TextField.vue'
import type { StoreEventRequestBody, UpdateEventRequestBody } from '@/dto/eventRequestBody'
import type { Event, EventFile } from '@/types/event.type'

interface Props {
  type: DialogFormType
  event?: Event
  showDialog: boolean
}

interface Emits {
  submit: [data: StoreEventRequestBody | UpdateEventRequestBody]
  close: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const initialFormData = (): StoreEventRequestBody => ({
  schoolId: undefined,
  title: '',
  excerpt: '',
  thumbnailFile: undefined,
  content: '',
  location: '',
  eventDate: undefined,
  startTime: undefined,
  endTime: undefined,
  isPublished: false,
  files: [],
})

const formData = ref<StoreEventRequestBody | UpdateEventRequestBody>(initialFormData())
const selectedFiles = ref<File[]>([])
const existingFiles = ref<EventFile[]>([])
const deletedFileIds = ref<number[]>([])

watch(
  () => props.showDialog,
  (visible) => {
    if (!visible) return

    if (props.type === 'edit' && props.event) {
      const event = props.event
      formData.value = {
        schoolId: event.schoolId,
        title: event.title,
        excerpt: event.excerpt,
        content: event.content,
        location: event.location,
        eventDate: event.eventDate ? new Date(event.eventDate) : undefined,
        startTime: event.startTime ? new Date(event.startTime) : undefined,
        endTime: event.endTime ? new Date(event.endTime) : undefined,
        isPublished: event.isPublished,
        thumbnailFile: undefined,
        files: [],
        deletedFiles: [],
      }
      existingFiles.value = [...(event.files ?? [])]
      deletedFileIds.value = []
    } else {
      formData.value = initialFormData()
      existingFiles.value = []
      deletedFileIds.value = []
    }
    selectedFiles.value = []
  },
  { immediate: true },
)

const thumbnailPreview = computed(() => {
  const thumbnail = formData.value.thumbnailFile
  if (thumbnail instanceof File) return URL.createObjectURL(thumbnail)
  return props.event?.thumbnailUrl || 'https://placehold.co/128x128?text=No+Thumbnail'
})

const onFilesSelected = (event: any) => {
  const selected = event.files as File[]
  formData.value.files = selected
  selectedFiles.value = selected
}

const removeExistingFile = (fileId: number) => {
  deletedFileIds.value.push(fileId)
  existingFiles.value = existingFiles.value.filter((file) => file.id !== fileId)
  formData.value.deletedFiles = [...deletedFileIds.value]
}

const getObjectUrl = (file: File) => URL.createObjectURL(file)
const isImage = (file: { fileType?: string; type?: string }) =>
  file.fileType?.startsWith('image') || file.type?.startsWith('image')
const isVideo = (file: { fileType?: string; type?: string }) =>
  file.fileType?.startsWith('video') || file.type?.startsWith('video')
const isPdf = (file: { fileType?: string; type?: string }) =>
  file.fileType === 'application/pdf' || file.type === 'application/pdf'
</script>

<template>
  <DialogForm
    :visible="showDialog"
    :title="`${type === 'add' ? 'Tambah' : 'Edit'} Acara`"
    :initial-values="event"
    @submit="emit('submit', { ...formData })"
    @close="emit('close')"
  >
        <TextField
      class="w-full"
      v-model="formData.title"
      label="Judul"
      name="title"
      :required="true"
    />
    
    <TextField
      class="w-full"
      v-model="formData.excerpt"
      label="Ringkasan"
      name="excerpt"
      :required="true"
    />
    
    <TextArea v-model="formData.content" label="Konten" name="content" required />
    <InfoText message="Deskripsi lengkap acara" />
    
    <TextField v-model="formData.location" label="Lokasi" name="location" />
    <InfoText message="Lokasi pelaksanaan acara" />
    
    <DatePicker
      v-model="formData.eventDate"
      label="Tanggal Acara"
      name="eventDate"
      dateFormat="dd/mm/yy"
      showIcon
      placeholder="Pilih Tanggal Acara"
    />
    <InfoText message="Tanggal pelaksanaan acara" />

    <DatePicker
      class="w-full"
      v-model="formData.startTime"
      name="startTime"
      dateFormat="dd/mm/yy"
      showTime
      hourFormat="24"
      showIcon
      placeholder="Waktu Mulai Acara"
    />
    <InfoText message="Waktu mulai pelaksanaan acara" />
    
    <DatePicker
      class="w-full"
      v-model="formData.endTime"
      name="endTime"
      showTime
      hourFormat="24"
      showIcon
      placeholder="Waktu Akhir Acara"
    />
    <InfoText message="Waktu berakhirnya acara" />

    <div>
      <label class="block font-medium mb-1">Pratinjau Thumbnail</label>
      <img :src="thumbnailPreview" alt="Thumbnail" class="w-32 h-32 object-cover rounded" />
      <InfoText message="Gambar yang akan ditampilkan sebagai thumbnail acara" />
    </div>

    <div>
      <InputFile
        class="w-full"
        v-model="formData.thumbnailFile"
        label="Unggah Thumbnail"
        name="thumbnailFile"
        accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
      />
      <InfoText message="Ukuran maksimal 5MB (Format yang didukung: JPEG, PNG, JPG, GIF, SVG)" />
    </div>

    <div>
      <FileUpload
        name="files[]"
        accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml,video/mp4,video/mov,video/avi,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        multiple
        customUpload
        @select="onFilesSelected"
      >
        <template #content>
          <InfoText message="1. Ukuran maksimal 5MB per file" />
          <InfoText message="2. Format file yang didukung: gambar (JPEG, PNG, JPG, GIF, SVG), video (MP4, MOV, AVI), PDF dan dokumen" />
          <InfoText message="3. Maksimal 10 file dalam satu waktu" />
        </template>
      </FileUpload>
    </div>

    <div>
      <span id="Publish">Publikasi</span>
      <ToggleSwitch v-model="formData.isPublished" name="isPublished" aria-labelledby="Publish" />
      <InfoText message="Aktifkan untuk mempublikasikan acara kepada pengguna" />
    </div>

    <!-- Existing File Preview -->
    <div v-if="existingFiles.length" class="grid grid-cols-2 gap-4 mt-4">
      <div
        v-for="(file, index) in existingFiles"
        :key="file.id || index"
        class="border rounded shadow p-2 bg-white relative"
      >
        <button
          class="absolute top-0 right-0 text-white text-xs bg-red-500 p-1 rounded-b-sm"
          @click.prevent="removeExistingFile(file.id!)"
        >
          <i class="pi pi-times text-xs"></i>
        </button>
        <div v-if="isImage(file)">
          <img :src="file.filePathUrl" class="w-full h-auto object-cover rounded" />
        </div>
        <div v-else-if="isVideo(file)">
          <video controls :src="file.filePathUrl" class="w-full rounded"></video>
        </div>
        <div v-else-if="isPdf(file)">
          <embed :src="file.filePathUrl" type="application/pdf" class="w-full h-64 rounded" />
        </div>
        <div v-else>
          <p class="text-sm text-gray-600">{{ file.filePath }}</p>
        </div>
      </div>
    </div>

    <!-- New File Preview -->
    <div v-if="selectedFiles.length" class="grid grid-cols-2 gap-4 mt-4">
      <div
        v-for="(file, index) in selectedFiles"
        :key="index"
        class="border rounded shadow p-2 bg-white"
      >
        <div v-if="isImage(file)">
          <img :src="getObjectUrl(file)" class="w-full h-auto object-cover rounded" />
        </div>
        <div v-else-if="isVideo(file)">
          <video controls :src="getObjectUrl(file)" class="w-full rounded"></video>
        </div>
        <div v-else-if="isPdf(file)">
          <embed :src="getObjectUrl(file)" type="application/pdf" class="w-full h-64 rounded" />
        </div>
        <div v-else>
          <p class="text-sm text-gray-600">{{ file.name }}</p>
          <span class="text-xs text-red-400">Preview tidak didukung</span>
        </div>
      </div>
    </div>
  </DialogForm>
</template>
