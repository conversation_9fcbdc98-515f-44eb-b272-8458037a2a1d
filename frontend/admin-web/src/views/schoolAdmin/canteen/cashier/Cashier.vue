<script setup lang="ts">
import { Button } from 'primevue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { CanteenRequestBody } from '@/dto/canteenRequestBody'

import CanteenCashierDialogForm from './CashierDialogForm.vue'
import { useCanteenCashierViewModel } from './useCashierViewModel'

const viewModel = useCanteenCashierViewModel()
const state = viewModel.state

const onAddClick = () => {
  viewModel.showAddDialog()
}

const onSubmit = (data: CanteenRequestBody) => {
  if (state.formType === 'add') {
    viewModel.addData(data)
  } else {
    viewModel.updateData(state.selectedData?.id as number, data)
  }
}
</script>

<template>
  <AdminSection title="<PERSON><PERSON>">
    <Button @click="onAddClick" label="Tambah" icon="pi pi-plus" class="bg-green-500 w-min" />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.canteenCashierData"
      :fetch="viewModel.getAllCanteenCashiers"
      :total-records="state.totalRecords"
      has-edit
      @edit="viewModel.showEditDialog"
      has-delete
      @delete="viewModel.deleteData($event.id)"
    />

    <CanteenCashierDialogForm
      :show-dialog="state.showDialog"
      :canteenCashiers="state.selectedData"
      :type="state.formType"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
    />
  </AdminSection>
</template>
