<script setup lang="ts">
import { But<PERSON> } from 'primevue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { CanteenRequestBody } from '@/dto/canteenRequestBody'

import CanteenDialogForm from './CanteenDialogForm.vue'
import { useCanteenViewModel } from './useCanteenViewModel'

const viewModel = useCanteenViewModel()
const state = viewModel.state

const onAddClick = () => {
  viewModel.showAddDialog()
}

const onSubmit = (data: CanteenRequestBody) => {
  if (state.formType === 'add') {
    viewModel.addData(data)
  } else {
    viewModel.updateData(state.selectedData?.id as number, data)
  }
}
</script>

<template>
  <AdminSection title="Kantin">
    <Button @click="onAddClick" label="Tambah" icon="pi pi-plus" class="bg-green-500 w-min" />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.canteenData"
      :fetch="viewModel.getAllCanteen"
      :total-records="state.totalRecords"
      has-edit
      @edit="viewModel.showEditDialog"
      has-delete
      @delete="viewModel.deleteData($event.id)"
    />

    <CanteenDialogForm
      :show-dialog="state.showDialog"
      :canteens="state.selectedData"
      :type="state.formType"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
    />
  </AdminSection>
</template>
