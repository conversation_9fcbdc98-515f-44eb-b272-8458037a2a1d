import { reactive } from 'vue'

import type { TableColumn } from '@/components/common/Table.vue'
import type { DormitoryRequestBody } from '@/dto/dormitoryRequestBody'
import type { Dormitory } from '@/types/dormitory.type'
import type { User } from '@/types/user.type'

interface DormitoryState {
  showDialog: boolean
  tableKey: number
  formData: DormitoryRequestBody
  selectedData: Dormitory | undefined
  showRoomDialog: boolean
  dormitoriesData: Dormitory[]
  teachersData: User[]
  columns: TableColumn[]
}

export const useDormitoryState = () => {
  return reactive<DormitoryState>({
    showDialog: false,
    showRoomDialog: false,
    tableKey: 0,
    formData: {},
    selectedData: undefined,
    dormitoriesData: [],
    teachersData: [],
    columns: [
      {
        header: '<PERSON><PERSON>',
        field: 'name',
      },
      {
        header: 'Kepala <PERSON>',
        field: 'guardian.name',
      },
    ],
  })
}
