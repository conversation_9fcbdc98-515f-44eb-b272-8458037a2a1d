import { useToast } from 'primevue/usetoast'

import DormitoryServices from '@/services/dormitory.service'
import TeacherServices from '@/services/teacher.service'
import type { Dormitory } from '@/types/dormitory.type'
import { showToast } from '@/utils/toast.util'

import { useDormitoryState } from './useDormitoryState'

export const useDormitoryViewModel = () => {
  const toast = useToast()
  const state = useDormitoryState()

  const showAddDialog = () => {
    state.selectedData = undefined
    state.formData = {}
    state.showDialog = true
    getAllTeachers()
  }

  const showEditDialog = (data: Dormitory) => {
    state.selectedData = data
    state.formData = {
      name: data.name,
      guardianUserId: data.guardian?.id,
    }
    state.showDialog = true
    getAllTeachers()
  }

  const closeDialog = () => {
    state.selectedData = undefined
    state.formData = {}
    state.showDialog = false
  }

  const addData = async () => {
    try {
      await DormitoryServices.createDormitory(state.formData)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Asrama berhasil ditambahkan')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Asrama gagal ditambahkan')
    }
  }

  const updateData = async (id: number) => {
    try {
      await DormitoryServices.updateDormitory(id, state.formData)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Asrama berhasil diperbarui')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Asrama gagal diperbarui')
    }
  }

  const deleteData = async (id: number) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus data ini?')) {
      try {
        await DormitoryServices.deleteDormitory(id)
        state.tableKey++
        showToast(toast, 'success', 'Asrama berhasil dihapus')
      } catch (error) {
        console.error(error)
        showToast(toast, 'error', 'Asrama gagal dihapus')
      }
    }
  }

  const getAllDormitories = async () => {
    try {
      const { data } = await DormitoryServices.getAllDormitories()
      state.dormitoriesData = data.data
    } catch (error) {
      console.error(error)
    }
  }

  const getAllTeachers = async () => {
    try {
      const { data } = await TeacherServices.getAllTeachers()
      state.teachersData = data.data
    } catch (error) {
      console.error(error)
    }
  }

  const showRoomDialog = (data: Dormitory) => {
    state.selectedData = data
    state.showRoomDialog = true
  }

  const closeRoomDialog = () => {
    state.selectedData = undefined
    state.showRoomDialog = false
  }

  return {
    state,
    showAddDialog,
    showEditDialog,
    closeDialog,
    addData,
    updateData,
    deleteData,
    showRoomDialog,
    closeRoomDialog,
    getAllDormitories,
  }
}
