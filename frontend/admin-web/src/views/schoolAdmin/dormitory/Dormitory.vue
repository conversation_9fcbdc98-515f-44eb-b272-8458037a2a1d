<script setup lang="ts">
import { Button } from 'primevue'

import AdminSection from '@/components/admin/AdminSection.vue'
import DialogForm from '@/components/common/DialogForm.vue'
import SelectField from '@/components/common/SelectField.vue'
import Table from '@/components/common/Table.vue'
import TextField from '@/components/common/TextField.vue'

import RoomDialog from './roomDialog/RoomDialog.vue'
import { useDormitoryViewModel } from './useDormitoryViewModel'
const viewModel = useDormitoryViewModel()
const state = viewModel.state

const onSubmit = () => {
  if (state.selectedData) {
    viewModel.updateData(state.selectedData.id)
  } else {
    viewModel.addData()
  }
}
</script>

<template>
  <AdminSection title="Asrama">
    <Button
      @click="viewModel.showAddDialog()"
      label="Tambah"
      icon="pi pi-plus"
      class="bg-green-500 w-min"
    />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.dormitoriesData"
      :fetch="viewModel.getAllDormitories"
      has-delete
      has-edit
      @edit="viewModel.showEditDialog"
      @delete="viewModel.deleteData($event.id)"
    >
      <template #action="{ data }">
        <Button
          @click="viewModel.showRoomDialog(data)"
          icon="pi pi-building"
          class="p-button-rounded p-button-info"
        />
      </template>
    </Table>

    <RoomDialog
      v-if="state.selectedData"
      :visible="state.showRoomDialog"
      :dormitory="state.selectedData"
      @close="viewModel.closeRoomDialog"
    />

    <DialogForm
      :visible="state.showDialog"
      :initial-values="state.selectedData"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
      title="Tambah Asrama"
    >
      <TextField v-model="state.formData.name" label="Nama Asrama" name="name" />
      <SelectField
        v-model="state.formData.guardianUserId"
        :options="state.teachersData"
        label="Kepala Asrama"
        name="guardianUserId"
        option-label="name"
        option-value="id"
        filter
      />
    </DialogForm>
  </AdminSection>
</template>
