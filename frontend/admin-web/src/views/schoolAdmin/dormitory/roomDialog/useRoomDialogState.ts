import { reactive } from 'vue'

import type { TableColumn } from '@/components/common/Table.vue'
import type { RoomRequestBody } from '@/dto/roomRequestBody'
import type { Room } from '@/types/room.type'
import type { User } from '@/types/user.type'

interface RoomDialogState {
  showDialog: boolean
  tableKey: number
  formData: RoomRequestBody
  selectedData: Room | undefined
  roomsData: Room[]
  teachersData: User[]
  columns: TableColumn[]
}

export const useRoomDialogState = () => {
  return reactive<RoomDialogState>({
    showDialog: false,
    tableKey: 0,
    formData: {},
    selectedData: undefined,
    roomsData: [],
    teachersData: [],
    columns: [
      {
        header: '<PERSON><PERSON>',
        field: 'name',
      },
      {
        header: '<PERSON><PERSON>',
        field: 'guardian.name',
      },
    ],
  })
}
