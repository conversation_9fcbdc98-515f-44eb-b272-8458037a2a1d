import { useToast } from 'primevue/usetoast'

import DormitoryServices from '@/services/dormitory.service'
import RoomServices from '@/services/room.service'
import TeacherServices from '@/services/teacher.service'
import type { Room } from '@/types/room.type'
import { showToast } from '@/utils/toast.util'

import { useRoomDialogState } from './useRoomDialogState'

export const useRoomDialogViewModel = () => {
  const toast = useToast()
  const state = useRoomDialogState()

  const showAddDialog = () => {
    state.selectedData = undefined
    state.formData = {}
    state.showDialog = true
    getAllTeachers()
  }

  const showEditDialog = (data: Room) => {
    state.selectedData = data
    state.formData = {
      name: data.name,
      guardianUserId: data.guardian?.id,
    }
    state.showDialog = true
    getAllTeachers()
  }

  const closeDialog = () => {
    state.selectedData = undefined
    state.formData = {}
    state.showDialog = false
  }

  const addData = async (dormitoryId: number) => {
    try {
      await RoomServices.createRoom({
        ...state.formData,
        dormitoryId: dormitoryId,
      })
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Kamar berhasil ditambahkan')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Kamar gagal ditambahkan')
    }
  }

  const updateData = async (id: number) => {
    try {
      await RoomServices.updateRoom(id, state.formData)
      state.tableKey++
      closeDialog()
      showToast(toast, 'success', 'Kamar berhasil diperbarui')
    } catch (error) {
      console.error(error)
      showToast(toast, 'error', 'Kamar gagal diperbarui')
    }
  }

  const deleteData = async (id: number) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus data ini?')) {
      try {
        await RoomServices.deleteRoom(id)
        state.tableKey++
        showToast(toast, 'success', 'Kamar berhasil dihapus')
      } catch (error) {
        console.error(error)
        showToast(toast, 'error', 'Kamar gagal dihapus')
      }
    }
  }

  const getAllRooms = async (id: number) => {
    try {
      const { data } = await DormitoryServices.getDormitoryById(id)
      state.roomsData = data.data.rooms
    } catch (error) {
      console.error(error)
    }
  }

  const getAllTeachers = async () => {
    try {
      const { data } = await TeacherServices.getAllTeachers()
      state.teachersData = data.data
    } catch (error) {
      console.error(error)
    }
  }

  return {
    state,
    showAddDialog,
    showEditDialog,
    closeDialog,
    addData,
    updateData,
    deleteData,
    getAllRooms,
  }
}
