<script setup lang="ts">
import { Button } from 'primevue'

import Dialog from '@/components/common/Dialog.vue'
import DialogForm from '@/components/common/DialogForm.vue'
import SelectField from '@/components/common/SelectField.vue'
import Table from '@/components/common/Table.vue'
import TextField from '@/components/common/TextField.vue'
import type { Dormitory } from '@/types/dormitory.type'

import { useRoomDialogViewModel } from './useRoomDialogViewModel'

const viewModel = useRoomDialogViewModel()
const state = viewModel.state
interface Props {
  visible: boolean
  dormitory: Dormitory
}

interface Emits {
  close: []
}

const props = defineProps<Props>()
defineEmits<Emits>()

const onSubmit = () => {
  if (state.selectedData) {
    viewModel.updateData(state.selectedData.id)
  } else {
    viewModel.addData(props.dormitory.id)
  }
}
</script>

<template>
  <Dialog :visible @close="$emit('close')" title="Data Kamar">
    <Button
      @click="viewModel.showAddDialog()"
      label="Tambah"
      icon="pi pi-plus"
      class="bg-green-500 w-min"
    />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.roomsData"
      :fetch="() => viewModel.getAllRooms(dormitory.id)"
      has-delete
      has-edit
      @edit="viewModel.showEditDialog"
      @delete="viewModel.deleteData($event.id)"
    />

    <DialogForm
      :visible="state.showDialog"
      :initial-values="state.selectedData"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
      title="Tambah Kamar"
    >
      <TextField v-model="state.formData.name" label="Nama Kamar" name="name" />
      <SelectField
        v-model="state.formData.guardianUserId"
        :options="state.teachersData"
        label="Wali Kamar"
        name="guardianUserId"
        option-label="name"
        option-value="id"
        filter
      />
    </DialogForm>
  </Dialog>
</template>
