<script setup lang="ts">
import BgHero from '@/assets/bg_hero.png'
import Column from '@/components/common/Column.vue'
import Row from '@/components/common/Row.vue'
</script>

<template>
  <div>
    <section
      class="w-full overflow-hidden bg-cover bg-no-repeat relative"
      :style="{ backgroundImage: `url(${BgHero})` }"
    >
      <div class="absolute inset-0 opacity-50"></div>

      <div
        class="relative max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-8 sm:py-12 md:py-16 lg:py-20"
      >
        <Row
          class="justify-center items-center min-h-[50vh] sm:min-h-[60vh] md:min-h-[65vh] lg:min-h-[75vh] xl:min-h-[85vh] flex-col gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16"
          responsive
        >
          <div class="teks-black">
            <h1 class="text-3xl sm:text-4xl md:text-5xl font-semibold mb-4">Latar Belakang TIAS</h1>
            <p class="text-lg sm:text-xl md:text-2xl leading-relaxed">
              TIAS dikembangkan sebagai solusi digital yang bertujuan untuk menciptakan sebuah
              platform digital yang terintegrasi guna mendukung proses belajar yang lebih fleksibel,
              interaktif, dan mudah diakses oleh pengguna dari berbagai latar belakang pendidikan.
            </p>
            <p class="text-lg sm:text-xl md:text-2xl leading-relaxed mt-4">
              Dengan menghadirkan berbagai fitur dalam satu aplikasi, TIAS berupaya menciptakan
              suatu digital yang inklusif, aman, serta mudah diakses oleh seluruh lapisan
              masyarakat.
            </p>
          </div>
        </Row>
      </div>
    </section>
    <section class="w-full py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12">
        <h2 class="text-3xl sm:text-4xl md:text-5xl font-semibold text-center mb-12">
          Visi dan Misi Kami
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-12">
          <!-- Vision Illustration - Moved to Left -->
          <div class="flex justify-center">
            <img
              src="@/assets/pendidikan.png"
              alt="Blibli"
              class="w-full max-w-[562px] h-auto object-contain hover:scale-105 transition-transform duration-200"
            />
          </div>

          <!-- Vision & Mission Text - Moved to Right -->
          <div class="space-y-6 sm:space-y-8 w-full">
            <!-- Efficiency Card -->
            <div
              class="bg-red-100 rounded-2xl p-4 sm:p-6 shadow-md hover:shadow-lg transition-shadow border border-red-200 w-full min-h-[80px] sm:min-h-[100px] flex justify-center items-center"
            >
              <p class="text-sm sm:text-base text-center">
                <span class="font-bold">Meningkatkan efisiensi</span> dalam pengelolaan administrasi
                sekolah.
              </p>
            </div>

            <!-- Parents Support Card -->
            <div
              class="bg-red-100 rounded-2xl p-4 sm:p-6 shadow-md hover:shadow-lg transition-shadow border border-red-200 w-full min-h-[80px] sm:min-h-[100px] flex justify-center items-center"
            >
              <p class="text-sm sm:text-base text-center">
                <span class="font-bold">Mempermudah orang tua</span> dalam memantau perkembangan
                akademik anak.
              </p>
            </div>

            <!-- Real-time Monitoring Card -->
            <div
              class="bg-red-100 rounded-2xl p-4 sm:p-6 shadow-md hover:shadow-lg transition-shadow border border-red-200 w-full min-h-[90px] sm:min-h-[110px] flex justify-center items-center"
            >
              <p class="text-sm sm:text-base text-center">
                Menghadirkan sistem yang dapat mengintegrasikan administrasi sekolah dengan
                <span class="font-bold">monitoring aktivitas siswa secara real-time.</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="w-full py-16 bg-white">
      <div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12">
        <h2 class="text-3xl sm:text-4xl md:text-5xl font-semibold text-center mb-12">
          Pengembangan Produk Kami
        </h2>
        <Row class="gap-8" responsive>
          <Column class="flex-1">
            <div
              class="bg-white rounded-2xl border-2 border-gray-200 p-8 h-full hover:shadow-xl transition-shadow"
            >
              <h3 class="text-2xl font-bold text-center">Kantin TIAS</h3>
              <Column class="flex-1 flex justify-center items-center">
                <img
                  src="@/assets/pendidikan.png"
                  alt="pendidikan"
                  class="w-[562px] h-[316px] object-contain hover:scale-105 transition-transform duration-200 flex-shrink-0"
                />
              </Column>
              <p class="text-gray-600 text-center leading-relaxed">
                Kantin TIAS menyediakan layanan pembelian makanan dan minuman dengan sistem
                pembayaran cashless melalui dompet digital.
              </p>
            </div>
          </Column>
          <Column class="flex-1">
            <div
              class="bg-white rounded-2xl border-2 border-gray-200 p-8 h-full hover:shadow-xl transition-shadow"
            >
              <h3 class="text-2xl font-bold text-center">LMS TIAS</h3>
              <Column class="flex-1 flex justify-center items-center">
                <img
                  src="@/assets/coffe.png"
                  alt="Blibli"
                  class="w-[462px] h-[256px] object-contain hover:scale-105 transition-transform duration-200 flex-shrink-0"
                />
              </Column>
              <p class="text-gray-600 text-center leading-relaxed mt-10">
                LMS TIAS merupakan sistem pembelajaran online yang digunakan untuk pelaksanaan ujian
                seperti UTS, UAS, dan ujian lainnya secara digital. Platform ini membantu proses
                ujian menjadi lebih tertib, efisien, dan terpantau.
              </p>
            </div>
          </Column>
        </Row>
      </div>
    </section>
  </div>
</template>
