<script setup lang="ts">
import BgHero from '@/assets/bg_hero.png'
import feature3 from '@/assets/feature/amico.png'
import feature2 from '@/assets/feature/cuate.png'
import feature1 from '@/assets/feature/rafiki.png'
import feature4 from '@/assets/feature/search.png'
import HeroImage from '@/assets/hero_image.png'
import icon1 from '@/assets/icon/icon1.png'
import icon2 from '@/assets/icon/icon2.png'
import icon3 from '@/assets/icon/icon3.png'
import icon4 from '@/assets/icon/icon4.png'
import info1 from '@/assets/information/image1.jpg'
import info2 from '@/assets/information/image2.jpg'
import info3 from '@/assets/information/image3.jpg'
import role1 from '@/assets/role/amico.png'
import role4 from '@/assets/role/pana.png'
import role2 from '@/assets/role/panaa.png'
import role3 from '@/assets/role/rafiki.png'
import why1 from '@/assets/why/why1.png'
import why2 from '@/assets/why/why2.png'
import why3 from '@/assets/why/why3.png'
import why4 from '@/assets/why/why4.png'
import Column from '@/components/common/Column.vue'
import Row from '@/components/common/Row.vue'

const why = [
  {
    title: 'Sistem Terintegrasi',
    description:
      'Semua data terkait siswa, guru, dan kegiatan sekolah tersimpan dalam satu platform.',
    icon: why1,
    isImageFile: true,
  },
  {
    title: 'Akses Real-Time',
    description:
      'Orang tua, guru, dan siswa dapat mengakses data akademik dan administrasi kapan saja.',
    icon: why2,
    isImageFile: true,
  },
  {
    title: 'Skalabilitas Tinggi',
    description:
      'Sistem dapat disesuaikan dengan jumlah siswa dan kebutuhan sekolah, sehingga cocok untuk sekolah kecil hingga besar.',
    icon: why3,
    isImageFile: true,
  },
  {
    title: 'Multi-Perangkat',
    description: 'Bisa digunakan di berbagai perangkat, untuk meningkatkan fleksibilitas pengguna.',
    icon: why4,
    isImageFile: true,
  },
]

const infoCards = [
  {
    title: 'Platform digital yang dirancang untuk meningkatkan kualitas pelayanan sekolah.',
    image: info1,
    isImageFile: true,
    bgColor: 'bg-[#FFF4F7]',
  },
  {
    title: 'Menghubungkan guru, siswa, dan orang tua dalam satu ekosistem terintegrasi.',
    image: info2,
    isImageFile: true,
    bgColor: 'bg-[#FFF4F7]',
  },
  {
    title: 'Dapat diakses dimanapun dan kapanpun melalui perangkat yang terhubung ke internet.',
    image: info3,
    isImageFile: true,
    bgColor: 'bg-[#FFF4F7]',
  },
]

const feature = [
  {
    title: 'Informasi Akademi',
    description:
      'Akses informasi akademik dan data siswa. Melihat jadwal pelajaran dan materi belajar. Mengakses bank soal dan tugas dari guru. Mengakses kalender akademik dan pengumuman sekolah.',
    image: feature1,
    isImageFile: true,
    bgColor: 'bg-[#FFF8FA]',
  },
  {
    title: 'Media Edukasi',
    description:
      'Mengakses materi pembelajaran. Mengerjakan latihan soal atau ujian. Berdiskusi dengan guru dan teman melalui forum edukatif. Belajar mandiri melalui materi digital atau video pembelajaran.',
    image: feature2,
    isImageFile: true,
    bgColor: 'bg-[#FFF8FA]',
  },
  {
    title: 'Layanan Akademik',
    description:
      'Mengajukan surat keterangan sekolah dari sekolah secara digital. Melakukan pembayaran SPP dan administrasi lainnya secara online. Mencetak kartu ujian dan kartu pelajar digital. Mengajukan izin secara online.',
    image: feature3,
    isImageFile: true,
    bgColor: 'bg-[#FFF8FA]',
  },
  {
    title: 'Pemantauan Akademik',
    description:
      'Memantau kehadiran harian siswa. Memantau perkembangan nilai dan performa belajar siswa. Melihat grafik perkembangan akademik siswa. Menerima notifikasi jika siswa tidak hadir atau tidak mengumpulkan tugas',
    image: feature4,
    isImageFile: true,
    bgColor: 'bg-[#FFF8FA]',
  },
]

const roles = [
  {
    icon: role1,
    isImageFile: true,
    title: 'Super Admin',
    description:
      'Menambahkan, mengedit, menghapus, dan melihat data pengguna. Mengelola jadwal pelajaran/kegiatan sekolah dan pengumuman.',
  },
  {
    icon: role2,
    isImageFile: true,
    title: 'Orang Tua',
    description:
      'Melihat perkembangan akademik anak, jadwal pelajaran anak, dan mengakses pengumuman sekolah.',
  },
  {
    icon: role3,
    isImageFile: true,
    title: 'Guru',
    description: 'Mengelola jadwal mengajar, daftar siswa, dan penilaian siswa.',
  },
  {
    icon: role4,
    isImageFile: true,
    title: 'Siswa',
    description: 'Melihat data pribadi, jadwal pelajaran, dan nilai akademik.',
  },
]

const comparisons = [
  {
    icon: icon1,
    isImageFile: true,
    title: 'Media Informasi',
    description:
      'Akun orang tua bisa akses informasi siswa atau anak lebih dari satu dalam satu aplikasi.',
    bgColor: 'bg-[#FFF4F7]',
  },
  {
    icon: icon2,
    isImageFile: true,
    title: 'Modular',
    description:
      'Setiap sekolah yang berlangganan memiliki service terpisah sehingga data masing-masing sekolah dapat diakses dengan mudah dan cepat.',
    bgColor: 'bg-[#FFF4F7]',
  },
  {
    icon: icon3,
    isImageFile: true,
    title: 'Terintegrasi',
    description: 'Dapat menjembatani integrasi dengan pihak lain. Contoh: e-MIS dan Dapodik.',
    bgColor: 'bg-[#FFF4F7]',
  },
  {
    icon: icon4,
    isImageFile: true,
    title: 'Aman',
    description:
      'Data data pihak sekolah, orang tua, maupun murid dijamin keamanannya karena perusahaan kami memiliki network operation center dan security operation center yang menjaga data 24/7.',
    bgColor: 'bg-[#FFF4F7]',
  },
]
</script>

<template>
  <div class="homepage-container">
    <section
      class="w-full overflow-hidden bg-cover bg-center bg-no-repeat relative"
      :style="{ backgroundImage: `url(${BgHero})` }"
    >
      <div class="absolute inset-0"></div>

      <div
        class="relative max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-8 sm:py-12 md:py-16 lg:py-20"
      >
        <Row
          class="justify-between items-center min-h-[50vh] sm:min-h-[60vh] md:min-h-[65vh] lg:min-h-[75vh] xl:min-h-[85vh] flex-col-reverse md:flex-row gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16"
          responsive
        >
          <Column
            class="flex-1 gap-4 sm:gap-5 md:gap-6 lg:gap-8 text-center md:text-left w-full max-w-lg md:max-w-none"
          >
            <h1
              class="font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl leading-tight tracking-tight"
            >
              <span class="text-[#FD0047]">Sekolah</span> Pintar <br />
              Dimulai Dari Sini
            </h1>
            <p
              class="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-gray-600 leading-relaxed max-w-2xl md:max-w-lg lg:max-w-xl xl:max-w-2xl"
            >
              Ucapkan selamat tinggal pada tumpukan kertas dan sistem yang ribet. TIAS bantu semua
              jadi otomatis, rapi, dan terkendali.
            </p>
            <div class="flex justify-center md:justify-start mt-6 sm:mt-8">
              <img
                src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png?hl=id"
                alt="Google Play Badge"
                class="w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48 hover:scale-105 transition-transform duration-300 cursor-pointer"
              />
            </div>
          </Column>
          <div class="flex-1 flex justify-center w-full">
            <img
              :src="HeroImage"
              alt="TIAS Hero Image"
              class="w-full max-w-[280px] sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-2xl hover:scale-105 transition-transform duration-500"
            />
          </div>
        </Row>
      </div>
    </section>
    <section class="w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-12 sm:py-16 md:py-20 lg:py-24">
      <div class="max-w-7xl mx-auto">
        <h2
          class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20 text-gray-800 tracking-tight"
        >
          Teknologi Informasi Akademik Sekolah
        </h2>

        <div
          class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8 md:gap-10 lg:gap-12"
        >
          <div
            v-for="(card, index) in infoCards"
            :key="index"
            :class="`${card.bgColor} rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-10 text-center shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-pink-200`"
            class="min-h-[300px] sm:min-h-[320px] md:min-h-[350px] lg:min-h-[380px]"
          >
            <div class="flex flex-col justify-center items-center h-full space-y-4 sm:space-y-6">
              <div v-if="card.isImageFile" class="w-full">
                <img
                  :src="card.image"
                  :alt="card.title"
                  class="w-full max-w-[200px] sm:max-w-[240px] md:max-w-[280px] lg:max-w-[320px] h-32 sm:h-40 md:h-48 lg:h-56 object-cover rounded-xl mx-auto hover:scale-105 transition-transform duration-300"
                />
              </div>
              <p
                class="text-gray-700 leading-relaxed text-sm sm:text-base md:text-lg font-medium px-2 sm:px-4"
              >
                {{ card.title }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-12 sm:py-16 md:py-20 lg:py-24">
      <div class="max-w-7xl mx-auto">
        <h2
          class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20 text-gray-800 tracking-tight"
        >
          Kenapa TIAS?
        </h2>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-10">
          <div
            v-for="(item, index) in why"
            :key="index"
            class="text-center p-4 sm:p-6 md:p-8 rounded-2xl bg-white hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
          >
            <div
              v-if="item.isImageFile"
              class="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 flex items-center justify-center mx-auto mb-4 sm:mb-6 p-2"
            >
              <img
                :src="item.icon"
                :alt="item.title"
                class="w-full h-full object-contain rounded-lg hover:scale-110 transition-transform duration-300"
              />
            </div>
            <h3
              class="font-bold text-base sm:text-lg md:text-xl lg:text-2xl mb-3 sm:mb-4 text-gray-800"
            >
              {{ item.title }}
            </h3>
            <p class="text-gray-600 text-sm sm:text-base md:text-lg leading-relaxed">
              {{ item.description }}
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-12 sm:py-16 md:py-20 lg:py-24">
      <div class="max-w-7xl mx-auto">
        <h2
          class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20 text-gray-800 tracking-tight"
        >
          Fitur TIAS
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16">
          <div
            v-for="(featureItem, index) in feature"
            :key="index"
            :class="`${featureItem.bgColor} rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-10 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-pink-200`"
            class="min-h-[350px] sm:min-h-[400px] md:min-h-[450px] lg:min-h-[500px]"
          >
            <div
              class="flex flex-col justify-center items-center h-full text-center space-y-4 sm:space-y-6"
            >
              <div class="w-full flex justify-center">
                <img
                  :src="featureItem.image"
                  :alt="featureItem.title"
                  class="w-full max-w-[200px] sm:max-w-[250px] md:max-w-[300px] lg:max-w-[350px] h-auto object-contain rounded-lg hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div class="w-full text-center">
                <h3
                  class="font-bold text-xl sm:text-2xl md:text-3xl lg:text-4xl mb-4 sm:mb-6 text-red-500"
                >
                  {{ featureItem.title }}
                </h3>
                <p class="text-gray-600 text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed">
                  {{ featureItem.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section
      class="w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50"
    >
      <div class="max-w-7xl mx-auto">
        <h2
          class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20 text-gray-800 tracking-tight"
        >
          User Role
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12">
          <div
            v-for="(role, index) in roles"
            :key="index"
            class="bg-white rounded-2xl sm:rounded-3xl lg:rounded-l-3xl lg:rounded-r-full p-6 sm:p-8 md:p-10 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 min-h-[200px] sm:min-h-[220px] md:min-h-[250px]"
          >
            <div
              class="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 md:gap-8 h-full"
            >
              <div
                class="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 flex items-center justify-center flex-shrink-0"
              >
                <img
                  :src="role.icon"
                  :alt="role.title"
                  class="w-full h-full object-contain rounded-lg hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div
                class="flex flex-col justify-center items-center sm:items-start text-center sm:text-left w-full space-y-2 sm:space-y-3"
              >
                <h3 class="font-bold text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-800">
                  {{ role.title }}
                </h3>
                <p class="text-gray-600 text-sm sm:text-base md:text-lg leading-relaxed">
                  {{ role.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 py-12 sm:py-16 md:py-20 lg:py-24">
      <div class="max-w-7xl mx-auto">
        <h2
          class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 md:mb-16 text-gray-800 tracking-tight"
        >
          Research Perbandingan TIAS VS yang lain
        </h2>

        <div
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-10 mt-12 sm:mt-16"
        >
          <div
            v-for="(comparison, index) in comparisons"
            :key="index"
            :class="`${comparison.bgColor} rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-10 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-red-200 relative pt-12 sm:pt-16 md:pt-20`"
            class="min-h-[280px] sm:min-h-[320px] md:min-h-[360px]"
          >
            <div
              class="absolute -top-8 sm:-top-10 md:-top-12 left-1/2 transform -translate-x-1/2 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-[#FD0047] rounded-full flex items-center justify-center z-10 shadow-lg hover:scale-110 transition-transform duration-300"
            >
              <img
                :src="comparison.icon"
                :alt="comparison.title"
                class="w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 object-contain"
              />
            </div>

            <div class="flex flex-col justify-start items-center h-full space-y-4 sm:space-y-6">
              <h3 class="font-bold text-lg sm:text-xl md:text-2xl text-gray-800">
                {{ comparison.title }}
              </h3>
              <p class="text-gray-600 text-sm sm:text-base md:text-lg leading-relaxed text-center">
                {{ comparison.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
@media (max-width: 640px) {
  .homepage-container {
    overflow-x: hidden;
  }
}
html {
  scroll-behavior: smooth;
}
/* .homepage-container img {
  will-change: transform;
} */
</style>
