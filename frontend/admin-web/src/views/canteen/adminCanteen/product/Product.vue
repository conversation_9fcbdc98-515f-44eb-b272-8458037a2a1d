<script setup lang="ts">
import { Button } from 'primevue'
import { onMounted } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { ProductRequestBody } from '@/dto/productRequestBody'

import ProductDialogForm from './ProductDialogForm.vue'
import { useProductViewModel } from './useProductViewModel'

const viewModel = useProductViewModel()
const state = viewModel.state
const onAddClick = () => {
  viewModel.showAddDialog()
}

const onSubmit = (data: ProductRequestBody) => {
  if (state.formType === 'add') {
    viewModel.addData(data)
  } else {
    viewModel.updateData(state.selectedData?.id as number, data)
  }
}

onMounted(() => {
  viewModel.getAllProduct()
})
</script>

<template>
  <AdminSection title="Produk">
    <Button @click="onAddClick" label="Tambah" icon="pi pi-plus" class="bg-green-500 w-min" />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.productData"
      :fetch="viewModel.getAllProduct"
      :total-records="state.totalRecords"
      has-edit
      @edit="viewModel.showEditDialog"
      has-delete
      @delete="viewModel.deleteData($event.id)"
    />

    <ProductDialogForm
      :show-dialog="state.showDialog"
      :product="state.selectedData"
      :type="state.formType"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
    />
  </AdminSection>
</template>
