<script setup lang="ts">
import { Button } from 'primevue'
import { onMounted } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { PurchaseRequestBody } from '@/dto/purchaseRequestBody'

import PurchaseDialogForm from './PurchaseDialogForm.vue'
import { usePurchaseViewModel } from './usePurchaseViewModel'

const viewModel = usePurchaseViewModel()
const state = viewModel.state

const onAddClick = () => {
  viewModel.showAddDialog()
}

const onSubmit = (data: PurchaseRequestBody) => {
  if (state.formType === 'add') {
    viewModel.addData(data)
  } else {
    // viewModel.(state.selectedData?.id as number, data)
  }
}

onMounted(() => {
  viewModel.getAllPurchases()
})
</script>

<template>
  <AdminSection title="Stok Produk">
    <Button @click="onAddClick" label="Tambah" icon="pi pi-plus" class="bg-green-500 w-min" />

    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.purchaseData"
      :fetch="viewModel.getAllPurchases"
      :total-records="state.totalRecords"
      has-edit
      @delete="viewModel.deleteData($event.id)"
    />

    <PurchaseDialogForm
      :show-dialog="state.showDialog"
      :purc="state.selectedData"
      :type="state.formType"
      @close="viewModel.closeDialog"
      @submit="onSubmit"
    />
  </AdminSection>
</template>
