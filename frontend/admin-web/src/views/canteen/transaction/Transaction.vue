<script setup lang="ts">
import { onMounted } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import Table from '@/components/common/Table.vue'
import type { TransactionRequestBody } from '@/dto/transactionRequestBody'

import DetailTransantionList from './DetailTransantionList.vue'
import TransactionDialogForm from './TransactionDialogForm.vue'
import { useTransactionViewModel } from './useTransactionViewModel'

const viewModel = useTransactionViewModel()
const state = viewModel.state

const onSubmit = (data: TransactionRequestBody) => {
  if (state.formType === 'edit') {
    viewModel.updateDataTransaction(state.selectedData?.id as number, data)
  }
}

onMounted(() => {
  viewModel.getAllTransaction({ status: 'completed' })
})
</script>

<template>
  <AdminSection title="History Transaksi">
    <Table
      :key="state.tableKey"
      :columns="state.columns"
      :data="state.transactionData"
      :fetch="() => viewModel.getAllTransaction({ status: 'completed' })"
      :total-records="state.totalRecords"
      has-view
      @view="viewModel.viewDetailTransaction"
      has-edit
      @edit="viewModel.showEditDialog"
    />

    <DetailTransantionList
      :transactionProductList="state.selectedData"
      :show-dialog="state.showProductList"
      @close="state.showProductList = false"
    />

    <TransactionDialogForm
      :show-dialog="state.showDialog"
      :transaction="state.selectedData"
      :type="state.formType"
      @submit="onSubmit"
      @close="viewModel.closeDialog"
    />
  </AdminSection>
</template>
