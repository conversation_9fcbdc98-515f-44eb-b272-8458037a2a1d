// composables/useChangePasswordViewModel.ts
import { computed } from 'vue'

import router from '@/router'
import AuthServices from '@/services/auth.service'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useChangePasswordState } from './useChangePassowordState'

export const useChangePasswordViewModel = () => {
  const state = useChangePasswordState()

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  const goBack = () => {
    router.back()
  }

  const clientErrors = computed(() => {
    const errs: Record<string, string[]> = {}
    const { currentPassword, newPassword, confirmNewPassword } = state.formData

    if (!currentPassword || currentPassword.length < 8) {
      errs.currentPassword = ['Minimal 8 karakter.']
    }
    if (!newPassword || newPassword.length < 8) {
      errs.newPassword = ['Minimal 8 karakter.']
    }
    if (newPassword && !/[0-9]/.test(newPassword)) {
      errs.newPassword = [...(errs.newPassword || []), 'Sertakan angka.']
    }
    if (newPassword && !/[A-Za-z]/.test(newPassword)) {
      errs.newPassword = [...(errs.newPassword || []), 'Sertakan huruf.']
    }
    if (!confirmNewPassword || confirmNewPassword !== newPassword) {
      errs.confirmNewPassword = ['Konfirmasi password tidak sama.']
    }
    return errs
  })

  const hasClientError = computed(() => Object.keys(clientErrors.value).length > 0)

  const resetForm = () => {
    state.formData.currentPassword = ''
    state.formData.newPassword = ''
    state.formData.confirmNewPassword = ''
  }

  const submit = async () => {
    if (hasClientError.value) return

    await handleRequest(
      async () => {
        await AuthServices.changePassword(state.formData)
        state.formErrors = null
        resetForm()
        goBack()
      },
      'Password berhasil diubah',
      'Gagal mengubah password',
    )
  }

  return {
    state,
    clientErrors,
    hasClientError,
    submit,
    resetForm,
    goBack,
  }
}
