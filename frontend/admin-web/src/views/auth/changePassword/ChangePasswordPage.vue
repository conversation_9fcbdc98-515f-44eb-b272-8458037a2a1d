<!-- pages/ChangePasswordPage.vue -->
<script setup lang="ts">
import Button from 'primevue/button'
import Card from 'primevue/card'
import Divider from 'primevue/divider'
import InlineMessage from 'primevue/inlinemessage'
import Password from 'primevue/password'

import { useChangePasswordViewModel } from './useChangePasswordViewModel'

const { state, clientErrors, hasClientError, submit, resetForm, goBack } =
  useChangePasswordViewModel()
</script>

<template>
  <div class="p-3 md:p-4 lg:p-6 max-w-xl mx-auto">
    <Card>
      <template #title>Ganti Password</template>
      <template #subtitle>Perbarui password akun Anda</template>

      <template #content>
        <div class="grid gap-4">
          <!-- Current Password -->
          <div class="flex flex-col gap-2">
            <label for="current_password" class="font-medium">Password</label>
            <Password
              v-model="state.formData.currentPassword"
              inputId="current_password"
              :feedback="false"
              toggleMask
              placeholder="********"
              fluid
            />
            <div v-if="clientErrors.currentPassword" class="flex flex-col gap-1">
              <InlineMessage
                v-for="(e, i) in clientErrors.currentPassword"
                :key="'cpc-' + i"
                severity="warn"
              >
                {{ e }}
              </InlineMessage>
            </div>
          </div>

          <Divider class="my-1" />

          <!-- New Password -->
          <div class="flex flex-col gap-2">
            <label for="new_password" class="font-medium">Password Baru</label>
            <Password
              v-model="state.formData.newPassword"
              inputId="new_password"
              toggleMask
              :feedback="false"
              promptLabel="Masukkan password"
              weakLabel="Lemah"
              mediumLabel="Sedang"
              strongLabel="Kuat"
              placeholder="Minimal 8 karakter"
              fluid
            />
            <div v-if="clientErrors.newPassword" class="flex flex-col gap-1">
              <InlineMessage
                v-for="(e, i) in clientErrors.newPassword"
                :key="'npc-' + i"
                severity="warn"
              >
                {{ e }}
              </InlineMessage>
            </div>
          </div>

          <!-- Confirm New Password -->
          <div class="flex flex-col gap-2">
            <label for="confirm_new_password" class="font-medium">Konfirmasi Password Baru</label>
            <Password
              v-model="state.formData.confirmNewPassword"
              inputId="confirm_new_password"
              :feedback="false"
              toggleMask
              placeholder="Ulangi password baru"
              fluid
            />
            <div v-if="clientErrors.confirmNewPassword" class="flex flex-col gap-1">
              <InlineMessage
                v-for="(e, i) in clientErrors.confirmNewPassword"
                :key="'cnp-' + i"
                severity="warn"
              >
                {{ e }}
              </InlineMessage>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex items-center justify-between gap-2">
          <Button label="Kembali" severity="secondary" outlined @click="goBack" />

          <div class="flex items-center gap-2">
            <Button label="Reset" severity="secondary" outlined @click="resetForm" />
            <Button label="Simpan" icon="pi pi-save" @click="submit" :disabled="hasClientError" />
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>
