<script setup lang="ts">
import { Form } from '@primevue/forms'
import { Button, Password } from 'primevue'

import img1 from '@/assets/img_login_1.png'
import img2 from '@/assets/img_login_2.png'
import tiasLogo from '@/assets/tias_logo.png'
import Column from '@/components/common/Column.vue'
import TextField from '@/components/common/TextField.vue'
import string from '@/strings.json'

import { useLoginPageViewModel } from './useLoginPageViewModel'

const viewModel = useLoginPageViewModel()
const state = viewModel.state
</script>

<template>
  <Column id="loginPage" class="h-screen justify-center p-12 sm:px-24 md:px-36">
    <Form
      @submit="viewModel.login"
      class="flex flex-col gap-4 self-end w-full lg:w-1/2 z-50 bg-white px-12 py-14 rounded-2xl"
    >
      <img :src="tiasLogo" alt="tias_logo" width="80" class="self-center" />
      <p class="text-center mb-4">{{ string.appName }}</p>
      <TextField
        v-model="state.formData.login"
        label="Email / Nomor Induk"
        name="login"
        type="texts"
      />
      <Password
        v-model="state.formData.password"
        :label="`Password`"
        placeholder="Passowrd"
        toggleMask
        name="password"
        type="password"
        fluid
        :feedback="false"
      />
      <Button v-slot="slotProps" as-child>
        <button
          v-bind="slotProps.a11yAttrs"
          type="submit"
          class="bg-primary font-semibold text-lg text-white rounded-md py-2 px-4"
        >
          {{ string.labelLogin }}
        </button>
      </Button>
    </Form>
    <img :src="img1" alt="img1" class="absolute bottom-0 left-0 w-1/2 select-none" />
    <img :src="img2" alt="img2" class="absolute top-0 right-0 w-1/4" />
  </Column>
</template>

<style scoped>
#loginPage {
  background-image: url('@/assets/bg_login.png');
  background-size: cover;
  background-position: center;
}
</style>
