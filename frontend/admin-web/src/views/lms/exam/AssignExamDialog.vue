<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { computed, watch } from 'vue'

import SelectField from '@/components/common/SelectField.vue'

import { useExamViewModel } from './useExamViewModel'

const { state, getSubClassroomSubjects, assignExam } = useExamViewModel()

const visible = computed({
  get: () => state.assignDialogVisible,
  set: (v: boolean) => (state.assignDialogVisible = v),
})

watch(visible, (val) => {
  if (val) {
    getSubClassroomSubjects()
  }
})

const onSubmit = async () => {
  await assignExam()
}
</script>

<template>
  <Dialog
    v-model:visible="visible"
    modal
    header="Assign Ujian ke Guru"
    style="width: 500px"
    appendTo="body"
  >
    <div class="space-y-4">
      <SelectField
        v-model="state.selectedSubClassroom"
        name="subClassroomSubjectId"
        label="Mata Pelajaran - Kelas - Guru"
        :options="state.subjectOptions"
        filter
        required
        class="mt-10"
      />

      <div class="flex justify-end gap-2">
        <Button label="Batal" severity="secondary" @click="visible = false" />
        <Button label="Assign" :disabled="!state.selectedSubClassroom" @click="onSubmit" />
      </div>
    </div>
  </Dialog>
</template>
