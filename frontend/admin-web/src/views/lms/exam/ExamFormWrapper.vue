<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import type { ExamRequestBody } from '@/dto/examRequestBody'
import { ExamType } from '@/enums/examType.anum'
import { goToExamList } from '@/utils/examAction'

import ExamFormPage from './ExamFormPage.vue'
import { useExamViewModel } from './useExamViewModel'

const { addData, updateData, state, getAllExam } = useExamViewModel()
const route = useRoute()

const examForm = ref<ExamRequestBody | null>(null)
const mode = ref<'add' | 'edit'>('add')

onMounted(async () => {
  const idParam = route.params.id
  if (idParam) {
    mode.value = 'edit'
    if (!state.examsData || state.examsData.length === 0) {
      await getAllExam()
    }
    const id = Number(idParam)
    const exam = state.examsData.find((e) => e.id === id)
    if (exam) {
      examForm.value = {
        title: exam.title,
        examsType: exam.examsType,
        description: exam.description,
        subClassroomSubjectId: exam.subClassroomSubjectId,
        startDatetime: exam.startDatetime,
        endDatetime: exam.endDatetime,
        passingScore: exam.passingScore,
        isPublished: exam.isPublished,
        isShuffled: exam.isShuffled,
        termId: exam.termId,
      }
      state.selectedData = exam
    }
  } else {
    // add mode
    examForm.value = {
      title: '',
      examsType: ExamType.SUMATIF_AKHIR_SEMESTER,
      description: '',
      subClassroomSubjectId: 0,
      startDatetime: new Date().toISOString(),
      endDatetime: new Date().toISOString(),
      passingScore: undefined,
      isPublished: false,
      isShuffled: false,
      termId: 0,
    }
  }
})
const handleSubmit = async () => {
  if (mode.value === 'add') {
    await addData(examForm.value)
  } else if (state.selectedData) {
    await updateData(state.selectedData.id, examForm.value)
  }

  if (!state.formErrors || Object.keys(state.formErrors).length === 0) {
    goToExamList()
  }
}
</script>

<template>
  <ExamFormPage v-if="examForm" v-model="examForm" :mode="mode" @submit="handleSubmit" />
</template>
