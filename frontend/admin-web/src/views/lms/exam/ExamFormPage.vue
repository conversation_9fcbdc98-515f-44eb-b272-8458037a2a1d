<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ToggleButton } from 'primevue'
import { onMounted, ref, watch, computed, watchEffect } from 'vue'

import BaseCard from '@/components/common/BaseCard.vue'
import Column from '@/components/common/Column.vue'
import Row from '@/components/common/Row.vue'
import SelectField from '@/components/common/SelectField.vue'
import TextField from '@/components/common/TextField.vue'
import type { ExamRequestBody } from '@/dto/examRequestBody'
import { ExamTypeOptions } from '@/enums/examType.anum'
import { isAdminOrSuperUser } from '@/utils/examAction'

import { useExamViewModel } from './useExamViewModel'
import Questions from '../question/Questions.vue'

const props = defineProps<{
  modelValue: ExamRequestBody
  mode: 'add' | 'edit'
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: ExamRequestBody): void
  (e: 'submit', payload: ExamRequestBody): void
}>()

const { state, getSubClassroomSubjects, subjectOptions, backDashboard, getTerms, termOptions } =
  useExamViewModel()

const startDate = ref<Date | null>(
  props.modelValue.startDatetime ? new Date(props.modelValue.startDatetime) : null,
)
const endDate = ref<Date | null>(
  props.modelValue.endDatetime ? new Date(props.modelValue.endDatetime) : null,
)

function formatDateToSQL(date?: Date | null): string {
  if (!date) return ''
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
}

const form = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

watchEffect(() => {
  form.value.startDatetime = formatDateToSQL(startDate.value)
  form.value.endDatetime = formatDateToSQL(endDate.value)
})

watch(
  () => props.modelValue,
  (val) => {
    startDate.value = val.startDatetime ? new Date(val.startDatetime) : null
    endDate.value = val.endDatetime ? new Date(val.endDatetime) : null
  },
  { immediate: true, deep: true },
)

watch(
  () => state.selectedSubClassroom,
  (val) => {
    if (val) getSubClassroomSubjects()
  },
  { immediate: true },
)

onMounted(async () => {
  state.selectedSubClassroom = 1
  await getTerms()
  if (!form.value.termId && state.terms.length) {
    form.value.termId = state.terms[0].id
  }
})

onMounted(() => {
  state.selectedSubClassroom = 1
})

const handleSubmit = () => {
  emit('submit', form.value)
}
</script>

<template>
  <Column class="max-w-5xl w-full mx-auto px-4">
    <Column
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"
    >
      <Column>
        <h2 class="text-xl font-semibold text-gray-800">
          {{ mode === 'edit' ? 'Ubah Data Ujian' : 'Tambah Ujian Baru' }}
        </h2>
        <p class="text-sm text-gray-600">
          Silakan lengkapi form berikut untuk
          {{ mode === 'edit' ? 'memperbarui data ujian' : 'menambahkan ujian baru ke sistem' }}.
        </p>
      </Column>
      <Row class="gap-2">
        <Button
          v-if="mode === 'add'"
          label="Kembali"
          icon="pi pi-arrow-left"
          class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
          @click="backDashboard"
        />
        <Button
          @click="handleSubmit"
          class="text-white px-4 py-2 rounded-md shadow-md outline !outline-[#ff0048] !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f]"
        >
          {{ mode === 'edit' ? 'Simpan Perubahan' : 'Simpan Ujian' }}
        </Button>
      </Row>
    </Column>

    <BaseCard class="w-full max-w-5xl mb-5">
      <Column class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <SelectField
          v-model="form.termId"
          name="termId"
          label="Semester/Tahun Ajaran"
          :options="termOptions"
          required
        />
        <SelectField
          v-model="form.examsType"
          name="examsType"
          label="Tipe Ujian"
          :options="ExamTypeOptions"
          required
        />

        <TextField v-model="form.title" name="title" label="Judul Ujian" required />
        <SelectField
          v-model="form.subClassroomSubjectId"
          name="subClassroomSubjectId"
          :label="isAdminOrSuperUser() ? 'Mata Pelajaran - Kelas - Guru' : 'Mata Pelajaran - Kelas'"
          :options="subjectOptions"
          filter
          required
        />

        <DatePicker
          v-model="startDate"
          name="startDatetime"
          placeholder="Waktu Mulai"
          showIcon
          showTime
          hourFormat="24"
          dateFormat="yy-mm-dd"
          required
        />

        <DatePicker
          v-model="endDate"
          name="endDatetime"
          placeholder="Waktu Selesai"
          showIcon
          showTime
          hourFormat="24"
          dateFormat="yy-mm-dd"
          required
        />

        <TextField v-model="form.description" name="description" label="Deskripsi" />

        <!-- <NumberField v-model="form.passingScore" name="passingScore" label="Nilai KKM" /> -->
        <ToggleButton
          v-model="form.isShuffled"
          name="isShuffled"
          onLabel="Acak Soal"
          offLabel="Jangan Acak"
          onIcon="pi pi-refresh"
          offIcon="pi pi-times"
          class="w-full sm:w-48"
        />
      </Column>
    </BaseCard>

    <Questions v-if="mode === 'edit'" class="max-w-5xl" :type="'edit'" />
  </Column>
</template>
