<script setup lang="ts">
import Button from 'primevue/button'
import { onMounted } from 'vue'

import { ExamStatus, getExamStatus } from '@/enums/examStatus.enumm'
import { formatDateOnly, formatTimeOnly } from '@/utils/date.util'
import ExamDialog from '@/views/lms/exam/examList/ExamDialog.vue'

import { useExamListViewModel } from './useExamListViewModel'

const { state, filteredExams, examTabs, startExam, setActiveTab, confirmStartExam, getAllExam } =
  useExamListViewModel()

const isStartable = (status: string): boolean => {
  return [ExamStatus.READY, ExamStatus.IN_PROGRESS].includes(status as ExamStatus)
}

const fetchExams = async () => {
  await getAllExam()
}
onMounted(() => {
  fetchExams()
})
</script>

<template>
  <div class="max-w-7xl mx-auto">
    <h2 class="text-2xl font-semibold text-gray-800 mb-6">Daftar Ujian</h2>

    <div class="flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0">
      <button
        v-for="tab in examTabs"
        :key="tab.key"
        @click="setActiveTab(tab.key)"
        :class="[
          'w-full sm:w-auto px-4 py-2 rounded-t-lg font-semibold transition-all text-sm',
          state.activeTab === tab.key
            ? 'bg-[#f4034a] text-white'
            : 'bg-gray-200 text-gray-600 hover:bg-gray-300',
        ]"
      >
        {{ tab.label }}
      </button>
    </div>

    <div class="rounded-xl bg-white shadow-lg p-4 sm:p-6">
      <div
        v-if="filteredExams.length > 0"
        class="flex flex-col gap-4 max-h-[40rem] overflow-y-auto"
      >
        <div
          v-for="exam in filteredExams"
          :key="exam.id"
          class="flex flex-col md:flex-row justify-between md:items-center gap-4 rounded-xl p-4 sm:p-5 shadow-md border border-gray-200 hover:shadow-lg transition-all bg-white"
        >
          <div class="flex-1 space-y-1">
            <h3 class="text-base sm:text-lg font-bold text-gray-800">{{ exam.title }}</h3>
            <p class="text-sm sm:text-base text-gray-600 font-medium">
              {{ exam.teacher.name }} – {{ exam.subject.name }}
            </p>

            <div class="mt-2 space-y-2 text-sm text-gray-700">
              <div class="flex items-center gap-2">
                <i class="pi pi-calendar"></i>
                <span>{{ formatDateOnly(exam.startDatetime) }}</span>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <div class="flex items-center gap-2">
                  <i class="pi pi-clock"></i>
                  <span>Mulai: {{ formatTimeOnly(exam.startDatetime) }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <i class="pi pi-clock"></i>
                  <span>Selesai: {{ formatTimeOnly(exam.endDatetime) }}</span>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <i class="pi pi-file"></i>
                <span>{{ exam.questionsCount }} Soal</span>
              </div>
            </div>
          </div>

          <div class="w-full md:w-auto">
            <Button
              @click="startExam(exam.id)"
              :disabled="!isStartable(exam.status)"
              :class="[
                'w-full md:w-auto px-6 py-2 rounded-md text-white text-sm font-semibold transition-colors duration-200',
                isStartable(exam.status)
                  ? 'outline !outline-[#ff0048] !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f]'
                  : exam.status === ExamStatus.GRADED
                    ? '!bg-green-600 hover:!bg-green-700'
                    : '!bg-gray-400 cursor-not-allowed',
              ]"
            >
              {{ getExamStatus(exam) }}
            </Button>

            <ExamDialog
              v-if="state.selectedExam && state.showDialog"
              v-model="state.showDialog"
              :exam="state.selectedExam"
              @confirm="confirmStartExam"
            />
          </div>
        </div>
      </div>

      <div v-else class="text-center text-gray-500 py-10">Tidak ada ujian saat ini</div>
    </div>
  </div>
</template>
