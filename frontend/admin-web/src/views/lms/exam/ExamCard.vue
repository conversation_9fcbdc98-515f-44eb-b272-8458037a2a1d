<script setup lang="ts">
import { Button } from 'primevue'

import BaseCard from '@/components/common/BaseCard.vue'
import Column from '@/components/common/Column.vue'
import { getExamTypeLabel, type ExamType } from '@/enums/examType.anum'
import { formatDateOnly, formatTimeOnly } from '@/utils/date.util'
import { isAdminOrSuperUser } from '@/utils/examAction'

type MiniExam = {
  id: number | string
  title: string
  examsType?: ExamType
  description?: string | null
  isPublished?: boolean
  startDatetime?: string
  endDatetime?: string
  subClassroom?: { name: string }
  subject?: { name: string }
  teacher?: { name: string }
}

defineProps<{
  exam: MiniExam
}>()

const emit = defineEmits<{
  (e: 'publish'): void
  (e: 'unpublish'): void
  (e: 'edit'): void
  (e: 'delete'): void
  (e: 'viewResults'): void
}>()
</script>

<template>
  <BaseCard
    class="h-full flex flex-col justify-between cursor-pointer hover:shadow-md transition-shadow duration-200"
  >
    <!-- Header -->
    <template #title>
      <div class="mb-2">
        <h2 class="text-lg font-semibold text-gray-900 truncate">{{ exam.title }}</h2>
        <p class="text-sm text-gray-600 line-clamp-2">{{ exam.description }}</p>
      </div>
    </template>

    <!-- Subinfo -->
    <template #subtitle>
      <div class="space-y-1 text-sm text-gray-700">
        <div class="flex">
          <p v-if="exam.subClassroom">
            <span class="font-medium">Kelas:</span> {{ exam.subClassroom?.name }}
          </p>
          <p v-if="exam.teacher && isAdminOrSuperUser()">
            <span class="mx-3"> - </span>
            <span class="font-medium">Guru:</span> {{ exam.teacher?.name }}
          </p>
        </div>
        <p v-if="exam.subject">
          <span class="font-medium">Mata Pelajaran:</span> {{ exam.subject?.name }}
        </p>
        <p v-if="exam.examsType">
          <span class="font-medium">Tipe Ujian:</span> {{ getExamTypeLabel(exam.examsType) }}
        </p>
      </div>
    </template>

    <!-- Jadwal -->
    <Column
      v-if="exam.startDatetime && exam.endDatetime"
      class="text-sm text-gray-700 space-y-2 mt-3"
    >
      <div class="flex items-center gap-2">
        <i class="pi pi-calendar"></i>
        <span>{{ formatDateOnly(exam.startDatetime) }}</span> -
        <span>{{ formatDateOnly(exam.endDatetime) }}</span>
      </div>
      <div class="flex items-center gap-2">
        <i class="pi pi-clock"></i>
        <span>Mulai: {{ formatTimeOnly(exam.startDatetime) }}</span>
        -
        <i class="pi pi-clock"></i>
        <span>Selesai: {{ formatTimeOnly(exam.endDatetime) }}</span>
      </div>
    </Column>

    <!-- Aksi -->
    <template #actions>
      <div class="mt-4 flex flex-wrap gap-2">
        <Button
          v-if="!exam.isPublished"
          label="Publish"
          icon="pi pi-upload"
          iconPos="left"
          severity="success"
          outlined
          @click.stop="emit('publish')"
        />
        <Button
          v-else
          label="Unpublish"
          icon="pi pi-times"
          iconPos="left"
          severity="secondary"
          outlined
          @click.stop="emit('unpublish')"
        />

        <Button
          icon="pi pi-pencil"
          iconPos="left"
          severity="success"
          title="Edit"
          outlined
          @click.stop="emit('edit')"
        />

        <Button
          icon="pi pi-trash"
          iconPos="left"
          severity="danger"
          title="Hapus"
          outlined
          @click.stop="emit('delete')"
        />

        <Button
          icon="pi pi-eye"
          iconPos="left"
          severity="info"
          title="Lihat Daftar Siswa"
          outlined
          @click.stop="emit('viewResults')"
        />

        <slot name="actions-append" />
      </div>
    </template>
  </BaseCard>
</template>
