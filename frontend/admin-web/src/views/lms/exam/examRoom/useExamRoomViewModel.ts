import debounce from 'lodash/debounce'
import { useToast } from 'primevue/usetoast'
import { computed, watch, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import ExamServices from '@/services/exam.service'
import ExamSessionServices from '@/services/examSession.service'
import { showToast } from '@/utils/toast.util'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useExamRoomState } from './useExamRoomState'

export const useExamRoomViewModel = () => {
  const state = useExamRoomState()
  const route = useRoute()
  const router = useRouter()
  const toast = useToast()

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  let pollingInterval: ReturnType<typeof setInterval> | null = null

  const currentQuestion = computed(() => state.questions[state.currentIndex])
  const selectedOptionId = computed(() => state.selectedAnswers[currentQuestion.value.id || 0])
  const canSubmit = computed(
    () => Object.keys(state.selectedAnswers).length === state.questions.length,
  )

  const essayAnswer = computed({
    get: () => state.questions[state.currentIndex]?.essayAnswer ?? '',
    set: (val: string) => {
      if (state.questions[state.currentIndex]) {
        state.questions[state.currentIndex].essayAnswer = val
        const qid = state.questions[state.currentIndex].id
        debouncedSubmitEssayAnswer(qid, val)
      }
    },
  })

  const formattedTime = computed(() => {
    const hours = String(Math.floor(state.timeLeft / 3600)).padStart(2, '0')
    const minutes = String(Math.floor((state.timeLeft % 3600) / 60)).padStart(2, '0')
    const seconds = String(state.timeLeft % 60).padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  })

  const isAnswered = (id: number) => {
    const q = state.questions.find((q) => q.id === id)
    return !!(q?.selectedOptionId || q?.essayAnswer || q?.mediaAnswerUrl)
  }

  const goTo = (index: number) => {
    state.currentIndex = index
  }

  const prev = () => {
    if (state.currentIndex > 0) state.currentIndex--
  }

  const nextOrFinish = () => {
    if (state.currentIndex < state.questions.length - 1) {
      state.currentIndex++
    } else {
      state.showConfirm = true
    }
  }

  const loadExamSessionId = () => {
    state.examSessionId = Number(route.params.id)
  }

  const loadExamQuestion = async () => {
    await handleRequest(
      async () => {
        const { data } = await ExamSessionServices.getExamAttemptById(state.examSessionId)
        const attempt = data.data

        state.attemptData = attempt
        state.questionIds = attempt.questions.map((q: any) => q.answerId)
        state.questions = attempt.questions.map((q: any) => ({
          ...q,
          id: q.answerId,
        }))

        for (const question of attempt.questions) {
          await loadQuestionAnswer(question.answerId)
        }

        const now = new Date()
        const endTime = new Date(attempt.exam.endDatetime)
        const diffSeconds = Math.floor((endTime.getTime() - now.getTime()) / 1000)

        if (diffSeconds <= 0) {
          showToast(toast, 'warn', 'Ujian Expired', 'Waktu ujian sudah habis.')
          router.push({ name: 'student.dashboard' })
          return
        }

        state.timeLeft = diffSeconds
        startPollingAttemptStatus()
      },
      undefined,
      'Gagal memuat data ujian.',
    )
  }

  watch(
    () => state.attemptData,
    (attempt) => {
      if (attempt && attempt.submitDatetime !== null && attempt.status === 'completed') {
        nextTick(() => {
          showToast(toast, 'info', 'Harap hubungi Guru ujian.', 'Ujian Kamu dikunci.')
          router.push({ name: 'student.dashboard' })
        })
      }
    },
    { immediate: true },
  )

  const loadQuestionAnswer = async (questionId: number) => {
    await handleRequest(
      async () => {
        const { data } = await ExamSessionServices.getExamQuestionById(
          state.examSessionId,
          questionId,
        )
        const fullQuestion = data.data

        console.log('Data', fullQuestion)

        const target = state.questions.find((q) => q.answerId === fullQuestion.id)
        if (target) {
          target.options = fullQuestion.question.options
          target.questionType = fullQuestion.question.questionType
          target.essayAnswer = fullQuestion.essayAnswer
          target.mediaQuestionsUrl = fullQuestion.question?.mediaQuestionsUrl ?? null
          target.mediaAnswerUrl = fullQuestion.mediaAnswerUrl ?? null
        }
      },
      undefined,
      'Gagal memuat pilihan jawaban.',
    )
  }

  // === Upload & Hapus Gambar Essay ===
  const isValidImage = (file: File) =>
    ['image/jpeg', 'image/png'].includes(file.type) && file.size <= 2 * 1024 * 1024

  const setPendingImage = (questionId: number, file: File) => {
    if (!isValidImage(file)) {
      showToast(toast, 'warn', 'File tidak valid', 'Gunakan JPG/PNG, maksimal 2 MB.')
      return
    }
    const url = URL.createObjectURL(file)
    state.pendingUploads[questionId] = { file, url }
  }

  const clearPendingImage = (questionId: number) => {
    const item = state.pendingUploads[questionId]
    if (item?.url) URL.revokeObjectURL(item.url)
    delete state.pendingUploads[questionId]
  }

  const uploadEssayImage = async (questionId: number, file: File) => {
    const question = state.questions.find((q) => q.id === questionId)
    if (!question) return

    if (!isValidImage(file)) {
      showToast(toast, 'warn', 'File tidak valid', 'Gunakan JPG/PNG, maksimal 2 MB.')
      return
    }

    const form = new FormData()
    form.append('media_answer', file)

    const answer = essayAnswer.value
    if (answer) {
      form.append('essay_answer', answer)
    }

    await handleRequest(
      async () => {
        for (const [key, value] of form.entries()) {
          console.log(key, value)
        }

        await ExamSessionServices.answerExamQuestion(state.examSessionId, questionId, form)
        await loadQuestionAnswer(questionId)
        clearPendingImage(questionId)
        showToast(toast, 'success', 'Terunggah', 'Gambar jawaban berhasil diunggah.')
      },
      undefined,
      'Gagal mengunggah gambar.',
    )
  }

  const removeEssayImage = async (questionId: number) => {
    await handleRequest(
      async () => {
        const form = new FormData()
        form.append('remove_media', '1')
        await ExamSessionServices.answerExamQuestion(state.examSessionId, questionId, form)

        const target = state.questions.find((q) => q.id === questionId)
        if (target) target.mediaAnswerUrl = null
      },
      'Gambar jawaban dihapus.',
      'Gagal menghapus gambar.',
    )
  }

  const startPollingAttemptStatus = () => {
    if (pollingInterval) clearInterval(pollingInterval)

    pollingInterval = setInterval(async () => {
      try {
        if (!state.examSessionId) return
        const { data } = await ExamSessionServices.getExamAttemptById(state.examSessionId)
        state.attemptData = data.data
      } catch (error) {
        console.error('Polling exam attempt failed:', error)
      }
    }, 5000)
  }

  let timerInterval: ReturnType<typeof setInterval> | null = null

  watch(
    () => state.timeLeft,
    async (newVal) => {
      if (newVal <= 0) {
        showToast(toast, 'warn', 'Ujian Expired', 'Waktu ujian sudah habis.')

        if (timerInterval) {
          clearInterval(timerInterval)
          timerInterval = null
        }
        router.push({ name: 'student.dashboard' })
        await submitExam()
      }
    },
  )

  const startTimer = () => {
    if (timerInterval) clearInterval(timerInterval)

    timerInterval = setInterval(() => {
      if (state.timeLeft > 0) {
        state.timeLeft--
      } else {
        if (timerInterval) {
          clearInterval(timerInterval)
          timerInterval = null
        }
      }
    }, 1000)
  }

  onUnmounted(() => {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
    }
  })

  const selectAnswer = async (questionId: number, optionId: number) => {
    const question = state.questions.find((q) => q.id === questionId)
    if (!question) return

    question.selectedOptionId = optionId
    state.selectedAnswers[questionId] = optionId

    await handleRequest(
      async () => {
        await ExamSessionServices.answerExamQuestion(state.examSessionId, questionId, {
          answerOptionId: optionId,
        })
      },
      undefined,
      'Gagal menyimpan jawaban.',
    )
  }

  const submitEssayAnswer = async (questionId: number, essayAnswer: string) => {
    const question = state.questions.find((q) => q.id === questionId)
    if (!question) return

    question.essayAnswer = essayAnswer

    await handleRequest(
      async () => {
        await ExamSessionServices.answerExamQuestion(state.examSessionId, questionId, {
          essayAnswer,
        })
      },
      undefined,
      'Gagal menyimpan jawaban esai.',
    )
  }

  const debouncedSubmitEssayAnswer = debounce((questionId: number, essayAnswer: string) => {
    submitEssayAnswer(questionId, essayAnswer)
  }, 1000)

  const submitExam = async () => {
    state.showConfirm = false
    const examId = state.attemptData?.exam?.id

    if (!examId) {
      showToast(toast, 'error', 'Gagal', 'ID ujian tidak ditemukan, submit dibatalkan.')
      router.push('/student')
      return
    }

    await handleRequest(
      async () => {
        await ExamServices.submitExam(examId)
        router.push('/student')
      },
      'Ujian berhasil disubmit.',
      'Gagal submit ujian.',
    )
  }

  return {
    state,
    currentQuestion,
    selectedOptionId,
    canSubmit,
    formattedTime,
    essayAnswer,
    isAnswered,
    loadExamSessionId,
    loadExamQuestion,
    goTo,
    prev,
    nextOrFinish,
    selectAnswer,
    submitExam,
    startTimer,
    submitEssayAnswer,
    uploadEssayImage,
    removeEssayImage,
    setPendingImage,
    clearPendingImage,
    debouncedSubmitEssayAnswer,
  }
}
