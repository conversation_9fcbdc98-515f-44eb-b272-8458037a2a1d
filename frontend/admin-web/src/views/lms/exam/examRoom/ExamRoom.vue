<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { computed, onMounted } from 'vue'

import raisingHand from '@/assets/raising_hand.svg'
import ChoiceList from '@/components/common/ChoiceList.vue'
import MediaPicker from '@/components/common/MediaPicker.vue'
import MediaPreview from '@/components/common/MediaPreview.vue'
import QuestionNavigator from '@/components/common/QuestionNavigator.vue'
import QuestionTitle from '@/components/common/QuestionTitle.vue'
import TextArea from '@/components/common/TextArea.vue'
import { QuestionType } from '@/enums/questionType.enum'

import { useExamRoomViewModel } from './useExamRoomViewModel'

const viewModel = useExamRoomViewModel()
const state = viewModel.state

const effectivePreviewUrl = computed(() => viewModel.currentQuestion.value.mediaQuestionsUrl)
const pending = computed(() => state.pendingUploads[viewModel.currentQuestion.value?.id || 0])
const pendingKind = computed<'image' | 'video' | 'audio' | undefined>(() => {
  const t = pending.value?.file?.type || ''
  if (t.startsWith('image/')) return 'image'
  if (t.startsWith('video/')) return 'video'
  if (t.startsWith('audio/')) return 'audio'
  return undefined
})

const selectedId = computed(
  () =>
    viewModel.selectedOptionId.value ?? viewModel.currentQuestion.value.selectedOptionId ?? null,
)

onMounted(async () => {
  await viewModel.loadExamSessionId()
  await viewModel.loadExamQuestion()
  viewModel.startTimer()
})
</script>

<template>
  <div class="max-w-full mx-auto px-4 py-6">
    <!-- Header -->
    <div class="space-y-1">
      <span class="text-2xl font-bold">{{ state.attemptData?.exam?.title }}</span>
    </div>

    <!-- Timer -->
    <div class="flex justify-end mb-4">
      <div
        class="bg-red-100 px-4 py-2 rounded-full text-red-600 font-semibold text-base flex items-center"
      >
        ⏰ {{ viewModel.formattedTime }}
      </div>
    </div>

    <div class="grid lg:grid-cols-12 gap-6 mt-6">
      <!-- Bagian Soal -->
      <div class="lg:col-span-9 space-y-6">
        <div class="bg-white rounded-xl shadow p-6">
          <div
            v-if="!viewModel.currentQuestion || state.questions.length === 0"
            class="text-center py-8"
          >
            <p>Memuat soal...</p>
          </div>

          <!-- Essay -->
          <div v-else-if="viewModel.currentQuestion?.value?.questionType === QuestionType.ESSAY">
            <QuestionTitle
              :index="state.currentIndex + 1"
              :html="viewModel.currentQuestion.value.content"
              :media-url="effectivePreviewUrl"
            />

            <TextArea
              v-model="viewModel.state.questions[viewModel.state.currentIndex].essayAnswer"
              :label="`Jawab Esai`"
              :name="`essayAnswer-${viewModel.state.questions[viewModel.state.currentIndex].id}`"
              class="w-full"
              rows="5"
              @input="
                (e: any) =>
                  viewModel.debouncedSubmitEssayAnswer(
                    viewModel.state.questions[viewModel.state.currentIndex].id,
                    e.target.value,
                  )
              "
            />

            <!-- Pending Upload -->
            <div v-if="pending" class="space-y-3 mt-4">
              <div class="text-sm text-gray-600">Preview lampiran (belum diunggah):</div>
              <MediaPreview :url="pending.url" :kind="pendingKind" />
              <div class="flex gap-2">
                <Button
                  label="Unggah"
                  icon="pi pi-upload"
                  @click="
                    pending?.file &&
                    viewModel.uploadEssayImage(viewModel.currentQuestion.value.id, pending.file)
                  "
                />
                <Button
                  label="Batal"
                  severity="secondary"
                  outlined
                  @click="viewModel.clearPendingImage(viewModel.currentQuestion?.value.id)"
                />
              </div>
            </div>

            <!-- No Pending Upload -->
            <div v-else class="space-y-3 mt-4">
              <div v-if="viewModel.currentQuestion?.value?.mediaAnswerUrl" class="space-y-2">
                <div class="text-sm text-gray-600">Lampiran terkirim:</div>
                <MediaPreview
                  :url="viewModel.currentQuestion.value.mediaAnswerUrl"
                  :alt="'Lampiran Jawaban'"
                />
                <div class="flex gap-2">
                  <MediaPicker
                    @select="
                      (file) => viewModel.setPendingImage(viewModel.currentQuestion?.value.id, file)
                    "
                  />
                  <Button
                    label="Hapus"
                    severity="danger"
                    text
                    @click="viewModel.removeEssayImage(viewModel.currentQuestion.value.id)"
                  />
                </div>
              </div>
              <div v-else class="flex items-center gap-3">
                <MediaPicker
                  @select="
                    (file) => viewModel.setPendingImage(viewModel.currentQuestion.value.id, file)
                  "
                />
                <span class="text-xs text-gray-500">JPG/PNG · maks 2 MB</span>
              </div>
            </div>
          </div>

          <!-- Multiple Choice -->
          <div
            v-else-if="
              viewModel.currentQuestion?.value?.questionType === QuestionType.MULTIPLE_CHOICE
            "
          >
            <QuestionTitle
              :index="state.currentIndex + 1"
              :html="viewModel.currentQuestion.value.content"
              :media-url="effectivePreviewUrl"
            />

            <ChoiceList
              :options="viewModel.currentQuestion.value.options"
              :selected-id="selectedId"
              @select="(id) => viewModel.selectAnswer(viewModel.currentQuestion.value.id, id)"
            />
          </div>
        </div>

        <!-- Navigasi -->
        <div class="flex flex-col sm:flex-row justify-between gap-3">
          <Button
            label="Sebelumnya"
            outlined
            @click="viewModel.prev"
            :disabled="state.currentIndex === 0"
            class="w-full sm:w-auto"
          />
          <Button
            :label="
              state.currentIndex === state.questions.length - 1 ? 'Selesai Ujian' : 'Selanjutnya'
            "
            @click="viewModel.nextOrFinish"
            :severity="state.currentIndex === state.questions.length - 1 ? 'danger' : 'primary'"
            class="w-full sm:w-auto"
          />
        </div>
      </div>

      <!-- Navigasi Soal -->
      <div class="lg:col-span-3">
        <QuestionNavigator
          :questions="state.questions"
          :current-index="state.currentIndex"
          :is-answered="viewModel.isAnswered"
          @go-to="viewModel.goTo"
        />
      </div>
    </div>

    <!-- Dialog Konfirmasi -->
    <Dialog
      v-model:visible="state.showConfirm"
      modal
      header="Konfirmasi"
      :style="{ width: '350px' }"
    >
      <div class="text-center space-y-4">
        <img :src="raisingHand" alt="Confirm" class="w-24 mx-auto" />
        <p>Apakah anda yakin ingin menyelesaikan ujian ini?</p>
        <Button label="Selesai" severity="danger" class="w-full" @click="viewModel.submitExam" />
      </div>
    </Dialog>
  </div>
</template>
