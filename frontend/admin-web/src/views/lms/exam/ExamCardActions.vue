<script setup lang="ts">
import Button from 'primevue/button'
import <PERSON>u from 'primevue/menu'
import { ref } from 'vue'

import { useAuthStore } from '@/stores/auth'

// Emit event-event
const emit = defineEmits<{
  (e: 'duplicate'): void
  (e: 'assignExamToTeacher'): void
  (e: 'exportKartuSoal'): void
  (e: 'exportQuestionsGrid'): void
}>()

// Referensi untuk menu
const menu = ref()

// Ambil role dari auth store
const auth = useAuthStore()
const roles = (auth.roles || []).map((r: any) => String(r).toLowerCase())

// Cek role
const isTeacher = roles.includes('teacher')
const isAdminOrSuperUser =
  roles.includes('school_admin') ||
  roles.includes('superadmin') ||
  roles.includes('foundation_admin')

// Inisialisasi item menu berdasarkan role
const items = ref<any[]>([])

if (isTeacher) {
  items.value = [
    {
      label: 'Export Kartu soal',
      icon: 'pi pi-download',
      command: () => emit('exportKartuSoal'),
    },
    {
      label: 'Export Kisi Kisi',
      icon: 'pi pi-download',
      command: () => emit('exportQuestionsGrid'),
    },
  ]
} else if (isAdminOrSuperUser) {
  items.value = [
    {
      label: 'Duplikasi',
      icon: 'pi pi-copy',
      command: () => emit('duplicate'),
    },
    {
      label: 'Assign',
      icon: 'pi pi-user-plus',
      command: () => emit('assignExamToTeacher'),
    },
    {
      label: 'Export Kartu soal',
      icon: 'pi pi-download',
      command: () => emit('exportKartuSoal'),
    },
    {
      label: 'Export Kisi Kisi',
      icon: 'pi pi-download',
      command: () => emit('exportQuestionsGrid'),
    },
  ]
}
</script>

<template>
  <div class="ml-auto">
    <Button
      icon="pi pi-ellipsis-v"
      text
      rounded
      aria-haspopup="true"
      aria-controls="exam-actions-menu"
      @click="menu?.toggle($event)"
      title="Aksi lainnya"
    />
    <Menu id="exam-actions-menu" ref="menu" :model="items" :popup="true" :appendTo="'body'" />
  </div>
</template>
