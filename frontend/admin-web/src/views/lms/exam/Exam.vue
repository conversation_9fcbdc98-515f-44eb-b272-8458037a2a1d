<script setup lang="ts">
import { Button } from 'primevue'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

import AdminSection from '@/components/admin/AdminSection.vue'
import DeleteConfirmation from '@/components/common/DeleteConfirmation.vue'
import Row from '@/components/common/Row.vue'
import SearchField from '@/components/common/SearchField.vue'
import { useTableSearch } from '@/components/common/useTableSearch'
import { isAdminOrSuperUser } from '@/utils/examAction'

import AssignExamDialog from './AssignExamDialog.vue'
import ExamCard from './ExamCard.vue'
import ExamCardActions from './ExamCardActions.vue'
import { useExamViewModel } from './useExamViewModel'

const viewModel = useExamViewModel()
const state = viewModel.state
const { getSearch } = useTableSearch()

const scrollContainer = ref<HTMLElement | null>(null)
const infiniteScrollTrigger = ref<HTMLElement | null>(null)

let observer: IntersectionObserver | null = null
onMounted(async () => {
  const search = getSearch('examsData')
  await viewModel.getAllExam({ search })

  observer = new IntersectionObserver(
    async (entries) => {
      const entry = entries[0]
      const canLoadMore = state.examsData.length < state.totalRecords
      if (entry.isIntersecting && !state.loadingMore && canLoadMore) {
        state.loadingMore = true
        state.page += 1
        await viewModel.getAllExam({ search }, true)
        state.loadingMore = false
      }
    },
    { root: scrollContainer.value, threshold: 1.0 },
  )
  if (infiniteScrollTrigger.value) observer.observe(infiniteScrollTrigger.value)
})

onBeforeUnmount(() => observer?.disconnect())

watch(
  () => getSearch('examsData'),
  async (newSearch) => {
    state.page = 1
    await viewModel.getAllExam({ search: newSearch }, false)
  },
)
</script>

<template>
  <AdminSection title="Exam Management" class="max-w-7xl w-full mx-auto">
    <!-- Header -->
    <Row class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-4">
      <Row class="gap-4">
        <Button
          v-if="!isAdminOrSuperUser()"
          label="Kembali"
          icon="pi pi-arrow-left"
          class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
          @click="viewModel.backDashboard()"
        />
        <Button
          label="Buat Ujian Baru"
          class="text-white rounded-md shadow-md outline !outline-[#ff0048] !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f]"
          @click="viewModel.showForm()"
        />
      </Row>
      <SearchField table-name="examsData" />
    </Row>

    <!-- Grid Card Ujian -->
    <div class="max-h-[700px] overflow-y-auto bg-gray-100 rounded-2xl p-3" ref="scrollContainer">
      <Row class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <ExamCard
          v-for="exam in state.examsData"
          :key="exam.id"
          :exam="exam"
          @publish="viewModel.togglePublishExam(exam.id, true)"
          @unpublish="viewModel.togglePublishExam(exam.id, false)"
          @edit="viewModel.showEditForm(exam)"
          @delete="viewModel.initiateDelete(exam.id, exam.title)"
          @viewResults="viewModel.viewExamResult(exam.id)"
        >
          <template #actions-append>
            <ExamCardActions
              @duplicate="viewModel.duplicateExam(exam.id)"
              @assignExamToTeacher="viewModel.popUpAssignExamToTeacher(exam.id)"
              @exportKartuSoal="viewModel.downloadQuestionsCard(exam.id)"
              @exportQuestionsGrid="viewModel.downloadQuestionsGrid(exam.id)"
            />
          </template>
        </ExamCard>
      </Row>

      <!-- Infinite scroll sentinel -->
      <div ref="infiniteScrollTrigger" class="h-10"></div>

      <Row v-if="state.loadingMore" class="flex justify-center py-4 text-md text-gray-500">
        Memuat data...
      </Row>

      <!-- Empty state -->
      <Row
        v-if="!state.loadingMore && state.examsData.length === 0"
        class="flex justify-center text-gray-500 text-md mb-6"
      >
        Tidak ada data ujian ditemukan.
      </Row>
    </div>
    <!-- Dialog Hapus -->
    <DeleteConfirmation
      v-model:showDialog="state.deleteDialogVisible"
      :idToDelete="state.idToDelete"
      :nameToDelete="state.deleteName"
      :message="state.deleteMessage"
      @delete="viewModel.deleteData()"
      @close="viewModel.onDeleteCanceled"
    />

    <AssignExamDialog
      v-model="state.assignDialogVisible"
      :exam-id="state.examId"
      @done="viewModel.getAllExam({ page: 1 }, false)"
    />
  </AdminSection>
</template>
