import { computed } from 'vue'

import type { ExamRequestBody } from '@/dto/examRequestBody'
import ExamServices from '@/services/exam.service'
import SubjectServices from '@/services/subject.service'
import TermServices from '@/services/term.service'
import type { Exam } from '@/types/exam.type'
import type { QueryParams } from '@/types/queryParams.type'
import { formatDateOnly } from '@/utils/date.util'
import {
  goToCreateExam,
  goToDashboard,
  goToEditExam,
  goToExamList,
  goToExamResult,
  isAdminOrSuperUser,
} from '@/utils/examAction'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useExamState } from './useExamState'

export const useExamViewModel = () => {
  const state = useExamState()
  const totalExam = computed(() => state.examsData.length)

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  const showForm = () => goToCreateExam()
  const showEditForm = (exam: Exam) => goToEditExam(exam.id)
  const viewExamResult = (id: number) => goToExamResult(id)
  const backDashboard = () => goToDashboard()
  const backExam = () => goToExamList()

  const closeForm = () => {
    state.selectedData = undefined
    state.showForm = false
  }

  const onDeleteCanceled = () => {
    state.deleteDialogVisible = false
  }

  const popUpAssignExamToTeacher = (id: number) => {
    state.examId = id
    state.assignDialogVisible = true
  }

  const assignExam = async () => {
    if (!state.examId || !state.selectedSubClassroom) return
    try {
      await ExamServices.assignExamToSubClassroomSubject(state.examId, state.selectedSubClassroom)
      alert('Berhasil assign exam')
      state.assignDialogVisible = false
    } catch (error) {
      console.error('Gagal assign exam', error)
      alert('Gagal assign exam')
    }
  }

  const addData = async (data: ExamRequestBody) => {
    await handleRequest(
      async () => {
        await ExamServices.createExam(data)
        state.formErrors = null
        backExam()
      },
      'Data ujian berhasil ditambahkan',
      'Gagal menambahkan data ujian',
    )
  }

  const updateData = async (id: number, data: ExamRequestBody) => {
    await handleRequest(
      async () => {
        await ExamServices.updateExam(id, data)
        state.formErrors = null
        backExam()
      },
      'Data ujian berhasil diperbarui',
      'Gagal memperbarui data ujian',
    )
  }

  const initiateDelete = (id: number, title: string) => {
    state.idToDelete = id
    state.deleteName = title
    state.deleteMessage = `Apakah Anda yakin ingin menghapus data "${title}"?`
    state.deleteDialogVisible = true
  }

  const deleteData = async () => {
    if (state.idToDelete !== null) {
      await handleRequest(
        async () => {
          await ExamServices.deleteExam(state.idToDelete!)
          state.examsData = state.examsData.filter((exam) => exam.id !== state.idToDelete)
          state.deleteDialogVisible = false
        },
        'Data ujian berhasil dihapus',
        'Gagal menghapus data ujian',
      )
    }
  }

  const getAllExam = async (params?: QueryParams, append = false) => {
    const { page, limit } = state

    await handleRequest(
      async () => {
        const { data } = await ExamServices.getAllExams({ page, limit, ...params })

        if (append) {
          state.examsData = [...state.examsData, ...data.data]
        } else {
          state.examsData = data.data
        }

        state.totalRecords = data.totalRecords
      },
      undefined,
      'Gagal mengambil data ujian',
    )
  }

  const togglePublishExam = async (id: number, publish: boolean) => {
    await handleRequest(
      async () => {
        if (publish) {
          await ExamServices.publishExam(id)
        } else {
          await ExamServices.unPublishExam(id)
        }
        const exam = state.examsData.find((e) => e.id === id)
        if (exam) {
          exam.isPublished = publish
        }
      },
      `Ujian berhasil ${publish ? 'dipublikasikan' : 'dibatalkan publikasinya'}`,
      `Gagal ${publish ? 'mempublikasikan' : 'membatalkan publikasi'} ujian`,
    )
  }

  const getSubClassroomSubjects = async () => {
    await handleRequest(
      async () => {
        const { data } = await SubjectServices.assignedSubjects()

        state.subjectOptions = data.data.map((item: any) => {
          let label = `${item.subject?.name ?? 'Tanpa Nama'} - ${item.subClassroom.name ?? 'Tanpa Kelas'}`

          if (isAdminOrSuperUser()) {
            label += ` - ${item.teacher?.name ?? 'Belum assign Guru'}`
          }

          return {
            label,
            value: item.id,
          }
        })
      },
      undefined,
      'Gagal mengambil mata pelajaran kelas',
    )
  }

  const duplicateExam = async (examId: number) => {
    await handleRequest(
      async () => {
        await ExamServices.duplicateExam(examId)
        state.page = 1
        await getAllExam({}, false)
      },
      'Berhasil duplikasi ujian',
      'Gagal duplikasi ujian',
    )
  }

  const getSubClassroomSubjectsTeacher = async () => {
    await handleRequest(
      async () => {
        const { data } = await SubjectServices.assignedSubjects()
        state.subjectOptions = data.data.map((item: any) => ({
          label: `${item.teacher?.name ?? 'Tanpa Nama'} - ${item.subClassroom.name ?? 'Tanpa Kelas'}`,
          value: item.id,
        }))
      },
      'Berhasil assign Guru',
      'Gagal mengambil data guru',
    )
  }

  const downloadQuestionsCard = async (examId: number) => {
    const res = await ExamServices.downloadQuestionsCard(examId)

    // ambil filename dari header kalau ada
    const cd = (res.headers as any)['content-disposition'] as string | undefined
    let filename = `kartu-soal-exam-${examId}.xlsx`
    if (cd) {
      const m = /filename\*?=(?:UTF-8''|")?([^\";]+)\"?/i.exec(cd) || /filename=([^;]+)/i.exec(cd)
      if (m?.[1]) {
        const raw = m[1].replace(/^["']|["']$/g, '')
        try {
          filename = decodeURIComponent(raw)
        } catch {
          filename = raw
        }
      }
    }

    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    a.remove()
    URL.revokeObjectURL(url)
  }

  const downloadQuestionsGrid = async (examId: number) => {
    const res = await ExamServices.downloadQuestionsGrid(examId)

    // ambil filename dari header kalau ada
    const cd = (res.headers as any)['content-disposition'] as string | undefined
    let filename = `kisi-kisi-exam-${examId}.xlsx`
    if (cd) {
      const m = /filename\*?=(?:UTF-8''|")?([^\";]+)\"?/i.exec(cd) || /filename=([^;]+)/i.exec(cd)
      if (m?.[1]) {
        const raw = m[1].replace(/^["']|["']$/g, '')
        try {
          filename = decodeURIComponent(raw)
        } catch {
          filename = raw
        }
      }
    }

    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    a.remove()
    URL.revokeObjectURL(url)
  }

  const getTerms = async (params?: QueryParams) => {
    const { data } = await TermServices.getAllTerms(params)
    state.terms = data.data.map((t: any) => ({
      id: t.id,
      label: `${t.name} (${formatDateOnly(t.startDate)} s/d ${formatDateOnly(t.endDate)})`,
    }))
  }

  const termOptions = computed(() => state.terms.map((t) => ({ label: t.label, value: t.id })))

  return {
    state,

    showForm,
    showEditForm,
    closeForm,
    backDashboard,
    backExam,
    onDeleteCanceled,

    getTerms,
    termOptions,

    addData,
    updateData,
    deleteData,
    getAllExam,
    togglePublishExam,
    viewExamResult,
    assignExam,
    duplicateExam,
    popUpAssignExamToTeacher,
    getSubClassroomSubjectsTeacher,
    downloadQuestionsCard,
    downloadQuestionsGrid,

    getSubClassroomSubjects,
    subjectOptions: computed(() => state.subjectOptions),
    initiateDelete,

    totalExam,
  }
}
