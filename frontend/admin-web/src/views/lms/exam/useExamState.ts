import { reactive } from 'vue'

import type { Exam } from '@/types/exam.type'
import type { TranslatedErrorList } from '@/utils/errorTranslation'

const state = reactive({
  formType: 'add' as 'add' | 'edit',
  showForm: false,
  selectedSubClassroom: null as number | null,
  selectedData: undefined as Exam | undefined,

  examsData: [] as Exam[],
  totalRecords: 0,
  page: 1,
  limit: 9,
  loadingMore: false,

  assignDialogVisible: false,
  examId: null as number | null,
  terms: [] as Array<{ id: number; label: string }>,

  totalExam: 0,
  totalSubmit: 0,
  totalGrade: 0,

  filters: {
    q: '' as string,
    teacherId: null as number | string | null,
    classroomId: null as number | string | null,
    subjectId: null as number | string | null,
    sort: 'start_datetime' as 'start_datetime' | 'end_datetime' | 'title' | 'created_at',
    order: 'asc' as 'asc' | 'desc',
  },

  formErrors: null as TranslatedErrorList | null,

  subjectOptions: [] as { label: string; value: number }[],

  deleteDialogVisible: false,
  idToDelete: null as number | null,
  deleteName: '',
  deleteMessage: '<PERSON><PERSON><PERSON>h Anda yakin ingin menghapus data ini?',
})

export const useExamState = () => state
