<script setup lang="ts">
import { Button, InputNumber } from 'primevue'
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'

import ChoiceList from '@/components/common/ChoiceList.vue'
import MathRenderer from '@/components/common/MathRenderer.vue'
import MediaPreview from '@/components/common/MediaPreview.vue'
import QuestionNavigator from '@/components/common/QuestionNavigator.vue'
import QuestionTitle from '@/components/common/QuestionTitle.vue'
import { QuestionType } from '@/enums/questionType.enum'
import { goToExamResult } from '@/utils/examAction'

import { useExamResultDetailViewModel } from './useExamResultDetailViewModel'

const {
  state,
  fetchAttemptDetail,
  loading,
  gradeEssay,
  goTo,
  prev,
  next,
  isAnswered,
  currentQuestion,
} = useExamResultDetailViewModel()

const questionHtml = computed(() => currentQuestion?.value?.question?.content || '')
const answerKeyEssay = computed(() => currentQuestion?.value?.question?.answerKeyEssay || '')
const questionPoint = computed(() => currentQuestion?.value?.question?.points || '')

const questionMedia = computed(() => currentQuestion?.value?.question?.mediaQuestionsUrl || null)

const route = useRoute()

const viewExamResult = () => {
  const examId = Number(route.params.examId)
  if (!examId) {
    console.error('Exam ID not found in route params')
    return
  }
  goToExamResult(examId)
}

onMounted(() => {
  fetchAttemptDetail()
})
</script>

<template>
  <div class="max-w-full mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center space-y-0 mb-4">
      <div>
        <h1 class="text-2xl font-bold">{{ state.attemptData?.exam?.title || 'Loading...' }}</h1>
        <p class="text-gray-600 text-base">
          {{ state.attemptData?.exam?.subject?.name || '-' }} -
          {{ state.attemptData?.exam?.subClassroom?.name || '-' }}<br />
          <span class="text-sm text-gray-500">
            Dikerjakan oleh: {{ state.attemptData?.student?.name || '-' }}
          </span>
        </p>
      </div>
      <div>
        <Button
          label="Kembali"
          icon="pi pi-arrow-left"
          class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
          @click="viewExamResult"
        />
      </div>
    </div>

    <div class="grid lg:grid-cols-12 gap-6 mt-6">
      <div class="lg:col-span-9 space-y-6">
        <div class="bg-white rounded-xl shadow p-6">
          <div v-if="loading" class="text-center py-8">
            <p>Memuat soal...</p>
          </div>

          <div v-else-if="!currentQuestion" class="text-center py-8">
            <p>Tidak ada soal untuk ditampilkan</p>
          </div>

          <div v-else>
            <div class="flex justify-between items-start mb-4">
              <QuestionTitle
                class="flex-1"
                :index="state.currentIndex + 1"
                :html="questionHtml"
                :media-url="questionMedia"
              />
              <div
                v-if="currentQuestion?.question?.questionType === QuestionType.ESSAY"
                class="flex flex-col items-center gap-2 ml-4"
              >
                <div>
                  <span class="text-sm font-medium whitespace-nowrap">
                    {{ currentQuestion.score ?? 0 }} / {{ questionPoint }}
                  </span>
                </div>
                <div>
                  <label class="text-sm font-medium whitespace-nowrap">Nilai: </label>
                  <InputNumber
                    type="number"
                    v-model="currentQuestion.score"
                    inputClass="w-32"
                    showButtons
                    :min="0"
                    :max="questionPoint"
                    @update:modelValue="(value) => gradeEssay(currentQuestion.id, value)"
                  />
                </div>
              </div>
            </div>

            <div class="space-y-3 mt-6">
              <div v-if="currentQuestion?.question?.questionType === QuestionType.MULTIPLE_CHOICE">
                <ChoiceList
                  :options="currentQuestion?.question?.options || []"
                  :selected-id="currentQuestion?.selectedOptionId || null"
                  :readonly="true"
                />
              </div>

              <div
                v-else-if="currentQuestion?.question?.questionType === QuestionType.ESSAY"
                class="mt-6 space-y-4"
              >
                <div class="p-4 border border-gray-300 rounded-xl bg-gray-50 text-gray-800">
                  {{ currentQuestion?.essayAnswer || 'Belum dijawab' }}
                </div>

                <span>Kunci Jawaban:</span>
                <div class="p-4 border border-gray-300 rounded-xl bg-gray-50 text-gray-800">
                  <MathRenderer
                    :html="answerKeyEssay || ''"
                    :auto-render="true"
                    :heuristic="true"
                  />
                </div>
                <MediaPreview :url="currentQuestion?.mediaAnswerUrl" />
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row justify-between gap-3 mt-6">
          <Button label="Sebelumnya" @click="prev" class="w-full sm:w-auto" />
          <Button label="Selanjutnya" @click="next" class="w-full sm:w-auto" />
        </div>
      </div>

      <!-- Navigasi Soal -->
      <div class="lg:col-span-3">
        <QuestionNavigator
          :questions="state.questions"
          :current-index="state.currentIndex"
          :is-answered="isAnswered"
          @go-to="goTo"
        />
      </div>
    </div>
  </div>
</template>
