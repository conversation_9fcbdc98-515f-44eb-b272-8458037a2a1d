import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

import ExamServices from '@/services/exam.service'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useExamResultDetailState } from './useExamResultDetailState'

export const useExamResultDetailViewModel = () => {
  const state = useExamResultDetailState()
  const route = useRoute()
  const loading = ref(false)

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  const goTo = (idx: number) => {
    state.currentIndex = idx
  }

  const isAnswered = (id: number) => {
    const q = state.questions.find((q) => q.id === id)
    return !!(q?.selectedOptionId || q?.essayAnswer || q.mediaAnswerUrl)
  }

  const prev = () => {
    if (state.currentIndex > 0) {
      state.currentIndex--
    }
  }

  const next = () => {
    if (state.currentIndex < state.questions.length - 1) {
      state.currentIndex++
    }
  }

  const currentQuestion = computed(() => {
    if (!state.questions.length) return null
    const q = state.questions[state.currentIndex]
    if (!q || !q.question) return null
    return q
  })

  const fetchAttemptDetail = async () => {
    const examId = Number(route.params.examId)
    const studentId = Number(route.params.studentId)
    const attemptId = Number(route.params.attemptId)
    state.examId = examId

    if (!examId || !studentId || !attemptId) {
      return
    }

    loading.value = true

    await handleRequest(
      async () => {
        const response = await ExamServices.getStudentAttempt(examId, studentId, attemptId)
        const data = response.data.data

        state.attemptData = {
          exam: data.exam,
          student: data.student,
          answers: data.answers,
        }

        state.questions = data.answers.map((answer: any) => ({
          id: answer.id,
          question: {
            id: answer.question?.id ?? null,
            content: answer.question?.content ?? '',
            answerKeyEssay: answer.question?.answerKeyEssay ?? '',
            points: answer.question?.points ?? 0,
            questionType: answer.question?.questionType ?? '',
            options: answer.question?.options ?? [],
            mediaQuestionsUrl: answer.question.mediaQuestionsUrl ?? '',
          },
          selectedOptionId: answer.selectedOptionId,
          essayAnswer: answer.essayAnswer,
          mediaAnswerUrl: answer.mediaAnswerUrl,
          score: answer.pointsAwarded,
        }))
      },
      undefined,
      'Gagal mengambil detail hasil ujian siswa',
    )

    loading.value = false
  }

  const gradeEssay = async (answerId: number, points: number) => {
    const attemptId = Number(route.params.attemptId)

    await handleRequest(
      async () => {
        await ExamServices.gradeEssay(attemptId, {
          examAttemptAnswersId: answerId,
          pointsAwarded: points,
        })

        const target = state.questions.find((q) => q.id === answerId)
        if (target) target.score = points
      },
      'Nilai berhasil disimpan',
      'Gagal menyimpan nilai',
    )
  }

  return {
    state,
    goTo,
    prev,
    isAnswered,
    next,
    currentQuestion,
    fetchAttemptDetail,
    gradeEssay,
    loading,
  }
}
