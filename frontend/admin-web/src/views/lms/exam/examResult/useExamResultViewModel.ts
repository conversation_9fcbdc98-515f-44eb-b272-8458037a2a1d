import router from '@/router'
import ExamServices from '@/services/exam.service'
import type { ExamResult } from '@/types/examResult.type'
import type { QueryParams } from '@/types/queryParams.type.ts'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useExamResultState } from './useExamResultState'

export const useExamResultViewModel = () => {
  const state = useExamResultState()

  function updateExamResultsPartial(newData: ExamResult[]) {
    const oldDataMap = new Map(state.examResultData.map((item) => [item.attemptId, item]))

    newData.forEach((newItem) => {
      const oldItem = oldDataMap.get(newItem.attemptId)

      if (!oldItem) {
        state.examResultData.push(newItem)
      } else {
        updateField(oldItem, newItem, ['score', 'correctCount', 'status', 'submitDatetime'])

        function updateField<T, K extends keyof T>(target: T, source: T, keys: K[]) {
          keys.forEach((key) => {
            if (target[key] !== source[key]) {
              target[key] = source[key]
            }
          })
        }
      }
    })
  }

  let pollingIntervalId: number | null = null

  function safeAssignCount(data: any) {
    if (
      typeof data?.notStarted === 'number' &&
      typeof data?.completed === 'number' &&
      typeof data?.inProgress === 'number'
    ) {
      state.notStarted = data.notStarted
      state.completed = data.completed
      state.inProgress = data.inProgress
    } else {
    }
  }

  const pollData = async () => {
    if (!state.examId) return

    try {
      const { data } = await ExamServices.resultExam(state.examId!)
      updateExamResultsPartial(data.data)

      const countData = await ExamServices.countExamStudents(state.examId!)
      console.log('📦 Count data dari polling:', countData.data)
      safeAssignCount(countData.data)
    } catch (error) {
      console.error('❌ Error saat polling:', error)
    }
  }

  const startPolling = () => {
    if (pollingIntervalId) return
    pollingIntervalId = window.setInterval(() => {
      pollData()
    }, 5000)
  }

  const stopPolling = () => {
    if (pollingIntervalId) {
      clearInterval(pollingIntervalId)
      pollingIntervalId = null
    }
  }

  const backExam = () => {
    stopPolling()
    router.push({ name: 'teacher.exams' })
  }

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  const getData = async (params?: QueryParams): Promise<void> => {
    if (!state.examId) return

    state.loading = true

    await handleRequest(
      async () => {
        const { data } = await ExamServices.resultExam(state.examId!, params)
        state.examResultData = data.data
        state.totalRecords = data.totalRecords
      },
      undefined,
      'Gagal mengambil data hasil ujian',
    )

    state.loading = false
  }

  const getCountExamStudent = async (): Promise<void> => {
    if (!state.examId) return

    state.loading = true

    await handleRequest(
      async () => {
        const { data } = await ExamServices.countExamStudents(state.examId!)
        console.log('📦 Count data dari getCountExamStudent:', data.data)
        safeAssignCount(data.data)
      },
      undefined,
      'Gagal mengambil jumlah siswa berdasarkan status ujian',
    )

    state.loading = false
  }

  const getExamResult = async (id: number): Promise<void> => {
    state.examId = id
    await getData()
    await getCountExamStudent()
    startPolling()
  }

  const gradeExam = async (id: number) => {
    await handleRequest(
      async () => {
        await ExamServices.gradeExam(id)
        state.tableKey++
      },
      'Berhasil Memberi nilai',
      'Gagal memberi nilai',
    )
  }

  const resetStudentExam = async (examId: number, studentId: number) => {
    await handleRequest(
      async () => {
        await ExamServices.resetStudentExam(examId, studentId)
        state.tableKey++
      },
      'Berhasil Mulai Ulang Ujian Siswa',
      'Gagal Mulai Ulang Ujian Siswa',
    )
  }

  const forceSubmitStudentExam = async (examId: number, studentId: number) => {
    await handleRequest(
      async () => {
        await ExamServices.forceSubmitStudentExam(examId, studentId)
        state.tableKey++
      },
      'Berhasil berhentikan ujian siswa',
      'Gagal berhentikan ujian siswa',
    )
  }

  return {
    getExamResult,
    gradeExam,
    getData,
    backExam,
    resetStudentExam,
    forceSubmitStudentExam,
    startPolling,
    stopPolling,
    state,
  }
}
