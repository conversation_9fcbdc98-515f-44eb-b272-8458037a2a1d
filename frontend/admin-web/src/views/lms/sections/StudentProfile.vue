<script setup lang="ts">
import Avatar from 'primevue/avatar'
import Button from 'primevue/button'
import Card from 'primevue/card'
import { RouterLink, useRouter } from 'vue-router'

import { useAuthStore } from '@/stores/auth'

const { user } = useAuthStore()
const router = useRouter()

const auth = useAuthStore()

function logout() {
  localStorage.removeItem('token')
  auth.$reset()
  router.push('/login')
}
</script>

<template>
  <section class="w-full max-w-sm md:max-w-md lg:max-w-lg mx-auto px-4">
    <h1 class="text-2xl font-semibold text-gray-800 mb-4 text-start md:text-left">Profil Siswa</h1>

    <Card class="overflow-hidden rounded-lg shadow-md">
      <template #header>
        <div
          class="p-6 text-center rounded-t-lg"
          style="background-image: linear-gradient(to right, #fd0047, #97002a)"
        >
          <Avatar
            label="RS"
            size="xlarge"
            shape="circle"
            class="bg-white !text-red-600 font-bold"
          />
        </div>
      </template>

      <template #content>
        <div class="p-4">
          <h3 class="font-semibold text-gray-800 mb-1 text-center md:text-left">
            {{ user?.name ?? 'Tidak ada Nama' }}
          </h3>
          <p class="text-sm text-gray-600 mb-4 text-center md:text-left">
            {{ user?.subClassroom?.name ?? 'Tidak ada Kelas' }}
          </p>

          <div class="space-y-2 border-t pt-3">
            <RouterLink :to="{ name: 'account.changePassword' }" class="no-underline text-inherit">
              <Button
                icon="pi pi-cog"
                label="Ubah Password"
                class="p-button-text w-full !text-red-600 !justify-start"
              />
            </RouterLink>
            <Button
              icon="pi pi-sign-out"
              label="Logout"
              class="p-button-text w-full !text-red-600 !justify-start"
              @click="logout"
            />
          </div>
        </div>
      </template>
    </Card>
  </section>
</template>
