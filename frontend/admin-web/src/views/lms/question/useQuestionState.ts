import { reactive } from 'vue'

import type { ExamQuestion } from '@/types/examQuestion.type'
import type { CompetencyPayload } from '@/types/question.type'
import type { TranslatedErrorList } from '@/utils/errorTranslation'

export const useQuestionState = () => {
  const state = reactive({
    formType: 'add' as 'add' | 'edit',
    showForm: false,
    selectedData: undefined as ExamQuestion | undefined,
    formErrors: null as TranslatedErrorList | null,
    showPreviewDialog: false,

    questionData: [] as ExamQuestion[],
    totalRecords: 0,
    tableKey: 0,

    loadingQuestions: false,

    showImportQuestions: false,

    showCompetencyDialog: false,
    competencyDialogMode: 'add' as 'add' | 'edit',
    editingQuestionIndex: null as number | null,
    pendingCompetency: {} as CompetencyPayload,

    // filters for copy dialog
    terms: [] as Array<{ id: number; label: string }>,
    termId: null as number | null,
    subClassroomSubjectId: null as number | null,

    // selections
    termOptions: [] as Array<{ label: string; value: number }>,
    subjectOptions: [] as Array<{ label: string; value: number }>,

    // copy dialog
    showCopyDialog: false,
    copy: {
      termId: null as number | null,
      sourceExamId: null as number | null,
      sourceExams: [] as Array<{ id: number; title: string; term?: string }>,
      sourceQuestions: [] as ExamQuestion[],
      selectedQuestionIds: new Set<number>(),
      loading: false,
      bulkSelect: false,
    },
  })
  return state
}
