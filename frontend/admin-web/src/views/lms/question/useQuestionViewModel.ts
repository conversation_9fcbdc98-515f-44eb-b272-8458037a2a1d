import { useToast } from 'primevue'

import type { CopyQuestionRequestBody } from '@/dto/copyQuestionRequestBody'
import { buildQuestionsFormData, type QuestionForm } from '@/dto/examQuestionRequestBody'
import { QuestionType } from '@/enums/questionType.enum'
import ExamServices from '@/services/exam.service'
import SubjectServices from '@/services/subject.service'
import TermServices from '@/services/term.service'
import { mapExamQuestionsResponse } from '@/types/examQuestion.type'
import type { QueryParams } from '@/types/queryParams.type'
import {
  makeBlankQuestion,
  patchCompetency,
  toPendingCompetency,
  type CompetencyPayload,
} from '@/types/question.type'
import { formatDateOnly } from '@/utils/date.util'
import { isAdminOrSuperUser } from '@/utils/examAction'
import { showToast } from '@/utils/toast.util'
import { useFormRequestHandler } from '@/utils/useFormRequestHandler'

import { useQuestionState } from './useQuestionState'

export const useQuestionViewModel = () => {
  const toast = useToast()
  const state = useQuestionState()

  const { handleRequest } = useFormRequestHandler((errors) => {
    state.formErrors = errors ?? null
  })

  const mapApiResponseToExamQuestions = (apiData: any[]) => mapExamQuestionsResponse(apiData)

  const getQuestionsByExam = async (examId: number) => {
    try {
      state.loadingQuestions = true
      const { data } = await ExamServices.getQuestionsByExam(examId)
      state.questionData = mapApiResponseToExamQuestions(data.data)
      state.totalRecords = data.total_records
    } catch (error: any) {
      const msg = error?.response?.data?.message || 'Gagal ambil data pertanyaan'
      showToast(toast, 'error', msg)
    } finally {
      state.loadingQuestions = false
    }
  }

  // === Dialog ===
  const openAddQuestionDialog = () => {
    state.pendingCompetency = state.pendingCompetency
    state.competencyDialogMode = 'add'
    state.editingQuestionIndex = null
    state.showCompetencyDialog = true
  }
  const addQuestion = () => openAddQuestionDialog()
  const confirmAddQuestion = (examId: number, payload: CompetencyPayload) => {
    state.showCompetencyDialog = false
    state.questionData.push(makeBlankQuestion(examId, state.questionData.length + 1, payload))
  }
  const openEditIndicatorDialog = (qIndex: number) => {
    const q = state.questionData[qIndex]
    state.pendingCompetency = toPendingCompetency(q)
    state.competencyDialogMode = 'edit'
    state.editingQuestionIndex = qIndex
    state.showCompetencyDialog = true
  }
  const confirmIndicatorDialog = (examId: number, payload: CompetencyPayload) => {
    state.showCompetencyDialog = false
    if (state.competencyDialogMode === 'add') {
      state.questionData.push(makeBlankQuestion(examId, state.questionData.length + 1, payload))
      return
    }
    const idx = state.editingQuestionIndex
    if (idx !== null && state.questionData[idx]) {
      patchCompetency(state.questionData[idx], payload)
    }
    state.editingQuestionIndex = null
  }

  const openPreviewDialog = () => {
    state.showPreviewDialog = true
  }
  const closePreviewDialog = () => {
    state.showPreviewDialog = false
  }
  const removeQuestion = (qIndex: number) => state.questionData.splice(qIndex, 1)

  // === Options (MCQ)
  const addOption = (qIndex: number) => {
    const question = state.questionData[qIndex]
    question?.answerOptions?.push({
      id: Date.now(),
      examQuestionId: question.id,
      content: '',
      isCorrect: false,
      orderIndex: question.answerOptions.length + 1,
      mediaAnswerOptionsUrl: null,
      mediaAnswerOptions: null,
      createdAt: '',
      updatedAt: '',
    })
  }
  const removeOption = (qIndex: number, oIndex: number) => {
    state.questionData[qIndex].answerOptions?.splice(oIndex, 1)
  }
  const updateOption = (qIndex: number, oIndex: number, value: string) => {
    state.questionData[qIndex].answerOptions[oIndex].content = value
  }
  const updateIsCorrect = (qIndex: number, oIndex: number, value: boolean) => {
    state.questionData[qIndex].answerOptions[oIndex].isCorrect = value
  }

  // === Media
  const handleMediaUpload = (qIndex: number, event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0] || null
    const question = state.questionData[qIndex]
    question.mediaQuestions = file
    question.mediaQuestionsUrl = file ? URL.createObjectURL(file) : null
  }
  const removeImage = (qIndex: number) => {
    const question = state.questionData[qIndex]
    question.mediaQuestions = null
    question.mediaQuestionsUrl = null
  }

  // === Validation & Submit bulk
  const validateQuestions = (): boolean => {
    for (const [i, q] of state.questionData.entries()) {
      if (q.questionType === QuestionType.MULTIPLE_CHOICE) {
        if (q.answerOptions.length < 2) {
          showToast(toast, 'error', `Soal ${i + 1} harus memiliki minimal 2 opsi jawaban.`)
          return false
        }
        const hasCorrect = q.answerOptions?.some((opt) => opt.isCorrect)
        if (!hasCorrect) {
          showToast(toast, 'error', `Soal ${i + 1} harus memiliki setidaknya satu jawaban benar.`)
          return false
        }
      }
    }
    return true
  }

  const submitQuestions = async (examId: number) => {
    if (!validateQuestions()) return
    const payload: QuestionForm[] = state.questionData.map((q, qIndex) => ({
      id: q.id,
      content: q.content,
      points: q.points ?? 0,
      answerKeyEssay: q.answerKeyEssay ?? '',
      orderIndex: q.orderIndex ?? qIndex + 1,
      questionType: q.questionType as unknown as QuestionForm['questionType'],
      kdNumber: q.kdNumber ?? null,
      learningOutcome: q.learningOutcome ?? null,
      competencyIndicator: q.competencyIndicator ?? null,
      levelKognitif: q.levelKognitif ?? null,
      mediaQuestions: q.mediaQuestions ?? null,
      answerOptions: (q.answerOptions || []).map((opt) => ({
        content: opt.content,
        isCorrect: !!opt.isCorrect,
        mediaAnswerOptions: opt.mediaAnswerOptions ?? null,
      })),
    }))
    const formData = buildQuestionsFormData(payload)
    formData.append('exam_id', String(examId))
    await handleRequest(
      async () => {
        await ExamServices.createQuestionsBulk(examId, formData)
        state.formErrors = {}
      },
      'Data Pertanyaan berhasil ditambahkan',
      'Gagal menambahkan data pertanyaan',
    )
  }

  // === Copy Soal (Dialog)
  const copySoal = async () => {
    // pastikan opsi term & subject sudah tersedia
    await initPage()

    state.copy.termId = null
    state.copy.sourceExamId = null
    state.copy.sourceExams = []
    state.copy.sourceQuestions = []
    state.copy.selectedQuestionIds = new Set()
    state.copy.bulkSelect = false
    state.showCopyDialog = true

    await fetchCopySources({
      term_id: state.termId ?? undefined,
      sub_classroom_subject_id: state.subClassroomSubjectId ?? undefined,
    })
  }

  const fetchCopySources = async (params?: QueryParams) => {
    state.copy.loading = true
    try {
      const { data } = await ExamServices.getCopySources(params)
      state.copy.sourceExams = data.data
    } catch (e: any) {
      showToast(toast, 'error', e?.response?.data?.message || 'Gagal memuat daftar exam')
    } finally {
      state.copy.loading = false
    }
  }

  const fetchSourceQuestions = async () => {
    if (!state.copy.sourceExamId) return
    state.copy.loading = true
    try {
      const { data } = await ExamServices.getQuestionsOfSource(state.copy.sourceExamId)
      state.copy.sourceQuestions = mapApiResponseToExamQuestions(data.data)
      state.copy.selectedQuestionIds = new Set()
      state.copy.bulkSelect = false
    } catch (e: any) {
      showToast(toast, 'error', e?.response?.data?.message || 'Gagal memuat soal sumber')
    } finally {
      state.copy.loading = false
    }
  }

  const toggleSelectAll = (checked: boolean) => {
    if (!checked) {
      state.copy.selectedQuestionIds = new Set()
      state.copy.bulkSelect = false
      return
    }
    const all = new Set<number>()
    state.copy.sourceQuestions.forEach((q) => {
      if (q.id != null) all.add(q.id)
    })
    state.copy.selectedQuestionIds = all
    state.copy.bulkSelect = true
  }

  const togglePick = (qid: number, checked: boolean) => {
    const next = new Set(state.copy.selectedQuestionIds)
    if (checked) next.add(qid)
    else next.delete(qid)
    state.copy.selectedQuestionIds = next

    if (!checked) state.copy.bulkSelect = false
    else {
      const totalIds = state.copy.sourceQuestions.filter((q) => q.id != null).length
      state.copy.bulkSelect = next.size === totalIds && totalIds > 0
    }
  }

  const submitCopy = async (targetExamId: number, body: CopyQuestionRequestBody) => {
    if (!state.copy.sourceExamId || state.copy.selectedQuestionIds.size === 0) {
      showToast(toast, 'warn', 'Pilih exam & minimal satu soal')
      return
    }
    await handleRequest(
      async () => {
        await ExamServices.copyQuestionsFromExam(targetExamId, body)
        state.showCopyDialog = false
        await getQuestionsByExam(targetExamId) // refresh
      },
      'Berhasil menyalin soal',
      'Gagal menyalin soal',
    )
  }

  // === Subjects & Terms
  const getSubClassroomSubjects = async () => {
    await handleRequest(
      async () => {
        const { data } = await SubjectServices.assignedSubjects()
        state.subjectOptions = data.data.map((item: any) => {
          let label = `${item.subject?.name ?? 'Tanpa Nama'} - ${item.subClassroom?.name ?? 'Tanpa Kelas'}`
          if (isAdminOrSuperUser()) {
            label += ` - ${item.teacher?.name ?? 'Belum assign Guru'}`
          }
          return { label, value: item.id }
        })
      },
      undefined,
      'Gagal mengambil mata pelajaran kelas',
    )
  }

  const getTerms = async () => {
    const { data } = await TermServices.getAllTerms()
    state.terms = data.data.map((t: any) => ({
      id: t.id,
      label: `${t.name} (${formatDateOnly(t.startDate)} s/d ${formatDateOnly(t.endDate)})`,
    }))
    state.termOptions = state.terms.map((t) => ({ label: t.label, value: t.id }))
  }

  const initPage = async () => {
    await getTerms()
    await getSubClassroomSubjects()
  }

  return {
    state,
    // questions
    getQuestionsByExam,
    addQuestion,
    openAddQuestionDialog,
    confirmAddQuestion,
    openEditIndicatorDialog,
    confirmIndicatorDialog,
    removeQuestion,
    addOption,
    removeOption,
    updateOption,
    updateIsCorrect,
    handleMediaUpload,
    removeImage,
    submitQuestions,

    copySoal,
    fetchCopySources,
    fetchSourceQuestions,
    toggleSelectAll,
    togglePick,
    submitCopy,
    openPreviewDialog,
    closePreviewDialog,

    openImportExamQuestionDialog: () => (state.showImportQuestions = true),

    getTerms,
    getSubClassroomSubjects,
    initPage,
  }
}
