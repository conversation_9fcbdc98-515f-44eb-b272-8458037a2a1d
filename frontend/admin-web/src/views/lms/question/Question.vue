<script setup lang="ts">
import Button from 'primevue/button'
import Card from 'primevue/card'
import InputNumber from 'primevue/inputnumber'
import { computed, onBeforeUnmount, ref, watch } from 'vue'

import EditorMath from '@/components/common/EditorMath.vue'
import MediaPicker from '@/components/common/MediaPicker.vue'
import MediaPreview from '@/components/common/MediaPreview.vue'
import SelectField from '@/components/common/SelectField.vue'
import { getLevelKognitifLabel } from '@/enums/levelKognitif.enum'
import { QuestionType, questionTypeOptions } from '@/enums/questionType.enum'
import type { ExamQuestion } from '@/types/examQuestion.type'
import type { CompetencyPayload } from '@/types/question.type'

import Answer from './Answer.vue'

const props = defineProps<{
  question: ExamQuestion
  qIndex: number
  formErrors?: Record<string, string[]>
}>()

const emit = defineEmits<{
  (e: 'update:content', value: string): void
  (e: 'update:answerKeyEssay', value: string): void
  (e: 'update:points', value: number): void
  (e: 'update:type', value: string): void
  (e: 'addOption'): void
  (e: 'removeQuestion'): void
  (e: 'removeOption', oIndex: number): void
  (e: 'update:answerOption', oIndex: number, value: string): void
  (e: 'update:isCorrect', oIndex: number, value: boolean): void
  (e: 'uploadMedia', file: File): void
  (e: 'update:indicators', payload: CompetencyPayload): void
  (e: 'editIndicators'): void
}>()

const localQuestionType = ref(props.question.questionType)

watch(localQuestionType, (newType: any) => {
  emit('update:type', newType)
})

const isMultipleChoice = computed(() => localQuestionType.value === QuestionType.MULTIPLE_CHOICE)

const fieldPath = (name: string) => `questions.${props.qIndex}.${name}`
const errorFor = (name: string) => props.formErrors?.[fieldPath(name)]?.[0] ?? ''

const localAnswerOptions = computed(() => props.question.answerOptions)

const correctIndex = computed({
  get: () => props.question.answerOptions?.findIndex((opt) => opt.isCorrect),
  set: (newIndex: number) => {
    props.question.answerOptions?.forEach((opt, i) => {
      opt.isCorrect = i === newIndex
    })
  },
})

const localPreviewUrl = ref<string | null>(null)
const localPreviewKind = ref<'image' | 'video' | 'audio' | null>(null)
let lastObjectUrl: string | null = null

const setLocalPreview = (file: File) => {
  if (lastObjectUrl) URL.revokeObjectURL(lastObjectUrl)
  const objUrl = URL.createObjectURL(file)
  lastObjectUrl = objUrl
  localPreviewUrl.value = objUrl

  if (file.type.startsWith('image/')) localPreviewKind.value = 'image'
  else if (file.type.startsWith('video/')) localPreviewKind.value = 'video'
  else if (file.type.startsWith('audio/')) localPreviewKind.value = 'audio'
  else localPreviewKind.value = null
}

const onPickMedia = (file: File) => {
  setLocalPreview(file)
  emit('uploadMedia', file)
}

const effectivePreviewUrl = computed(
  () => localPreviewUrl.value || props.question.mediaQuestionsUrl || null,
)
const effectiveKind = computed(() => localPreviewKind.value || null)

onBeforeUnmount(() => {
  if (lastObjectUrl) URL.revokeObjectURL(lastObjectUrl)
})
</script>

<template>
  <Card>
    <template #title>
      <div class="flex justify-between items-center gap-3 flex-wrap">
        <div class="flex items-center gap-3">
          <span class="text-xl font-bold">Soal {{ props.qIndex + 1 }}</span>
          <SelectField
            v-model="localQuestionType"
            name="questionType"
            label="Tipe Pertanyaan"
            :options="questionTypeOptions"
          />
        </div>

        <div class="flex items-center gap-3">
          <span class="text-gray-500 text-sm">Nilai:</span>
          <InputNumber
            v-model="props.question.points"
            :min="0"
            @input="emit('update:points', props.question.points)"
          />
          <Button
            icon="pi pi-trash"
            severity="danger"
            text
            rounded
            title="Hapus Soal"
            @click="emit('removeQuestion')"
          />
        </div>
      </div>
    </template>

    <template #content>
      <div class="bg-gray-50 p-5 rounded-2xl">
        <!-- Question Content -->
        <div class="mb-3">
          <EditorMath
            :key="`${props.question.id ?? 'new'}-${props.qIndex}`"
            :model-value="props.question.content"
            :height="200"
            @update:model-value="(val) => emit('update:content', val)"
          />
          <small v-if="errorFor('content')" class="text-red-500">{{ errorFor('content') }}</small>
        </div>

        <!-- Ringkasan Indikator -->
        <div class="grid gap-4 md:grid-cols-4 mb-2">
          <div>
            <span class="text-xs text-gray-500 block mb-1">Nomor KD</span>
            <div
              class="px-3 py-2 rounded bg-white border overflow-hidden text-ellipsis whitespace-nowrap max-w-full"
            >
              {{ props.question.kdNumber || '-' }}
            </div>
          </div>
          <div>
            <span class="text-xs text-gray-500 block mb-1">Capaian Pembelajaran / Elemen</span>
            <div
              class="px-3 py-2 rounded bg-white border overflow-hidden text-ellipsis whitespace-nowrap max-w-full"
            >
              {{ props.question.learningOutcome || '-' }}
            </div>
          </div>
          <div>
            <span class="text-xs text-gray-500 block mb-1">Indikator Pencapaian Kompetensi</span>
            <div
              class="px-3 py-2 rounded bg-white border overflow-hidden text-ellipsis whitespace-nowrap max-w-full"
            >
              {{ props.question.competencyIndicator || '-' }}
            </div>
          </div>
          <div>
            <span class="text-xs text-gray-500 block mb-1">Level Kognitif</span>
            <div
              class="px-3 py-2 rounded bg-white border overflow-hidden text-ellipsis whitespace-nowrap max-w-full"
            >
              {{ getLevelKognitifLabel(props.question.levelKognitif ?? '-') || '-' }}
            </div>
          </div>
        </div>

        <div class="flex justify-between items-center mb-4">
          <!-- Upload Media: ganti input file manual -> MediaPicker -->
          <div class="flex items-center gap-3 mb-4">
            <MediaPicker @select="onPickMedia" />
            <small v-if="errorFor('media_questions')" class="block text-red-500">
              {{ errorFor('media_questions') }}
            </small>
          </div>

          <!-- Edit Indikator -->
          <div>
            <Button
              label="Edit Ringkasan Indikator"
              icon="pi pi-pencil"
              severity="secondary"
              outlined
              @click="emit('editIndicators')"
            />
          </div>
        </div>

        <!-- Preview media soal -->
        <MediaPreview
          v-if="effectivePreviewUrl"
          :url="effectivePreviewUrl"
          :kind="effectiveKind || undefined"
          class="mb-4"
        />

        <div class="mt-5 bg-gray-200 p-5 rounded-2xl" v-if="!isMultipleChoice">
          <h3 for="answerKeyEssay" class="mb-3 text-black">Kunci Jawaban Esai:</h3>
          <EditorMath
            :key="`${props.question.id ?? 'new'}-${props.qIndex}`"
            :model-value="props.question.answerKeyEssay"
            :height="200"
            @update:model-value="(val) => emit('update:answerKeyEssay', val)"
          />
          <small v-if="errorFor('answerKeyEssay')" class="text-red-500">{{
            errorFor('answerKeyEssay')
          }}</small>
        </div>

        <div v-if="isMultipleChoice">
          <div class="flex flex-col gap-3 mb-4">
            <Answer
              v-for="(option, oIndex) in localAnswerOptions"
              :key="oIndex"
              :oIndex="oIndex"
              :answerOption="option"
              :correctIndex="correctIndex"
              @update:option="(val) => emit('update:answerOption', oIndex, val)"
              @update:isCorrect="(val) => (correctIndex = oIndex)"
              @remove="emit('removeOption', oIndex)"
            />
          </div>

          <div class="text-start mb-3">
            <Button
              label="Tambah Opsi"
              icon="pi pi-plus"
              severity="secondary"
              outlined
              @click="emit('addOption')"
            />
          </div>
        </div>
      </div>
    </template>
  </Card>
</template>
