<script setup lang="ts">
import Button from 'primevue/button'
import Card from 'primevue/card'
import Dialog from 'primevue/dialog'
import Divider from 'primevue/divider'
import { useToast } from 'primevue/usetoast'
import { computed, watch, ref } from 'vue'

import ChoiceList from '@/components/common/ChoiceList.vue'
import InfoText from '@/components/common/InfoText.vue'
import QuestionTitle from '@/components/common/QuestionTitle.vue'
import { QuestionType } from '@/enums/questionType.enum'
import ExamServices from '@/services/exam.service'
import { mapExamQuestionsResponse } from '@/types/examQuestion.type'
import { showToast } from '@/utils/toast.util'

interface Props {
  visible: boolean
  examId: number
}
const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', v: boolean): void
}>()

const toast = useToast()

const loading = ref(false)
const examTitle = ref<string>('')
const questions = ref<any[]>([])
const currentIndex = ref(0)

const currentQuestion = computed(() => questions.value[currentIndex.value] ?? null)
const currentSelectedId = computed<number | null>(
  () => currentQuestion.value?.selectedOptionId ?? null,
)
const effectivePreviewUrl = computed<string | null>(
  () => currentQuestion.value?.mediaQuestionsUrl ?? null,
)

const close = () => emit('update:visible', false)

const loadQuestions = async () => {
  loading.value = true
  try {
    const { data } = await ExamServices.getQuestionsByExam(props.examId)
    examTitle.value = data?.exam?.title ?? `Preview Exam #${props.examId}`
    const mapped = mapExamQuestionsResponse(data.data)
    questions.value = mapped.map((q: any, i: number) => ({
      id: q.id ?? i + 1,
      content: q.content,
      questionType: q.questionType,
      mediaQuestionsUrl: q.mediaQuestionsUrl ?? null,
      options: (q.options?.length ? q.options : q.answerOptions) ?? [],
      selectedOptionId: null,
    }))
    currentIndex.value = 0
  } catch (e: any) {
    showToast(toast, 'error', e?.response?.data?.message || 'Gagal memuat soal untuk preview')
    close()
  } finally {
    loading.value = false
  }
}

const prev = () => {
  if (currentIndex.value > 0) currentIndex.value--
}
const next = () => {
  if (currentIndex.value < questions.value.length - 1) currentIndex.value++
}
const goTo = (i: number) => {
  if (i >= 0 && i < questions.value.length) currentIndex.value = i
}
const selectAnswer = (qid: number, optionId: number) => {
  const q = questions.value.find((x) => (x.id ?? 0) === qid)
  if (q) q.selectedOptionId = optionId // lokal saja
}

watch(
  () => props.visible,
  (v) => {
    if (v) loadQuestions()
    else {
      questions.value = []
      currentIndex.value = 0
      examTitle.value = ''
    }
  },
)
</script>

<template>
  <Dialog
    :visible="props.visible"
    modal
    :dismissableMask="true"
    :draggable="false"
    :closable="true"
    :breakpoints="{ '960px': '95vw', '640px': '98vw' }"
    :style="{ width: '900px', maxWidth: '95vw' }"
    header="Pratinjau Soal"
    @update:visible="emit('update:visible', $event)"
  >
    <div class="space-y-4">
      <Card class="w-max-200">
        <template #content>
          <div v-if="loading" class="py-10 text-center">Memuat soal…</div>

          <InfoText
            v-else-if="!currentQuestion"
            message="Belum ada soal diexam ini."
            tag="div"
            customClass="mx-auto text-center"
          />

          <div v-else class="space-y-6">
            <!-- Essay -->
            <div v-if="currentQuestion.questionType === QuestionType.ESSAY">
              <QuestionTitle
                :index="currentIndex + 1"
                :html="currentQuestion.content"
                :media-url="effectivePreviewUrl"
              />
              <Divider />
              <InfoText
                message="Di sini siswa nanti mengisi jawaban esai/unggah lampiran. (Dinonaktifkan pada
                pratinjau.)"
              />
            </div>

            <!-- Multiple Choice -->
            <div v-else-if="currentQuestion.questionType === QuestionType.MULTIPLE_CHOICE">
              <QuestionTitle
                :index="currentIndex + 1"
                :html="currentQuestion.content"
                :media-url="effectivePreviewUrl"
              />
              <ChoiceList
                class="mt-4"
                :options="currentQuestion.options"
                :selected-id="currentSelectedId"
                @select="(id: any) => selectAnswer(currentQuestion.id ?? 0, id)"
              />
              <InfoText message="*Pratinjau: pilihan tidak disimpan ke server." />
            </div>

            <!-- Fallback -->
            <InfoText v-else message="Tipe soal tidak dikenali dalam pratinjau." />
          </div>
        </template>

        <template #footer>
          <div class="flex flex-col sm:flex-row justify-between gap-3">
            <Button
              label="Sebelumnya"
              outlined
              @click="prev"
              :disabled="currentIndex === 0"
              class="w-full sm:w-auto"
            />
            <div class="text-sm text-gray-500 self-center">
              Soal {{ currentIndex + 1 }} dari {{ questions.length }}
            </div>
            <Button
              label="Selanjutnya"
              :disabled="currentIndex >= questions.length - 1"
              @click="next"
              class="w-full sm:w-auto"
            />
          </div>
        </template>
      </Card>

      <!-- Navigator sederhana (optional, bubble angka) -->
      <div v-if="questions.length" class="flex flex-wrap gap-2 justify-center">
        <button
          v-for="(q, i) in questions"
          :key="q.id ?? i"
          class="w-9 h-9 rounded-full border flex items-center justify-center text-sm"
          :class="
            i === currentIndex
              ? 'bg-blue-600 text-white border-blue-600'
              : 'bg-white text-gray-700 border-gray-300'
          "
          @click="goTo(i)"
          :aria-label="`Pergi ke soal ${i + 1}`"
        >
          {{ i + 1 }}
        </button>
      </div>
    </div>
  </Dialog>
</template>
