<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { reactive, watch } from 'vue'

import SelectField from '@/components/common/SelectField.vue'
import TextArea from '@/components/common/TextArea.vue'
import TextField from '@/components/common/TextField.vue'
import { LevelKognitifOptions } from '@/enums/levelKognitif.enum'
import type { CompetencyPayload } from '@/types/question.type'

const props = defineProps<{
  visible: boolean
  mode?: 'add' | 'edit'
  initialValues?: Partial<CompetencyPayload>
  errors?: Record<string, string[]>
}>()

const emit = defineEmits<{
  (e: 'update:visible', v: boolean): void
  (e: 'confirm', payload: CompetencyPayload): void
  (e: 'cancel'): void
}>()

const form = reactive<CompetencyPayload>({
  kdNumber: '',
  learningOutcome: '',
  competencyIndicator: '',
  levelKognitif: '',
})

const err = (key: keyof CompetencyPayload) => props.errors?.[key]?.[0] ?? ''

watch(
  () => props.visible,
  (v) => {
    if (v) {
      form.kdNumber = props.initialValues?.kdNumber ?? ''
      form.learningOutcome = props.initialValues?.learningOutcome ?? ''
      form.competencyIndicator = props.initialValues?.competencyIndicator ?? ''
      form.levelKognitif = props.initialValues?.levelKognitif ?? ''
    }
  },
  { immediate: true },
)

const onHide = () => {
  emit('update:visible', false)
  emit('cancel')
  form.kdNumber = ''
  form.learningOutcome = ''
  form.competencyIndicator = ''
  form.levelKognitif = ''
}

const close = () => {
  emit('update:visible', false)
  emit('cancel')
}

const confirm = () => {
  emit('confirm', { ...form })
  emit('update:visible', false)
}
</script>

<template>
  <Dialog
    v-model:visible="(props as any).visible"
    modal
    :header="props.mode === 'edit' ? 'Edit Indikator Soal' : 'Indikator Soal'"
    :style="{ width: '640px' }"
    @hide="onHide"
    dismissableMask
  >
    <div class="space-y-4">
      <div>
        <TextField
          v-model="form.kdNumber"
          name="kdNumber"
          label="Nomor Kompetensi Dasar/ Isi Capaian Pembelajaran / Elemen"
          class="mt-3"
          required
        />
        <small v-if="err('kdNumber')" class="text-red-500">{{ err('kdNumber') }}</small>
      </div>

      <div>
        <TextArea
          v-model="form.learningOutcome"
          name="learningOutcome"
          label="Isi Kompetensi Dasar/ Isi Capaian Pembelajaran / Elemen"
          required
        />
        <small v-if="err('learningOutcome')" class="text-red-500">{{
          err('learningOutcome')
        }}</small>
      </div>

      <div>
        <TextArea
          v-model="form.competencyIndicator"
          name="competencyIndicator"
          label="Indikator Pencapaian Kompetensi"
          required
        />
        <small v-if="err('competencyIndicator')" class="text-red-500">{{
          err('competencyIndicator')
        }}</small>
      </div>

      <div>
        <SelectField
          v-model="form.levelKognitif"
          name="levelKognitif"
          :options="LevelKognitifOptions"
          optionLabel="label"
          optionValue="value"
          label="Pilih Level Kognitif"
          required
        />
      </div>

      <div class="flex justify-end gap-2 pt-2">
        <Button label="Batal" severity="secondary" @click="close" />
        <Button
          :label="props.mode === 'edit' ? 'Simpan' : 'Tambah'"
          icon="pi pi-check"
          @click="confirm"
        />
      </div>
    </div>
  </Dialog>
</template>
