<script setup lang="ts">
import Button from 'primevue/button'
import FileUpload, { type FileUploadSelectEvent } from 'primevue/fileupload'
import RadioButton from 'primevue/radiobutton'
import { useToast } from 'primevue/usetoast'
import { ref, watch, computed } from 'vue'

import Dialog from '@/components/common/Dialog.vue'
import Row from '@/components/common/Row.vue'
import ExamServices from '@/services/exam.service'
import { showToast } from '@/utils/toast.util'

interface Props {
  examId: number
  visible: boolean
}
const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', v: boolean): void
  (e: 'success'): void
}>()

const toast = useToast()

const mode = ref<'append' | 'replace'>('append')
const file = ref<File | null>(null)

const isDownloading = ref(false)
const isImporting = ref(false)
const canImport = computed(() => !!file.value && !isImporting.value)

watch(
  () => props.visible,
  (v) => {
    if (!v) reset()
  },
)

function reset() {
  mode.value = 'append'
  file.value = null
}

async function onDownloadTemplate() {
  try {
    isDownloading.value = true
    const res = await ExamServices.downloadTemplateQuestions()
    const blob = new Blob([res.data], {
      type:
        res.headers['content-type'] ||
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'template-import-exam-questions.xlsx'
    document.body.appendChild(a)
    a.click()
    a.remove()
    URL.revokeObjectURL(url)
  } catch (err: any) {
    const msg = err?.response?.data?.message || 'Gagal mengunduh template'
    showToast(toast, 'error', msg)
  } finally {
    isDownloading.value = false
  }
}

function onSelect(e: FileUploadSelectEvent | any) {
  const picked = Array.isArray(e?.files) ? e.files[0] : null
  file.value = picked ?? null
}

function onClear() {
  file.value = null
}

async function onImport() {
  if (!file.value) return
  try {
    isImporting.value = true
    const fd = new FormData()
    fd.append('file', file.value)
    fd.append('mode', mode.value)

    const res = await ExamServices.importQuestions(props.examId, fd)
    const msg = res?.data?.message || 'Import selesai'
    showToast(toast, 'success', msg)

    emit('success')
    emit('update:visible', false)
  } catch (err: any) {
    const data = err?.response?.data
    if (data?.message) showToast(toast, 'warn', data.message)
    else showToast(toast, 'error', 'Gagal mengimpor soal')
    // console.debug(data) // buka kalau mau lihat detail error baris
  } finally {
    isImporting.value = false
  }
}
</script>

<template>
  <Dialog
    :title="'Impor Soal'"
    :visible="visible"
    size="large"
    @close="$emit('update:visible', false)"
  >
    <div class="flex flex-col gap-4">
      <!-- 1) Download template -->
      <div class="rounded-md border border-gray-200 p-4 bg-white">
        <div class="font-medium mb-1">Template Excel</div>
        <p class="text-sm text-gray-600 mb-3">
          Gunakan template resmi agar heading sesuai:
          <span class="italic">
            No KD, Konten KD, Indikator Pencapaian, Level Kognitif, Bentuk, Soal, Opsi A–E,
            Jawaban(HURUF), Poin </span
          >.
        </p>
        <Button
          :label="isDownloading ? 'Mengunduh…' : 'Download Template'"
          icon="pi pi-download"
          class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
          :disabled="isDownloading"
          @click="onDownloadTemplate"
        />
      </div>

      <!-- 2) Mode -->
      <div class="rounded-md border border-gray-200 p-4 bg-white">
        <div class="font-medium mb-3">Mode Import</div>
        <div class="flex flex-col gap-3">
          <label class="inline-flex items-center gap-2 cursor-pointer">
            <RadioButton inputId="mode-append" name="mode" value="append" v-model="mode" />
            <span for="mode-append">
              <span class="font-medium">Append</span> — Tambahkan/merge soal ke exam; soal lama
              tetap ada.
            </span>
          </label>
          <label class="inline-flex items-center gap-2 cursor-pointer">
            <RadioButton inputId="mode-replace" name="mode" value="replace" v-model="mode" />
            <span for="mode-replace">
              <span class="font-medium text-red-600">Replace</span> — Hapus semua soal lama, ganti
              dengan isi file.
            </span>
          </label>
        </div>
      </div>

      <!-- 3) Upload -->
      <div class="rounded-md border border-gray-200 p-4 bg-white">
        <div class="font-medium mb-3">Upload File</div>
        <p class="text-sm text-gray-600 mb-3">Format: .xlsx / .xls (maks 5MB).</p>

        <FileUpload
          name="file"
          mode="basic"
          chooseLabel="Pilih File"
          :customUpload="true"
          :auto="false"
          :multiple="false"
          accept=".xlsx,.xls"
          @select="onSelect"
          @clear="onClear"
          class="w-full sm:w-auto"
        />
        <div v-if="file" class="mt-2 text-sm text-gray-700">
          Terpilih: <span class="font-medium">{{ file?.name }}</span>
        </div>
      </div>
    </div>

    <template #action>
      <Row class="gap-2">
        <Button
          label="Batal"
          class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
          @click="$emit('update:visible', false)"
        />
        <Button
          :label="isImporting ? 'Mengimpor…' : 'Impor'"
          icon="pi pi-upload"
          class="text-white px-4 py-2 rounded-md shadow-md outline !outline-[#ff0048] !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f]"
          :disabled="!canImport"
          @click="onImport"
        />
      </Row>
    </template>
  </Dialog>
</template>
