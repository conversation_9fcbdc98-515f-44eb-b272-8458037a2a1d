<script setup lang="ts">
import Button from 'primevue/button'
import Checkbox from 'primevue/checkbox'
import Dialog from 'primevue/dialog'
import Divider from 'primevue/divider'
import ProgressSpinner from 'primevue/progressspinner'
import { computed } from 'vue'

import MediaPreview from '@/components/common/MediaPreview.vue'
import QuestionTitle from '@/components/common/QuestionTitle.vue'
import SelectField from '@/components/common/SelectField.vue'
import { getQuestionTypeLabel } from '@/enums/questionType.enum'
import type { ExamQuestion } from '@/types/examQuestion.type'

interface Props {
  examId: number
  visible: boolean
  loading?: boolean

  // filters & selections
  termOptions: Array<{ label: string; value: number }>
  subjectOptions: Array<{ label: string; value: number }>
  termId: number | null
  subjectId: number | null
  sourceExamId: number | null

  // data
  sourceExams: Array<{ id: number; title: string; term?: string }>
  sourceQuestions: ExamQuestion[]

  // selection UI
  bulkChecked: boolean
  selectedIds: number[]
}
interface Emits {
  'update:visible': [boolean]
  'update:termId': [number | null]
  'update:subjectId': [number | null]
  'update:sourceExamId': [number | null]
  'refresh:sources': [] // ketika term/subject berubah
  'refresh:questions': [] // ketika exam dipilih
  'toggle:all': [boolean]
  'toggle:one': [number, boolean]
  submit: []
}
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const canSubmit = computed(() => props.sourceQuestions.length > 0)
const selectedCount = computed(() => props.selectedIds.length)

// helper baca url media dari berbagai casing key
const qMediaUrl = (q: any) =>
  q.mediaQuestionsUrl || q.media_questions_url || q.mediaquestionsurl || null
const optMediaUrl = (o: any) =>
  o.mediaAnswerOptionsUrl || o.media_answer_options_url || o.mediaansweroptionsurl || null

const termIdModel = computed({
  get: () => props.termId,
  set: (v) => {
    emit('update:termId', v)
    emit('update:sourceExamId', null)
    emit('refresh:sources')
  },
})
const subjectIdModel = computed({
  get: () => props.subjectId,
  set: (v) => {
    emit('update:subjectId', v)
    emit('update:sourceExamId', null)
    emit('refresh:sources')
  },
})
const sourceExamIdModel = computed({
  get: () => props.sourceExamId,
  set: (v) => {
    emit('update:sourceExamId', v)
    emit('refresh:questions')
  },
})

const isPicked = (qid?: number) => !!qid && props.selectedIds.includes(qid)
</script>

<template>
  <Dialog
    :visible="visible"
    modal
    header="Salin Soal dari Ujian Lain"
    style="width: 980px"
    :pt="{ header: { class: 'border-b pb-3' }, content: { class: 'pt-4' } }"
    @update:visible="(v) => emit('update:visible', v)"
  >
    <div class="grid gap-4">
      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-5">
        <SelectField
          v-model="termIdModel"
          name="termId"
          :options="termOptions"
          option-label="label"
          option-value="value"
          label="Semester"
          filter
        />
        <SelectField
          v-model="subjectIdModel"
          name="subjectId"
          :options="subjectOptions"
          option-label="label"
          option-value="value"
          label="Mata Pelajaran"
          filter
        />
        <SelectField
          v-model="sourceExamIdModel"
          :options="
            sourceExams.map((e) => ({ label: `${e.title} / ${e.term ?? '-'}`, value: e.id }))
          "
          name="sourceExamIdModel"
          label="Pilih judul ujian"
          option-value="value"
          class="w-full"
          showClear
          filter
        />
      </div>

      <!-- Catatan -->
      <div
        class="flex items-start gap-2 text-sm text-gray-700 bg-blue-50 border border-blue-200 rounded-md p-2"
      >
        <i class="pi pi-info-circle mt-0.5 text-blue-600"></i>
        <span>
          <strong>Catatan:</strong> Soal yang disalin akan langsung disimpan ke ujian ini. Anda
          <em>tidak perlu</em> menekan tombol <strong>Simpan Soal</strong> lagi.
        </span>
      </div>

      <Divider class="mb-1" />

      <!-- Loading -->
      <div v-if="loading" class="flex items-center justify-center p-6">
        <ProgressSpinner style="width: 32px; height: 32px" strokeWidth="4" />
      </div>

      <!-- List Questions -->
      <div v-else>
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
              <Checkbox
                :modelValue="bulkChecked"
                @update:modelValue="(val) => emit('toggle:all', !!val)"
              />
              <span class="text-sm">Pilih Semua</span>
            </div>
            <Button
              label="Bersihkan Pilihan"
              size="small"
              icon="pi pi-times"
              class="p-button-text"
              @click="() => emit('toggle:all', false)"
            />
          </div>
          <div class="text-sm text-gray-600">
            Terpilih: <b>{{ selectedCount }}</b>
          </div>
        </div>

        <div class="max-h-[480px] overflow-auto rounded-lg border p-3 bg-white">
          <div
            v-for="(q, idx) in sourceQuestions"
            :key="q.id ?? idx"
            class="flex gap-3 border rounded-md p-3 mb-3 last:mb-0 hover:shadow-sm transition"
          >
            <!-- Pick -->
            <div class="pt-1">
              <Checkbox
                :binary="true"
                :modelValue="isPicked(q.id)"
                @update:modelValue="(val) => emit('toggle:one', q.id!, !!val)"
              />
            </div>

            <!-- Body -->
            <div class="flex-1 min-w-0">
              <div class="flex flex-wrap items-center gap-2 mb-2">
                <span class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                  #{{ q.orderIndex ?? idx + 1 }}
                </span>
                <span class="text-xs px-2 py-1 rounded-full bg-indigo-100 text-indigo-700">
                  {{ getQuestionTypeLabel(q.questionType ?? '') }}
                </span>
                <span
                  v-if="q.points != null"
                  class="text-xs px-2 py-1 rounded-full bg-emerald-100 text-emerald-700"
                >
                  {{ Number(q.points) }} poin
                </span>
              </div>

              <QuestionTitle :html="q.content" :media-url="qMediaUrl(q)!" />

              <!-- Multiple choice options -->
              <div v-if="q.answerOptions?.length" class="mt-3 text-sm">
                <ul class="list-disc ml-5 space-y-2">
                  <li v-for="(o, i) in q.answerOptions" :key="i" class="leading-relaxed">
                    <span v-html="o.content"></span>
                    <strong v-if="o.isCorrect" class="text-emerald-700"> (Benar)</strong>

                    <!-- Media Option -->
                    <div v-if="optMediaUrl(o)" class="mt-1 ml-1">
                      <MediaPreview :url="optMediaUrl(o)!" class="max-w-full" :compact="true" />
                    </div>
                  </li>
                </ul>
              </div>

              <!-- Essay answer -->
              <div v-if="q.answerKeyEssay" class="mt-3">
                <div class="text-xs font-semibold text-gray-600 mb-1">Kunci Jawaban (Essay)</div>
                <div
                  class="prose max-w-none bg-gray-50 rounded p-2 border"
                  v-html="q.answerKeyEssay"
                ></div>
              </div>
            </div>
          </div>

          <div v-if="!sourceQuestions.length" class="text-center text-sm text-gray-500 py-8">
            Tidak ada soal untuk ditampilkan.
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-2 mt-4">
          <Button label="Batal" class="p-button-text" @click="emit('update:visible', false)" />
          <Button
            label="Salin ke Ujian Ini"
            icon="pi pi-copy"
            :disabled="!canSubmit"
            @click="emit('submit')"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>
