<script setup lang="ts">
import Button from 'primevue/button'
import Checkbox from 'primevue/checkbox'

import EditorMath from '@/components/common/EditorMath.vue'
import type { ExamQuestionHasAnswerOption } from '@/types/examQuestionHasAnswerOption.type'

const props = defineProps<{
  answerOption: ExamQuestionHasAnswerOption
  oIndex: number
  correctIndex: number
}>()

defineEmits<{
  (e: 'update:option', content: string): void
  (e: 'update:isCorrect', value: boolean): void
  (e: 'remove'): void
}>()

const editorKey = `${props.answerOption?.id ?? 'new'}-${props.oIndex}`
</script>

<template>
  <div class="flex items-start gap-3 p-3 border border-gray-300 rounded">
    <div
      class="w-8 h-8 flex items-center justify-center bg-gray-200 rounded-full font-bold text-sm"
    >
      {{ String.fromCharCode(65 + props.oIndex) }}
    </div>
    <Checkbox
      class="mt-1"
      binary
      :modelValue="props.correctIndex === props.oIndex"
      @change="$emit('update:isCorrect', true)"
    />

    <div class="flex-1">
      <EditorMath
        :key="editorKey"
        :model-value="props.answerOption.content || ''"
        :height="120"
        @update:model-value="(val) => $emit('update:option', val)"
      />
    </div>

    <Button icon="pi pi-times" text rounded severity="danger" @click="$emit('remove')" />
  </div>
</template>
