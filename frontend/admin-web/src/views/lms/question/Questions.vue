<script setup lang="ts">
import Button from 'primevue/button'
import ProgressSpinner from 'primevue/progressspinner'
import { onMounted, computed, ref } from 'vue'
import { useRoute } from 'vue-router'

import type { DialogFormType } from '@/components/common/DialogForm.vue'
import Row from '@/components/common/Row.vue'
import { backToExamList } from '@/utils/examAction'

import CopyQuestionsDialog from './dialog/CopyQuestionsDialog.vue'
import ImportQuestionsDialog from './dialog/ImportQuestionsDialog.vue'
import PreviewQuestionsDialog from './dialog/PreviewQuestionsDialog.vue'
import QuestionCompetencyDialog from './dialog/QuestionCompetencyDialog.vue'
import Question from './Question.vue'
import { useQuestionViewModel } from './useQuestionViewModel'

const viewModel = useQuestionViewModel()
const state = viewModel.state
const route = useRoute()
const examId = Number(route.params.id)

const questions = computed(() => state.questionData)
const scrollContainer = ref<HTMLElement | null>(null)
const bottomRef = ref<HTMLElement | null>(null)
const showActionButtons = ref(false)

interface Props {
  type: DialogFormType
}
interface Emits {
  submit: []
  close: []
}
defineProps<Props>()
defineEmits<Emits>()

onMounted(() => {
  viewModel.getQuestionsByExam(examId)
})

const addQuestion = async () => {
  await viewModel.addQuestion(examId)
  bottomRef.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
}
const total = computed(() => questions.value.reduce((sum, q) => sum + (Number(q.points) || 0), 0))

async function onImportSuccess() {
  await viewModel.getQuestionsByExam(examId)
}
</script>

<template>
  <div class="flex flex-col gap-6" ref="scrollContainer">
    <Row class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <Button
        label="Kembali"
        icon="pi pi-arrow-left"
        class="!bg-white !text-gray-700 border !border-gray-300 hover:bg-gray-100"
        @click="backToExamList"
      />
      <span class="font-bold">Total nilai: {{ total ?? '-' }}</span>

      <div class="relative flex gap-2">
        <!-- Menu Aksi + Dropdown -->
        <div class="relative">
          <Button
            label="Menu Aksi"
            icon="pi pi-bars"
            class="!bg-blue-500 text-white"
            @click="showActionButtons = !showActionButtons"
          />

          <div
            v-show="showActionButtons"
            class="absolute left-0 top-full mt-2 flex flex-col gap-2 bg-white border p-2 rounded shadow z-10"
          >
            <Button
              label="Preview soal"
              icon="pi pi-eye"
              class="w-40 text-sm py-2 px-3 justify-start"
              @click="viewModel.openPreviewDialog()"
            />

            <Button
              label="Salin Soal"
              icon="pi pi-copy"
              class="w-40 text-sm py-2 px-3 justify-start text-white !bg-[#5693f0] !border-[#5693f0]"
              @click="viewModel.copySoal"
            />

            <Button
              label="Impor Soal"
              icon="pi pi-save"
              class="w-40 text-sm py-2 px-3 justify-start"
              @click="viewModel.openImportExamQuestionDialog"
            />
          </div>
        </div>

        <!-- Tombol Simpan -->
        <div>
          <Button
            label="Simpan Soal"
            icon="pi pi-save"
            class="text-white !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f] w-full justify-start"
            @click="viewModel.submitQuestions(examId)"
          />
        </div>
      </div>
    </Row>
    <div
      v-if="state.loadingQuestions"
      class="fixed inset-0 z-[1000] bg-white/70 backdrop-blur-[2px] flex items-center justify-center"
      role="status"
      aria-live="polite"
      aria-label="Memuat pertanyaan"
    >
      <div class="flex flex-col items-center gap-3">
        <ProgressSpinner strokeWidth="4" style="width: 48px; height: 48px" />
        <span class="text-sm text-gray-700">Memuat pertanyaan…</span>
      </div>
    </div>
    <div class="space-y-4">
      <Question
        v-for="(q, i) in questions"
        :key="q.id ?? i"
        :question="q"
        :qIndex="i"
        :formErrors="state.formErrors as any"
        @update:content="(v) => (q.content = v)"
        @update:answerKeyEssay="(v) => (q.answerKeyEssay = v)"
        @update:points="(v) => (q.points = v)"
        @update:type="(v) => (q.questionType = v)"
        @addOption="() => viewModel.addOption(i)"
        @removeQuestion="() => viewModel.removeQuestion(i)"
        @removeOption="(o) => viewModel.removeOption(i, o)"
        @update:answerOption="(o, v) => viewModel.updateOption(i, o, v)"
        @update:isCorrect="(o, v) => viewModel.updateIsCorrect(i, o, v)"
        @uploadMedia="
          (file: any) => {
            q.mediaQuestions = file
            q.mediaQuestionsUrl = URL.createObjectURL(file)
          }
        "
        @editIndicators="() => viewModel.openEditIndicatorDialog(i)"
      />

      <QuestionCompetencyDialog
        v-model:visible="state.showCompetencyDialog"
        :mode="state.competencyDialogMode"
        :initialValues="state.pendingCompetency"
        :errors="(state.formErrors as any) ?? undefined"
        @confirm="(p) => viewModel.confirmIndicatorDialog(examId, p)"
      />

      <ImportQuestionsDialog
        :examId="examId"
        v-model:visible="state.showImportQuestions"
        @success="onImportSuccess"
      />

      <CopyQuestionsDialog
        :examId="examId"
        v-model:visible="state.showCopyDialog"
        :loading="state.copy.loading"
        :sourceExams="state.copy.sourceExams"
        :sourceQuestions="state.copy.sourceQuestions"
        :bulkChecked="state.copy.bulkSelect"
        :selectedIds="Array.from(state.copy.selectedQuestionIds)"
        :termOptions="state.termOptions"
        :subjectOptions="state.subjectOptions"
        :termId="state.termId"
        :subjectId="state.subClassroomSubjectId"
        :sourceExamId="state.copy.sourceExamId"
        @refresh:sources="
          () =>
            viewModel.fetchCopySources({
              term_id: state.termId ?? undefined,
              sub_classroom_subject_id: state.subClassroomSubjectId ?? undefined,
            })
        "
        @refresh:questions="viewModel.fetchSourceQuestions"
        @update:termId="
          (v) => {
            state.termId = v
            state.copy.sourceExamId = null
            state.copy.sourceQuestions = []
          }
        "
        @update:subjectId="
          (v) => {
            state.subClassroomSubjectId = v
            state.copy.sourceExamId = null
            state.copy.sourceQuestions = []
          }
        "
        @update:sourceExamId="(v) => (state.copy.sourceExamId = v)"
        @toggle:all="viewModel.toggleSelectAll"
        @toggle:one="(id, checked) => viewModel.togglePick(id, checked)"
        @submit="
          () =>
            viewModel.submitCopy(examId, {
              sourceExamId: state.copy.sourceExamId!,
              questionIds: Array.from(state.copy.selectedQuestionIds),
              insertMode: 'append',
              deepCopyMedia: false,
            })
        "
      />

      <PreviewQuestionsDialog v-model:visible="state.showPreviewDialog" :exam-id="examId" />
    </div>
  </div>

  <div class="text-start p-4" ref="bottomRef">
    <Button
      label="Tambah Soal"
      icon="pi pi-plus"
      class="text-white px-4 py-2 rounded-md shadow-md outline !outline-[#ff0048] !bg-[#ff0048] !border-[#ff0048] hover:!bg-[#e6003f]"
      @click="addQuestion"
    />
  </div>
</template>
