<script setup lang="ts">
import MathRenderer from './MathRenderer.vue'

type OptionItem = { id: number | string; content: string }

defineProps<{
  options?: OptionItem[]
  selectedId?: number | string | null
  readonly?: boolean
}>()

const emit = defineEmits<{ (e: 'select', id: number | string): void }>()
</script>

<template>
  <div class="space-y-3">
    <div
      v-for="(opt, i) in options"
      :key="opt.id"
      :class="[
        'flex items-start space-x-3 p-4 border rounded-xl text-sm',
        selectedId === opt.id
          ? 'border-green-500 bg-green-50'
          : 'border-gray-300 hover:border-gray-400',
        !readonly && 'cursor-pointer transition',
      ]"
      @click="!readonly && emit('select', opt.id)"
    >
      <div
        :class="[
          'w-8 h-8 flex items-center justify-center rounded-full font-semibold shrink-0',
          selectedId === opt.id ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700',
        ]"
      >
        {{ String.fromCharCode(65 + i) }}
      </div>
      <!-- <div class="text-gray-800">{{ opt.content }}</div> -->
      <MathRenderer :html="opt.content || ''" :auto-render="true" :heuristic="true" />
    </div>
  </div>
</template>
