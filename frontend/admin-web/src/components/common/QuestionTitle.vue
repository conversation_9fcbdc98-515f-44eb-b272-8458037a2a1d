<!-- src/components/common/QuestionTitle.vue -->
<script setup lang="ts">
import MathRenderer from '@/components/common/MathRenderer.vue'
import MediaPreview from '@/components/common/MediaPreview.vue'

const props = defineProps<{
  index?: number
  html?: string
  mediaUrl?: string | null
}>()
</script>

<template>
  <div class="mb-4">
    <h2 class="text-lg font-semibold text-gray-900 flex">
      <span v-if="index"> {{ index }}. </span>
      <span class="align-middle ml-1">
        <MathRenderer :html="props.html || ''" :auto-render="true" :heuristic="true" />
      </span>
    </h2>
    <MediaPreview v-if="props.mediaUrl" :url="props.mediaUrl" class="mt-3" />
  </div>
</template>

<style scoped>
:deep(.katex) {
  line-height: 1.2;
}
:deep(.katex-display) {
  display: inline-block;
  margin: 0 0.25rem;
}
</style>
