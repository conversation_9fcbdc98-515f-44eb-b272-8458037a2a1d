<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  password?: string | null
  /** tampilkan password saat awal (default: false = tersembunyi) */
  revealByDefault?: boolean
  /** teks yang ditampilkan saat berhasil copy */
  copiedText?: string
}>()

// default: tersembunyi bila parent tidak mengirim prop
const show = ref(props.revealByDefault ?? false)
const justCopied = ref(false)

async function copy() {
  if (!props.password) return
  try {
    await navigator.clipboard.writeText(props.password)
    justCopied.value = true
    setTimeout(() => (justCopied.value = false), 1800)
  } catch (e) {
    console.error('Copy failed', e)
  }
}
</script>

<template>
  <div class="inline-flex items-center gap-2">
    <code
      class="bg-gray-100 px-2 py-1 rounded text-sm font-mono select-none"
      :title="props.password ? 'Klik ikon salin untuk menyalin' : ''"
    >
      <span v-if="props.password">
        <span v-if="show">{{ props.password }}</span>
        <span v-else>••••••••</span>
      </span>
      <span v-else>-</span>
    </code>

    <button
      type="button"
      class="px-2 py-1 rounded text-xs border hover:bg-gray-50 transition"
      :disabled="!props.password"
      @click="copy"
      aria-label="Salin password"
      title="Salin password"
    >
      Salin
    </button>

    <button
      v-if="props.password"
      type="button"
      class="px-2 py-1 rounded text-xs border hover:bg-gray-50 transition"
      @click="show = !show"
      :aria-label="show ? 'Sembunyikan password' : 'Tampilkan password'"
      :title="show ? 'Sembunyikan' : 'Tampilkan'"
    >
      {{ show ? 'Sembunyikan' : 'Tampilkan' }}
    </button>

    <!-- mini toast lokal -->
    <transition name="fade">
      <span v-if="justCopied" class="text-green-600 text-xs" role="status" aria-live="polite">
        {{ props.copiedText || 'Password berhasil di-copy!' }}
      </span>
    </transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
