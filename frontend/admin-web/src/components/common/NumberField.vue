<script setup lang="ts">
import { FloatLabel, InputNumber } from 'primevue'

interface TextFieldProps {
  label: string
  name: string
  min?: number
  max?: number
  placeholder?: string
  modelValue?: number
  disabled?: boolean
  currency?: string
}

defineProps<TextFieldProps>()
</script>

<template>
  <FloatLabel variant="on">
    <InputNumber
      :name="name"
      :id="name"
      :model-value="modelValue"
      :disabled
      :min
      :max
      :mode="currency ? 'currency' : 'decimal'"
      :currency="currency || 'IDR'"
      :locale="currency ? 'id-ID' : undefined"
      @input="$emit('update:modelValue', $event.value)"
      fluid
    />
    <label :for="name">{{ label }}</label>
  </FloatLabel>
</template>
