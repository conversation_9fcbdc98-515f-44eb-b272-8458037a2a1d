<!-- src/components/common/MathRenderer.vue -->
<script setup lang="ts">
import katex from 'katex'
import renderMathInElement from 'katex/contrib/auto-render'
import { onMounted, onUpdated, ref, watch, nextTick } from 'vue'
import type { PropType } from 'vue'

import 'katex/dist/katex.min.css'

const props = defineProps({
  html: { type: String as PropType<string>, default: '' },
  autoRender: { type: Boolean, default: true },
  heuristic: { type: Boolean, default: true },
  sanitize: { type: Boolean, default: false },
})

const containerRef = ref<HTMLElement | null>(null)

function sanitizeHtml(input: string) {
  if (!props.sanitize) return input
  const DOMPurify = (window as any).DOMPurify
  if (DOMPurify) return DOMPurify.sanitize(input)
  return input
}

async function renderAll() {
  await nextTick()
  const el = containerRef.value
  if (!el) return

  // 0) Heuristik: render token LaTeX TANPA delimiter (E = mc^2, \frac{a}{b}, \sqrt{x})
  if (props.heuristic) renderUndelimitedLatex(el)

  // 1) Render <span class="ql-formula" data-value="...">
  const formulaNodes = el.querySelectorAll<HTMLElement>('span.ql-formula[data-value]')
  formulaNodes.forEach((node) => {
    const tex = node.dataset.value || node.textContent || ''
    try {
      katex.render(tex, node, {
        throwOnError: false,
        displayMode: node.tagName === 'DIV' || node.classList.contains('block-math'),
      })
    } catch {
      /* noop */
    }
  })

  // 2) Auto-render LaTeX dengan delimiter
  if (props.autoRender) {
    try {
      renderMathInElement(el, {
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\[', right: '\\]', display: true },
          { left: '\\(', right: '\\)', display: false },
        ],
        throwOnError: false,
      })
    } catch {
      /* noop */
    }
  }
}

/** Heuristik LaTeX tanpa delimiter */
function renderUndelimitedLatex(root: HTMLElement) {
  const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT)
  const textNodes: Text[] = []
  let n: Node | null
  while ((n = walker.nextNode())) {
    const parent = (n as Text).parentElement
    if (!(parent && parent.closest('.katex'))) textNodes.push(n as Text)
  }

  const cmdRegex = /\\[a-zA-Z]+/ // \frac, \sqrt, \alpha, ...
  const powerRegex = /[A-Za-z0-9]\^[A-Za-z0-9({]/ // x^2, x^{10}

  textNodes.forEach((node) => {
    const raw = node.nodeValue ?? ''
    if (!raw) return
    if (raw.includes('$') || raw.includes('\\(') || raw.includes('\\[')) return

    // pecah per koma / titik koma / line break, pertahankan delimiter
    const parts = raw.split(/(,|;|\n)/)
    let mutated = false
    const frag = document.createDocumentFragment()

    for (let i = 0; i < parts.length; i++) {
      const token = parts[i]
      if (token === ',' || token === ';' || token === '\n') {
        frag.appendChild(document.createTextNode(token))
        continue
      }
      const piece = token.trim()
      const looksLatex = cmdRegex.test(piece) || powerRegex.test(piece)

      if (!piece || !looksLatex) {
        frag.appendChild(document.createTextNode(token))
        continue
      }

      const span = document.createElement('span')
      try {
        katex.render(piece, span, { throwOnError: false, displayMode: false })
        mutated = true
        const leading = token.match(/^\s*/)?.[0] ?? ''
        const trailing = token.match(/\s*$/)?.[0] ?? ''
        if (leading) frag.appendChild(document.createTextNode(leading))
        frag.appendChild(span)
        if (trailing) frag.appendChild(document.createTextNode(trailing))
      } catch {
        frag.appendChild(document.createTextNode(token))
      }
    }

    if (mutated) node.parentNode?.replaceChild(frag, node)
  })
}

onMounted(renderAll)
watch(() => props.html, renderAll)
onUpdated(renderAll)
</script>

<template>
  <div ref="containerRef" v-html="sanitizeHtml(html)"></div>
</template>

<style scoped>
:deep(.katex-display) {
  margin: 0.5rem 0;
}
</style>
