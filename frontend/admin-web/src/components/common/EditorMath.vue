<!-- src/components/common/EditorMath.vue -->
<script setup lang="ts">
import Editor from 'primevue/editor'
import { ref, watch, nextTick, computed, onMounted, onBeforeUnmount } from 'vue'
import type { PropType } from 'vue'
import 'katex/dist/katex.min.css'

const props = defineProps({
  modelValue: { type: String, default: '' },
  height: { type: [Number, String] as PropType<number | string>, default: 200 },
  placeholder: { type: String, default: 'Ketik di sini…' },
  disabled: { type: Boolean, default: false },
  modules: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
  formats: { type: Array as PropType<string[]>, default: () => [] },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'text-change', payload: any): void
  (e: 'ready'): void
}>()

const editorRef = ref<any>(null)
const localContent = ref(props.modelValue)
const hasHydratedFromServer = ref(false)
const userHasEdited = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    if (userHasEdited.value) return
    if (!hasHydratedFromServer.value) {
      localContent.value = normalizeForEditor(val ?? '')
      hasHydratedFromServer.value = true
    } else if (val !== localContent.value) {
      localContent.value = val ?? ''
    }
  },
  { immediate: true },
)

watch(localContent, (val) => {
  if (val !== props.modelValue) emit('update:modelValue', val)
})

const baseModules = {
  formula: true,
  toolbar: {
    container: [
      [{ header: [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ align: [] }],
      ['link'],
      ['code-block'],
      ['formula'],
      ['clean'],
    ],
  },
}
const editorModules = { ...baseModules, ...props.modules }
const baseFormats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'list',
  'bullet',
  'script',
  'indent',
  'align',
  'link',
  'code-block',
  'formula',
]
const editorFormats = props.formats.length ? props.formats : baseFormats

const editorStyle = computed(
  () => `height: ${typeof props.height === 'number' ? props.height + 'px' : props.height}`,
)

// --- Lifecycle: enable Ctrl/Cmd+Click to open links in edit mode ---
onMounted(async () => {
  await nextTick()
  const root: HTMLElement | null = editorRef.value?.$el?.querySelector('.ql-editor')
  if (root) root.addEventListener('click', onEditorClick)
  emit('ready')
})

onBeforeUnmount(() => {
  const root: HTMLElement | null = editorRef.value?.$el?.querySelector('.ql-editor')
  if (root) root.removeEventListener('click', onEditorClick)
})

function onEditorClick(e: MouseEvent) {
  const target = e.target as HTMLElement
  const a = target?.closest?.('a[href]') as HTMLAnchorElement | null
  if (a && (e.ctrlKey || e.metaKey)) {
    window.open(a.href, '_blank', 'noopener')
    e.preventDefault()
  }
}

// --- Text change: set HTML terbaru + autolink URL ---
const onTextChange = (e: any) => {
  // Auto-link hanya untuk input user
  if (e?.source === 'user') {
    autoLinkDelta(e)
  }
  // Ambil HTML terbaru setelah mungkin diformat
  const html = e?.editor?.root?.innerHTML ?? e?.htmlValue ?? localContent.value
  localContent.value = html
  userHasEdited.value = true
  emit('update:modelValue', localContent.value)
  emit('text-change', e)
}

// === Auto-link tanpa plugin (format 'link' Quill) ===
function autoLinkDelta(e: any) {
  const quill = e?.editor
  const ops: any[] = e?.delta?.ops || []
  if (!quill || !ops.length) return

  const urlRe = /((https?:\/\/|www\.)[^\s<]+)/g
  let index = 0

  for (const op of ops) {
    if (typeof op.insert === 'string') {
      const text: string = op.insert
      let m: RegExpExecArray | null
      while ((m = urlRe.exec(text)) !== null) {
        const url = m[1]
        const start = index + m.index
        const len = url.length

        // Jangan timpa jika sudah berformat link
        const fmt = quill.getFormat(start, len)
        if (!fmt.link) {
          const href = url.startsWith('www.') ? `http://${url}` : url
          quill.formatText(start, len, 'link', href, 'silent')
        }
      }
      index += text.length
    } else {
      // embed / newline
      index += 1
    }
  }
}

function escAttr(s: string) {
  return s
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
}

/** Pastikan link aman: target=_blank + rel=noopener noreferrer */
function hardenLinks(root: HTMLElement) {
  root.querySelectorAll<HTMLAnchorElement>('a[href]').forEach((a) => {
    if (!a.getAttribute('target')) a.setAttribute('target', '_blank')
    const relNow = (a.getAttribute('rel') || '').split(/\s+/).filter(Boolean)
    const wanted = new Set([...relNow, 'noopener', 'noreferrer'])
    a.setAttribute('rel', Array.from(wanted).join(' '))
  })
}

/** Rehydrate formula & normalisasi link dari HTML server/DB → siap untuk Editor */
function normalizeForEditor(html: string): string {
  if (!html) return html
  const container = document.createElement('div')
  container.innerHTML = html

  // 1) pastikan <span class="ql-formula"> punya data-value
  container.querySelectorAll<HTMLSpanElement>('span.ql-formula').forEach((el) => {
    const tex = (el.dataset.value || el.textContent || '').trim()
    if (!el.dataset.value && tex) el.setAttribute('data-value', tex)
  })

  // 2) perkuat link (target/rel) agar aman
  hardenLinks(container)

  // 3) convert delimiter → ql-formula (agar editor tampilkan rumus)
  let out = container.innerHTML
  out = out.replace(/\$\$([\s\S]+?)\$\$/g, (_m, tex) => {
    const t = String(tex).trim()
    return `<span class="ql-formula block-math" data-value="${escAttr(t)}">${t}</span>`
  })
  out = out.replace(/\$([^$]+?)\$/g, (_m, tex) => {
    const t = String(tex).trim()
    return `<span class="ql-formula" data-value="${escAttr(t)}">${t}</span>`
  })
  out = out.replace(/\\\[((?:.|\n)+?)\\\]/g, (_m, tex) => {
    const t = String(tex).trim()
    return `<span class="ql-formula block-math" data-value="${escAttr(t)}">${t}</span>`
  })
  out = out.replace(/\\\(((?:.|\n)+?)\\\)/g, (_m, tex) => {
    const t = String(tex).trim()
    return `<span class="ql-formula" data-value="${escAttr(t)}">${t}</span>`
  })

  return out
}
</script>

<template>
  <div class="space-y-3">
    <Editor
      ref="editorRef"
      v-model="localContent"
      :modules="editorModules"
      :formats="editorFormats"
      :placeholder="placeholder"
      :readonly="disabled"
      :editor-style="editorStyle"
      class="rounded-lg shadow"
      @text-change="onTextChange"
    />
    <slot name="hint">
      <div class="text-xs text-gray-600">
        Klik ikon <span class="font-bold">fx</span> untuk menambahkan rumus (KaTeX/LaTeX). Misal:
        <code>E = mc^2</code>, <code>\frac{a}{b}</code>, <code>\sqrt{x}</code>
      </div>
    </slot>
  </div>
</template>
