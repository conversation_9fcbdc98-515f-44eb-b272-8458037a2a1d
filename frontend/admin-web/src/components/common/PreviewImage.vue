<template>
  <div
    v-if="visible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-blur bg-opacity-100 backdrop-blur-md p-4 rounded-2xl"
  >
    <div
      class="relative bg-white rounded-lg shadow-lg w-full max-w-5xl max-h-full overflow-y-auto p-4"
    >
      <button
        class="absolute top-3 right-3 text-gray-600 hover:text-black text-2xl"
        @click="$emit('close')"
      >
        ✕
      </button>

      <h2 v-if="title" class="text-lg font-semibold mb-4 text-center break-words">
        {{ title }}
      </h2>

      <div class="flex justify-center">
        <img :src="imgUrl" alt="Preview" class="max-w-full max-h-[80vh] object-contain rounded" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  visible: boolean
  imgUrl: string
  title?: string
}>()

defineEmits(['close'])
</script>
