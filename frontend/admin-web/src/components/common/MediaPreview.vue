<script setup lang="ts">
import { ref } from 'vue'

import PreviewImage from './PreviewImage.vue'

interface Props {
  url?: string | null
  alt?: string
  kind?: 'image' | 'video' | 'audio'
}
defineProps<Props>()

const visible = ref(false)

const getCleanUrl = (u: string) => u.split('?')[0]

const isImg = (u: string) => /\.(png|jpe?g|webp|gif)$/i.test(getCleanUrl(u))
const isVid = (u: string) => /\.(mp4|webm|ogg)$/i.test(getCleanUrl(u))
const isAud = (u: string) => /\.(mp3|wav|ogg)$/i.test(getCleanUrl(u))
</script>

<template>
  <div v-if="url" class="mt-2">
    <img
      v-if="kind === 'image' || (!kind && isImg(url!))"
      :src="url!"
      :alt="alt"
      class="max-h-64 w-96 object-contain rounded-lg cursor-pointer hover:opacity-90"
      @click="visible = true"
    />
    <video
      v-else-if="kind === 'video' || (!kind && isVid(url!))"
      :src="url!"
      controls
      class="max-h-64 w-96 object-contain rounded-lg"
    />
    <audio v-else-if="kind === 'audio' || (!kind && isAud(url!))" :src="url!" controls />
    <a v-else :href="url!" target="_blank" rel="noopener" class="text-blue-600 underline"
      >Buka Media</a
    >

    <PreviewImage :visible="visible" :img-url="url!" :title="alt" @close="visible = false" />
  </div>
</template>
