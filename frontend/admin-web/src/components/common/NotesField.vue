<template>
  <FloatLabel>
    <Textarea
      :id="name"
      v-model="inputValue"
      :name="name"
      :placeholder="placeholder"
      :required="required"
      :disabled="disabled"
      :readonly="readonly"
      :maxlength="maxlength"
      :auto-resize="autoResize"
      :rows="rows"
      :cols="cols"
      class="w-full"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
    />
    <label :for="name">{{ label }}</label>
  </FloatLabel>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import FloatLabel from 'primevue/floatlabel'
import Textarea from 'primevue/textarea'

interface Props {
  modelValue?: string | null
  label: string
  name: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  autoResize?: boolean
  rows?: number
  cols?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'blur', event: Event): void
  (e: 'focus', event: Event): void
  (e: 'input', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '',
  required: false,
  disabled: false,
  readonly: false,
  maxlength: undefined,
  autoResize: true,
  rows: 3,
  cols: undefined
})

const emit = defineEmits<Emits>()

const inputValue = computed({
  get: () => props.modelValue || '',
  set: (value: string) => emit('update:modelValue', value)
})

const handleBlur = (event: Event) => {
  emit('blur', event)
}

const handleFocus = (event: Event) => {
  emit('focus', event)
}

const handleInput = (event: Event) => {
  emit('input', event)
}
</script>

<style scoped>
/* Inherits from global PrimeVue styles */
</style>