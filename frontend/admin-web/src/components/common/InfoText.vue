<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  message: string
  type?: 'info' | 'warning' | 'error'
  customClass?: string
  tag?: string
}

const props = defineProps<Props>()

const colorClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'text-yellow-600'
    case 'error':
      return 'text-red-600'
    case 'info':
      return 'text-blue-600'
    default:
      return 'text-gray-500'
  }
})

const combinedClass = computed(() => {
  const base = ['text-sm', 'italic', colorClass.value]
  const custom = props.customClass?.split(' ') ?? []

  const needsBlock = custom.some((c) =>
    ['mx-auto', 'text-center', 'text-left', 'text-right'].includes(c),
  )
  if (needsBlock) base.push('block')

  return [...base, ...custom].join(' ')
})
</script>

<template>
  <component :is="props.tag ?? 'span'" :class="combinedClass">
    {{ message }}
  </component>
</template>
