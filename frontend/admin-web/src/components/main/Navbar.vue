<script setup lang="ts">
import { Button } from 'primevue'
import { ref } from 'vue'

import Logo from '@/assets/tias_logo.png'
import Row from '@/components/common/Row.vue'

const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}
</script>

<template>
  <Row class="gap-8 font-medium uppercase py-4 px-14 items-center justify-between shadow relative">
    <div>
      <img :src="Logo" alt="logo" width="42" />
    </div>

    <div class="hidden md:flex gap-8">
      <RouterLink
        to="/"
        exact-active-class="border-b-2 border-[#FD0047] !text-[#FD0047]"
        class="text-gray-500 hover:text-gray-700 transition-colors pb-1 relative"
      >
        Home
      </RouterLink>

      <RouterLink
        to="/about"
        exact-active-class="border-b-2 border-[#FD0047] !text-[#FD0047]"
        class="text-gray-500 hover:text-gray-700 transition-colors pb-1 relative"
      >
        About
      </RouterLink>
    </div>

    <div class="hidden md:block">
      <Button
        icon="pi pi-download"
        label="Download App"
        :pt="{ root: '!bg-[#FD0047] !border-none hover:!bg-[#E5003E] !transition-colors' }"
      />
    </div>

    <button
      @click="toggleMobileMenu"
      class="md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1.5 focus:outline-none"
      :class="{ 'space-y-0': isMobileMenuOpen }"
    >
      <span
        class="w-6 h-0.5 bg-gray-600 transition-all duration-300 ease-in-out"
        :class="isMobileMenuOpen ? 'rotate-45 translate-y-2' : ''"
      ></span>
      <span
        class="w-6 h-0.5 bg-gray-600 transition-all duration-300 ease-in-out"
        :class="isMobileMenuOpen ? 'opacity-0' : ''"
      ></span>
      <span
        class="w-6 h-0.5 bg-gray-600 transition-all duration-300 ease-in-out"
        :class="isMobileMenuOpen ? '-rotate-45 -translate-y-2' : ''"
      ></span>
    </button>

    <div
      v-if="isMobileMenuOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
      @click="toggleMobileMenu"
    ></div>

    <div
      class="fixed top-0 right-0 h-full w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 md:hidden"
      :class="isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'"
    >
      <div class="flex justify-between items-center p-6 border-b">
        <img :src="Logo" alt="logo" width="32" />
        <button
          @click="toggleMobileMenu"
          class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <div class="flex flex-col p-6 space-y-6">
        <RouterLink
          to="/"
          exact-active-class="!text-[#FD0047] !font-semibold !bg-red-50 !border-l-4 !border-l-[#FD0047]"
          class="text-gray-500 hover:text-gray-700 hover:bg-gray-50 py-3 px-4 rounded-r-lg border-b border-gray-100 transition-all duration-300 -mx-4"
          @click="toggleMobileMenu"
        >
          Home
        </RouterLink>

        <RouterLink
          to="/about"
          exact-active-class="!text-[#FD0047] !font-semibold !bg-red-50 !border-l-4 !border-l-[#FD0047]"
          class="text-gray-500 hover:text-gray-700 hover:bg-gray-50 py-3 px-4 rounded-r-lg border-b border-gray-100 transition-all duration-300 -mx-4"
          @click="toggleMobileMenu"
        >
          About
        </RouterLink>

        <div class="pt-4">
          <Button
            icon="pi pi-download"
            label="Download App"
            :pt="{
              root: '!bg-[#FD0047] !border-none !w-full hover:!bg-[#E5003E] !transition-colors',
            }"
            @click="toggleMobileMenu"
          />
        </div>
      </div>
    </div>
  </Row>
</template>
