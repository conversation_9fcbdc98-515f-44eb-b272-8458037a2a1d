<script setup lang="ts">
import { PanelMenu } from 'primevue'
import type { MenuItem } from 'primevue/menuitem'
import { computed } from 'vue'

// Import semua menu berdasarkan role
import { menuItemsCanteenAdmin } from '@/data/sidebarItemsCanteenAdmin'
import { menuItemsCashier } from '@/data/sidebarItemsCashier'
import { menuItemsHeadmaster } from '@/data/sidebarItemsHeadmaster'
import { menuItemsSchoolAdmin } from '@/data/sidebarItemsSchoolAdmin'
import { menuItemsStudent } from '@/data/sidebarItemsStudent'
import { menuItemsSuperadmin } from '@/data/sidebarItemsSuperadmin'
import { menuItemsTeachers } from '@/data/sidebarItemsTeacher'
import { useAuthStore } from '@/stores/auth'

// Ambil role dari auth store
const auth = useAuthStore()

const menuItems = computed<MenuItem[]>(() => {
  if (auth.user?.isSuperadmin) return menuItemsSuperadmin
  if (auth.roles.includes('school_admin')) return menuItemsSchoolAdmin
  if (auth.roles.includes('canteen_admin')) return menuItemsCanteenAdmin
  if (auth.roles.includes('cashier')) return menuItemsCashier
  if (auth.roles.includes('teacher')) return menuItemsTeachers
  if (auth.roles.includes('student')) return menuItemsStudent
  if (auth.roles.includes('headmaster')) return menuItemsHeadmaster
  return []
})
</script>

<template>
  <PanelMenu class="layout-menu" :model="menuItems">
    <template #item="{ item }">
      <router-link v-if="item.route" v-slot="{ href, navigate }" :to="item.route" custom>
        <a class="flex items-center cursor-pointer px-4 py-2" :href="href" @click="navigate">
          <span :class="item.icon" />
          <span class="ml-2">{{ item.label }}</span>
        </a>
      </router-link>
      <a
        v-else
        class="flex items-center cursor-pointer px-4 py-2"
        :href="item.url"
        :target="item.target"
      >
        <span :class="item.icon" />
        <span class="ml-2">{{ item.label }}</span>
        <span v-if="item.items" class="pi pi-angle-down text-primary ml-auto" />
      </a>
    </template>
  </PanelMenu>
</template>

<style scoped>
.layout-menu {
  min-width: 16em;
  background-color: var(--surface-0);
  border-right: 1px solid var(--border-1);
}
</style>
