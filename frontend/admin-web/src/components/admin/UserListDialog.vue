<script setup lang="ts">
import { Button } from 'primevue'
import { ref } from 'vue'

import Dialog from '@/components/common/Dialog.vue'
import SearchField from '@/components/common/SearchField.vue'
import Table, { type TableColumn } from '@/components/common/Table.vue'
import UserServices from '@/services/user.service'
import type { QueryParams } from '@/types/queryParams.type'
import type { User } from '@/types/user.type'

interface Props {
  visible: boolean
}

interface Emits {
  close: []
  selected: [user: User]
}

defineProps<Props>()
defineEmits<Emits>()

const userList = ref<User[]>([])
const totalRecords = ref<number>(0)
const key = ref<number>(0)

const columns = ref<TableColumn[]>([
  { header: 'Nama', field: 'name' },
  { header: 'Email', field: 'email' },
])

const loadUsers = async (params?: QueryParams) => {
  try {
    const { data } = await UserServices.getAllUsers(params)
    userList.value = data.data
    totalRecords.value = data.totalRecords
  } catch (error) {
    console.error(error)
  }
}
</script>

<template>
  <Dialog title="Data Pengguna" :visible @close="$emit('close')" size="large">
    <SearchField table-name="users" />
    <Table :key :data="userList" :total-records :fetch="loadUsers" :columns table-name="users">
      <template #action="{ data }">
        <Button @click="$emit('selected', data)" label="Pilih" />
      </template>
    </Table>
  </Dialog>
</template>
