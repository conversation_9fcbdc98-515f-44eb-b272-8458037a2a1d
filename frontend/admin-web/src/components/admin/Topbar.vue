<script setup lang="ts">
import { Button } from 'primevue'

import Logo from '@/assets/tias_logo.png'
import { useAuthStore } from '@/stores/auth'

import Row from '../common/Row.vue'

interface Emits {
  toggleSidebar: () => void
}

defineEmits<Emits>()

const { user } = useAuthStore()
</script>

<template>
  <Row class="justify-between items-center px-10 py-4 w-full bg-white shadow-md">
    <Row class="items-center gap-8 cursor-pointer">
      <Button
        @click="$emit('toggleSidebar')"
        icon="pi pi-bars"
        class="p-button-rounded"
        severity="contrast"
        variant="outlined"
      />
      <img :src="Logo" alt="logo" class="w-10" />
    </Row>
    <RouterLink :to="{ name: 'account.changePassword' }" class="no-underline text-inherit">
      <Row class="items-center gap-4">
        <img
          src="https://www.rollingstone.com/wp-content/uploads/2025/04/lil-nas-x-facial-paralysis.jpg?w=1581&h=1054&crop=1"
          alt="person"
          class="w-10 h-10 rounded-full"
        />
        <h4>Hello, {{ user.name }}!</h4>
      </Row>
    </RouterLink>
  </Row>
</template>
