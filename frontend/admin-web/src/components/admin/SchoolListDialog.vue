<script setup lang="ts">
import { Button } from 'primevue'
import { ref } from 'vue'

import Dialog from '@/components/common/Dialog.vue'
import SearchField from '@/components/common/SearchField.vue'
import Table, { type TableColumn } from '@/components/common/Table.vue'
import SchoolServices from '@/services/school.service'
import type { QueryParams } from '@/types/queryParams.type'
import type { School } from '@/types/school.type'

interface Props {
  visible: boolean
}

interface Emits {
  close: []
  selected: [school: School]
}

defineProps<Props>()
defineEmits<Emits>()

const schoolList = ref<School[]>([])
const totalRecords = ref<number>(0)
const key = ref<number>(0)

const columns = ref<TableColumn[]>([
  { header: 'Nama', field: 'name' },
  { header: 'Email', field: 'email' },
])

const loadSchools = async (params?: QueryParams) => {
  try {
    const { data } = await SchoolServices.getAllSchools(params)
    schoolList.value = data.data
    totalRecords.value = data.totalRecords
  } catch (error) {
    console.error(error)
  }
}
</script>

<template>
  <Dialog title="Data Sekolah" :visible @close="$emit('close')" size="large">
    <SearchField table-name="schools" />
    <Table
      :key
      :data="schoolList"
      :total-records
      :fetch="loadSchools"
      :columns
      table-name="schools"
    >
      <template #action="{ data }">
        <Button @click="$emit('selected', data)" label="Pilih" />
      </template>
    </Table>
  </Dialog>
</template>
