<template>
  <div class="flex gap-4">
    <div class="card p-6 bg-white shadow-xl rounded-xl w-full">
      <h1 class="font-semibold text-2xl mb-4">{{ title }}</h1>
      <Chart type="pie" :data="pieChartData" :options="pieOptions" class="h-[30rem]" />
    </div>
    <div class="card p-6 bg-white shadow-xl rounded-xl w-full">
      <h1 class="font-semibold text-2xl mb-4">{{ title }}</h1>
      <Chart type="bar" :data="barChartData" :options="chartOptions" class="h-[30rem]" />

      <div class="flex items-center justify-between mt-6">
        <span class="text-gray-600">Halaman {{ currentPage }} dari {{ totalPages }}</span>
        <div class="flex gap-2">
          <button @click="prevPage" :disabled="currentPage === 1" :class="btn">Kembali</button>
          <button @click="nextPage" :disabled="currentPage === totalPages" :class="btn">
            Lanjut
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Chart from 'primevue/chart'
import { ref, watch, computed, onMounted } from 'vue'

import { getAbsenceReason } from '@/enums/absenceReason.enum'

const props = defineProps<{
  title: string
  fetchFn: (params: { page: number }) => Promise<any>
  statuses: string[] // misal ['present', 'absent']
  statusKeyMap: Record<string, string> // misal { present: 'totalPresent', absent: 'totalAbsent' }
  colorVars: Record<string, string> // misal { present: '--p-green-500', absent: '--p-red-500' }
}>()

const currentPage = ref(1)
const perPage = 10
const totalRecords = ref(0)
const labels = ref<string[]>([])
const dataMap = ref<Record<string, number[]>>({})

const fetchChartData = async () => {
  try {
    const res = await props.fetchFn({ page: currentPage.value })
    totalRecords.value = res.data.totalRecords
    const data = res.data.data

    labels.value = data.map((item: any) => item.name)

    const map: Record<string, number[]> = {}
    props.statuses.forEach((status) => {
      const key = props.statusKeyMap[status]
      map[status] = data.map((item: any) => item[key] ?? 0)
    })
    dataMap.value = map
  } catch (e) {
    console.error('Gagal fetch data:', e)
  }
}

const barChartData = computed(() => {
  const style = getComputedStyle(document.documentElement)
  return {
    labels: labels.value,
    datasets: props.statuses.map((status) => ({
      type: 'bar',
      label: statusLabel(status),
      backgroundColor: style.getPropertyValue(props.colorVars[status]) || '#ccc',
      data: dataMap.value[status] || [],
      borderRadius: 8,
      barPercentage: 0.5,
    })),
  }
})

const pieChartData = computed(() => {
  const style = getComputedStyle(document.documentElement)
  const totals = props.statuses.map((status) =>
    (dataMap.value[status] || []).reduce((a, b) => a + b, 0),
  )
  return {
    labels: props.statuses.map(statusLabel),
    datasets: [
      {
        data: totals,
        backgroundColor: props.statuses.map(
          (status) => style.getPropertyValue(props.colorVars[status]) || '#ccc',
        ),
        borderWidth: 1,
      },
    ],
  }
})

const chartOptions = computed(() => {
  const style = getComputedStyle(document.documentElement)
  const textColor = style.getPropertyValue('--p-text-color') || '#1f2937'
  const gridColor = style.getPropertyValue('--p-content-border-color') || '#e5e7eb'
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
    plugins: {
      legend: {
        labels: {
          color: textColor,
          boxWidth: 12,
        },
      },
      tooltip: {
        backgroundColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
        titleColor: textColor,
        bodyColor: textColor,
        titleFont: { weight: 'bold' },
        padding: 10,
      },
    },
    scales: {
      x: {
        stacked: true,
        grid: {
          color: gridColor,
        },
        ticks: {
          color: textColor,
        },
      },
      y: {
        stacked: true,
        grid: {
          color: gridColor,
        },
        ticks: {
          color: textColor,
          beginAtZero: true,
        },
      },
    },
  }
})

const pieOptions = computed(() => {
  const style = getComputedStyle(document.documentElement)
  const total = props.statuses.reduce(
    (sum, status) => sum + (dataMap.value[status] || []).reduce((a, b) => a + b, 0),
    0,
  )
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: style.getPropertyValue('--p-text-color') || '#1f2937',
        },
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem: any) {
            const value = tooltipItem.raw
            const percentage = ((value / total) * 100).toFixed(1)
            return `${tooltipItem.label}: ${value} (${percentage}%)`
          },
        },
      },
    },
  }
})

const totalPages = computed(() => Math.ceil(totalRecords.value / perPage))

function prevPage() {
  if (currentPage.value > 1) currentPage.value--
}

function nextPage() {
  if (currentPage.value < totalPages.value) currentPage.value++
}

watch(currentPage, fetchChartData)
onMounted(fetchChartData)

function statusLabel(status: string) {
  return getAbsenceReason(status)
}

const btn = 'px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400'
</script>

<style scoped>
button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
