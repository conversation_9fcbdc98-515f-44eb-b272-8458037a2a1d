import { QuestionType } from '@/enums/questionType.enum'

export interface AnswerOptionForm {
  content: string
  isCorrect: boolean
  mediaAnswerOptions?: File | null
}

export interface QuestionForm {
  content?: string
  answerKeyEssay?: string
  points?: number
  questionType: QuestionType
  orderIndex?: number
  kdNumber?: string | null
  learningOutcome?: string | null
  competencyIndicator?: string | null
  levelKognitif?: string | null
  mediaQuestions?: File | null
  mediaQuestionsUrl?: string | null
  answerOptions?: AnswerOptionForm[]
}

// Builder: ubah array QuestionForm => FormData (sesuai format backend sekarang)
export function buildQuestionsFormData(questions: QuestionForm[]): FormData {
  const formData = new FormData()

  questions.forEach((q, qIndex) => {
    if ((q as any).id) {
      formData.append(`questions[${qIndex}][id]`, String((q as any).id))
    }

    formData.append(`questions[${qIndex}][content]`, q.content ?? '')
    formData.append(`questions[${qIndex}][answer_key_essay]`, q.answerKeyEssay ?? '')
    formData.append(`questions[${qIndex}][question_type]`, q.questionType)
    formData.append(`questions[${qIndex}][points]`, String(q.points ?? 0))
    formData.append(`questions[${qIndex}][order_index]`, String(q.orderIndex ?? qIndex + 1))

    if (q.kdNumber) formData.append(`questions[${qIndex}][kd_number]`, q.kdNumber)
    if (q.learningOutcome)
      formData.append(`questions[${qIndex}][learning_outcome]`, q.learningOutcome)
    if (q.competencyIndicator)
      formData.append(`questions[${qIndex}][competency_indicator]`, q.competencyIndicator)
    if (q.levelKognitif) formData.append(`questions[${qIndex}][level_kognitif]`, q.levelKognitif)
    if (q.mediaQuestions) formData.append(`questions[${qIndex}][media_questions]`, q.mediaQuestions)
    if (q.mediaQuestionsUrl)
      formData.append(`questions[${qIndex}][media_questions]`, q.mediaQuestionsUrl)

    if (q.questionType === 'multiple_choice' && q.answerOptions?.length) {
      q.answerOptions.forEach((opt, oIndex) => {
        formData.append(
          `questions[${qIndex}][answer_options][${oIndex}][content]`,
          opt.content ?? '',
        )
        formData.append(
          `questions[${qIndex}][answer_options][${oIndex}][is_correct]`,
          opt.isCorrect ? '1' : '0',
        )
        formData.append(
          `questions[${qIndex}][answer_options][${oIndex}][order_index]`,
          String(oIndex + 1),
        )
        if (opt.mediaAnswerOptions) {
          formData.append(
            `questions[${qIndex}][answer_options][${oIndex}][media_answer_options]`,
            opt.mediaAnswerOptions,
          )
        }
      })
    }
  })

  return formData
}
