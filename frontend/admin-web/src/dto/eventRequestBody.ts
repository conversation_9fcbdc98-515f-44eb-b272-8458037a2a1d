export interface StoreEventRequestBody {
  schoolId?: number
  title?: string
  excerpt?: string
  thumbnailFile?: File
  content?: string
  location?: string
  eventDate?: Date
  startTime?: Date
  endTime?: Date
  isPublished?: boolean
  files?: File[]
  deletedFiles?: number[] // Added to make it consistent with UpdateEventRequestBody
}

export interface UpdateEventRequestBody {
  schoolId?: number
  title?: string
  excerpt?: string
  thumbnailFile?: File
  clearThumbnail?: boolean
  content?: string
  location?: string
  eventDate?: Date
  startTime?: Date
  endTime?: Date
  isPublished?: boolean
  files?: File[]
  deletedFiles?: number[]
}
