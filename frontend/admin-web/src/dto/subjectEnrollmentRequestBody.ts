export interface ToggleCellRequestDTO {
  termId: number
  subClassroomSubjectId: number
  userId: number
  isEnrolled: boolean
  note?: string | null
}

export interface BulkUpsertRowDTO {
  subClassroomSubjectId: number
  userId: number
  isEnrolled: boolean
  note?: string | null
}

export interface BulkUpsertRequestDTO {
  termId: number
  subClassroomId: number
  rows: BulkUpsertRowDTO[]
}

export interface SetDefaultClassRequestDTO {
  termId: number
  default: boolean
  note?: string | null
}

export interface ToggleAllColumnRequestDTO {
  termId: number
  isEnrolled: boolean
  note?: string | null
}

export interface ToggleAllRowRequestDTO {
  termId: number
  isEnrolled: boolean
  note?: string | null
}
