import type { AxiosResponse } from 'axios'

import type { examTokenRequestBody } from '@/dto/examTokenRequestBody'

import { API } from './api'

const api = API({ prefix: 'ExamToken' })
/**
 * A service object for managing extracurricular-related API calls.
 */
const ExamTokenServices = {
  generateExamTokenForStudent: (
    userId: number,
    data: examTokenRequestBody,
  ): Promise<AxiosResponse> => {
    return api.post(`/generate-token/${userId}`, data)
  },

  generateExamTokenForAllStudent: (data: examTokenRequestBody): Promise<AxiosResponse> => {
    return api.post('/generate-token-all', data)
  },
}

export default ExamTokenServices
