import type { AxiosResponse } from 'axios'

import type { CopyQuestionRequestBody } from '@/dto/copyQuestionRequestBody'
import type { ExamRequestBody } from '@/dto/examRequestBody'
import type { examTokenRequestBody } from '@/dto/examTokenRequestBody'
import type { GradeEssayRequestBody } from '@/dto/gradeEssayRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'exams' })
const fileApi = API_FILE({ prefix: 'exams' })

/**
 * A service object for managing extracurricular-related API calls.
 */
const ExamServices = {
  /**
   * Fetches all exams with optional query parameters.
   *
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise that resolves to the Axios response containing the list of exams.
   */
  getAllExams: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },
  /**
   * Fetches a specific exam by its ID.
   *
   * @param id - The ID of the exam to retrieve.
   * @returns A promise that resolves to the Axios response containing the exam details.
   */
  getExamById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new exam.
   *
   * @param data - The data for the new exam.
   * @returns A promise that resolves to the Axios response after creating the exam.
   */
  createExam: (data: ExamRequestBody): Promise<AxiosResponse> => {
    return api.post('', data)
  },
  /**
   * Updates an existing exam by its ID.
   *
   * @param id - The ID of the exam to update.
   * @param data - The updated exam data.
   * @returns A promise that resolves to the Axios response after updating the exam.
   */
  updateExam: (id: number, data: ExamRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, data)
  },
  /**
   * Deletes an exam by its ID.
   *
   * @param id - The ID of the exam to delete.
   * @returns A promise that resolves to the Axios response after deletion.
   */
  deleteExam: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  /**
   * Publishes an exam by its ID.
   *
   * @param id - The ID of the exam to publish.
   * @returns A promise that resolves to the Axios response after publishing the exam.
   */
  publishExam: (id: number): Promise<AxiosResponse> => {
    return api.post(`/${id}/publish`)
  },

  /**
   * Unpublishes an exam by its ID.
   *
   * @param id - The ID of the exam to unpublish.
   * @returns A promise that resolves to the Axios response after unpublishing the exam.
   */
  unPublishExam: (id: number): Promise<AxiosResponse> => {
    return api.post(`/${id}/unpublish`)
  },

  createQuestionsBulk: (examId: number, data: FormData): Promise<AxiosResponse> => {
    return api.post(`/${examId}/questions-bulk`, data)
  },

  getQuestionsByExam: (examId: number): Promise<AxiosResponse> => {
    return api.get(`/${examId}/questions`)
  },

  startExam: (examId: number, examToken: examTokenRequestBody): Promise<AxiosResponse> => {
    return api.post(`/${examId}/start`, examToken)
  },

  submitExam: (examId: number): Promise<AxiosResponse> => {
    return api.post(`/${examId}/submit`)
  },

  gradeExam: (examId: number): Promise<AxiosResponse> => {
    return api.post(`/${examId}/grade`)
  },

  resultExam: (examId: number, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/${examId}/results`, { params: { ...params } })
  },

  countExamStudents: (examId: number): Promise<AxiosResponse> => {
    return api.get(`/${examId}/count`)
  },

  resetStudentExam: (examId: number, studentId: number): Promise<AxiosResponse> => {
    return api.post(`/${examId}/student/${studentId}/reset`)
  },

  forceSubmitStudentExam: (examId: number, studentId: number): Promise<AxiosResponse> => {
    return api.post(`/${examId}/student/${studentId}/force-submit`)
  },

  getStudentAttempt: (
    examId: number,
    studentId: number,
    attemptId: number,
  ): Promise<AxiosResponse> => {
    return api.get(`/${examId}/students/${studentId}/attempts/${attemptId}`)
  },
  gradeEssay: (attemptId: number, gradeEssay: GradeEssayRequestBody): Promise<AxiosResponse> => {
    return api.post(`/${attemptId}/grade-essay`, gradeEssay)
  },

  duplicateExam: (examId: number): Promise<AxiosResponse> => {
    return api.post(`/${examId}/duplicate`)
  },

  assignExamToSubClassroomSubject: (
    examId: number,
    subClassroomSubjectId: number,
  ): Promise<AxiosResponse> => {
    return api.patch(`/${examId}/assignTeacher`, {
      sub_classroom_subject_id: subClassroomSubjectId,
    })
  },

  downloadQuestionsCard(examId: number): Promise<AxiosResponse<Blob>> {
    return fileApi.post(
      '/export/questions-card',
      { exam_id: examId },
      { headers: { 'Content-Type': 'application/json' } },
    )
  },

  downloadQuestionsGrid(examId: number): Promise<AxiosResponse<Blob>> {
    return fileApi.post(
      '/export/questions-grid',
      { exam_id: examId },
      { headers: { 'Content-Type': 'application/json' } },
    )
  },

  downloadTemplateQuestions(): Promise<AxiosResponse<Blob>> {
    return fileApi.get('/questions/template')
  },

  importQuestions(examId: number, data: FormData): Promise<AxiosResponse> {
    return fileApi.post(`/${examId}/questions/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },

  getCopySources: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/questions/copy/sources', { params: { ...params } })
  },

  getQuestionsOfSource(sourceExamId: number) {
    return api.get(`/questions/copy/sources/${sourceExamId}/questions`)
  },

  copyQuestionsFromExam(targetExamId: number, payload: CopyQuestionRequestBody) {
    return api.post(`/${targetExamId}/questions/copyFrom`, payload)
  },
}

export default ExamServices
