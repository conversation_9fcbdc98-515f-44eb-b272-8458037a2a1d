import type { AxiosResponse } from 'axios'

import type { FinancialCoreRequestBody } from '@/dto/financialCoreRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'financial-core' })

/**
 * A service object for managing Financial Core through API calls.
 */
const FinancialCoreServices = {
  /**
   * Fetches all Financial Core transactions with optional pagination.
   *
   * @param params - Optional query parameters for filtering the transactions.
   * @returns A promise that resolves to the Axios response containing the transactions.
   */
  getAllFinancialCore: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Fetches a Financial Core transaction by its unique identifier.
   *
   * @param id - The unique identifier of the transaction.
   * @returns A promise that resolves to the Axios response containing the transaction data.
   */
  getFinancialCoreById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new Financial Core transaction with the provided data.
   *
   * @param data - The transaction data to be created.
   * @returns A promise that resolves to the Axios response of the creation operation.
   */
  createFinancialCore: (data: FinancialCoreRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  /**
   * Updates an existing Financial Core transaction by its unique identifier.
   *
   * @param id - The unique identifier of the transaction to be updated.
   * @param data - The updated transaction data.
   * @returns A promise that resolves to the Axios response of the update operation.
   */
  updateFinancialCore: (id: number, data: FinancialCoreRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  /**
   * Deletes a Financial Core transaction by its unique identifier.
   *
   * @param id - The unique identifier of the transaction to be deleted.
   * @returns A promise that resolves to the Axios response of the deletion operation.
   */
  deleteFinancialCore: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  /**
   * Get financial summary with optional filters.
   *
   * @param params - Optional query parameters for filtering the summary.
   * @returns A promise that resolves to the Axios response containing the summary.
   */
  getFinancialSummary: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/summary', { params: { ...params } })
  },
}

export default FinancialCoreServices