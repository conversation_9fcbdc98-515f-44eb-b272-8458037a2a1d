import type { AxiosResponse } from 'axios'

import type { DormitoryRequestBody } from '@/dto/dormitoryRequestBody'

import { API } from './api'

const api = API({ prefix: 'dormitories' })

/**
 * A service object for managing dormitories.
 */
const DormitoryServices = {
  /**
   * Retrieves all dormitories with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to an AxiosResponse containing the dormitories.
   */
  getAllDormitories: (page?: number): Promise<AxiosResponse> => {
    return api.get('', { params: { page } })
  },

  /**
   * Retrieves a dormitory by its ID.
   *
   * @param id - The ID of the dormitory to retrieve.
   * @returns A promise that resolves to an AxiosResponse containing the dormitory details.
   */
  getDormitoryById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new dormitory.
   *
   * @param data - The request body containing the details of the dormitory to create.
   * @returns A promise that resolves to an AxiosResponse containing the created dormitory.
   */
  createDormitory: (data: DormitoryRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  /**
   * Updates an existing dormitory by its ID.
   *
   * @param id - The ID of the dormitory to update.
   * @param data - The request body containing the updated details of the dormitory.
   * @returns A promise that resolves to an AxiosResponse containing the updated dormitory.
   */
  updateDormitory: (id: number, data: DormitoryRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  /**
   * Deletes a dormitory by its ID.
   *
   * @param id - The ID of the dormitory to delete.
   * @returns A promise that resolves to an AxiosResponse confirming the deletion.
   */
  deleteDormitory: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default DormitoryServices
