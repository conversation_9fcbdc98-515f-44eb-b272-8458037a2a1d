import type { AxiosResponse } from 'axios'

import { API, API_FILE } from './api'

const api = API({ prefix: 'classAttendance' })
const apiFile = API_FILE({ prefix: 'classAttendance' })

const classAttendanceService = {
  getAllClassAttendance: (page?: number): Promise<AxiosResponse> => {
    return api.get('', { params: { page } })
  },

  getClassAttendanceById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  createClassAttendance: (data: string): Promise<AxiosResponse> => {
    return api.post('', { classAttendance: data })
  },

  updateClassAttendance: (id: number, data: string): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { classAttendance: data })
  },

  deleteClassAttendance: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  list: (termId: number, subClassroomId?: number): Promise<AxiosResponse> => {
    return api.get('', {
      params: { term_id: termId, sub_classroom_id: subClassroomId },
    })
  },

  upsert: (payload: {
    term_id: number
    student_user_id: number
    sick: number
    leave: number
    present: number
    alpha: number
  }): Promise<AxiosResponse> => {
    return api.post('/upsert', payload)
  },
  bulkAppend: (
    items: Array<{
      term_id: number
      student_user_id: number
      sick?: number
      leave?: number
      present?: number
      alpha?: number
    }>,
  ): Promise<AxiosResponse> => {
    return api.post('/bulk-append', { items })
  },
  adjust: (
    id: number,
    field: 'sick' | 'leave' | 'present' | 'alpha',
    delta: number,
  ): Promise<AxiosResponse> => {
    return api.patch(`/${id}/adjust`, { field, delta })
  },
  downloadTemplate: (termId: number, subClassroomId?: number): Promise<AxiosResponse> => {
    return apiFile.get('/template', {
      params: { term_id: termId, sub_classroom_id: subClassroomId },
      responseType: 'blob',
    })
  },
  importExcel: (file: File): Promise<AxiosResponse> => {
    const fd = new FormData()
    fd.append('file', file)
    return api.post('/import', fd, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },
}

export default classAttendanceService
