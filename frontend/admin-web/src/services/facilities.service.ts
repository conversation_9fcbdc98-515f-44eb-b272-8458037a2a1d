import type { AxiosResponse } from 'axios'

import type { QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'facilities' })
const apiFile = API_FILE({ prefix: 'facilities' })

/**
 * A service object for managing Fasilitas-related API calls.
 */
const FacilitiesServices = {
  /**
   * Fetches all facilities with optional query parameters.
   * @param params Optional query parameters for filtering or pagination.
   * @returns A promise that resolves to the Axios response containing facilities data.
   */
  getAllFacilities: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },
  /**
   * Fetches a facility by its ID.
   * @param id The ID of the facility to fetch.
   * @returns A promise that resolves to the Axios response containing the facility data.
   */
  getFacilityById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },
  /**
   * Creates a new facility.
   * @param data The data for the new facility.
   * @returns A promise that resolves to the Axios response after creating the facility.
   */
  createFacility: (data: FormData): Promise<AxiosResponse> => {
    return api.post('', data)
  },
  /**
   * Updates an existing facility by its ID.
   * @param id The ID of the facility to update.
   * @param data The updated data for the facility.
   * @returns A promise that resolves to the Axios response after updating the facility.
   */
  updateFacility: (id: number, data: FormData): Promise<AxiosResponse> => {
    data.append('_method', 'PUT')
    return api.post(`/${id}`, data)
  },
  /**
   * Deletes a facility by its ID.
   * @param id The ID of the facility to delete.
   * @returns A promise that resolves to the Axios response after deleting the facility.
   */
  deleteFacility: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  /**
   * Imports facilities from a file.
   *
   * @param file - The file containing facility data to import.
   * @returns A promise that resolves to the Axios response of the import operation.
   */
  importFacility: (file: File): Promise<AxiosResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiFile.post('/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * Downloads a template for importing facilities.
   *
   * @returns A promise that resolves to the Axios response containing the template file.
   */
  getImportTemplate: (): Promise<AxiosResponse> => {
    // Get active school ID from auth store
    const { useAuthStore } = require('@/stores/auth')
    const auth = useAuthStore()
    
    const headers: Record<string, string> = {
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    
    // Add X-School-ID header if available
    if (auth.isAuthenticated && auth.activeSchool && !auth.user?.isSuperadmin) {
      headers['X-School-ID'] = auth.activeSchool.id.toString()
    }
    
    return apiFile.get('/template', {
      responseType: 'blob',
      headers
    })
  },

  /**
   * Exports facilities to a file.
   *
   * @param params Optional query parameters for filtering the exported data.
   * @returns A promise that resolves to the Axios response containing the exported file.
   */
  exportFacility: (params?: QueryParams): Promise<AxiosResponse> => {
    return apiFile.get('/export', {
      params,
      responseType: 'blob'
    })
  },
}

export default FacilitiesServices
