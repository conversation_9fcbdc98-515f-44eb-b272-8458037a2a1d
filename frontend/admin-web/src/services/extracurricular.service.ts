import type { AxiosResponse } from 'axios'

import type { ExtracurricularRequestBody } from '@/dto/extracurricularRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'extracurricular' })
type ListEnrollmentsParams = QueryParams & {
  termId?: number | null
  classId?: number | null
}
/**
 * A service object for managing extracurricular-related API calls.
 */
const ExtracurricularServices = {
  /**
   * Fetches all extracurriculars with optional query parameters.
   *
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise that resolves to the Axios response containing the list of extracurriculars.
   */
  getAllExtracurriculars: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Fetches a specific extracurricular by its ID.
   *
   * @param id - The ID of the extracurricular to retrieve.
   * @returns A promise that resolves to the Axios response containing the extracurricular details.
   */
  getExtracurricularById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Fetches all students associated with a specific extracurricular by its ID.
   *
   * @param id - The ID of the extracurricular to retrieve students for.
   * @returns A promise that resolves to the Axios response containing the list of students.
   */
  getExtracurricularStudents: (id: number, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/${id}/students`, { params: { ...params } })
  },

  /**
   * Fetches attendance records for students in a specific extracurricular by its ID.
   *
   * @param id - The ID of the extracurricular to retrieve attendance records for.
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise that resolves to the Axios response containing the attendance records.
   */
  getAttendances: (id: number, date?: Date, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/${id}/getAttendances`, {
      params: { ...params, date: Math.floor((date?.getTime() || new Date().getTime()) / 1000) },
    })
  },

  /**
   * Creates a new extracurricular.
   *
   * @param data - The request body containing the details of the extracurricular to create.
   * @returns A promise that resolves to the Axios response of the creation operation.
   */
  createExtracurricular: (data: ExtracurricularRequestBody): Promise<AxiosResponse> => {
    return api.post('', data)
  },

  /**
   * Updates an existing extracurricular by its ID.
   *
   * @param id - The ID of the extracurricular to update.
   * @param data - The request body containing the updated details of the extracurricular.
   * @returns A promise that resolves to the Axios response of the update operation.
   */
  updateExtracurricular: (id: number, data: ExtracurricularRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, data)
  },

  /**
   * Deletes an extracurricular by its ID.
   *
   * @param id - The ID of the extracurricular to delete.
   * @returns A promise that resolves to the Axios response of the deletion operation.
   */
  deleteExtracurricular: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  listEnrollments: (params: ListEnrollmentsParams): Promise<AxiosResponse> => {
    return api.get('/students', {
      params: {
        term_id: params?.termId ?? null,
        class_id: params?.classId ?? null,
        per_page: params?.limit,
        page: params?.page,
      },
    })
  },

  // POST /extracurricular/students
  enroll: (body: { studentUserId: number; termId: number }): Promise<AxiosResponse> => {
    return api.post('/students', {
      student_user_id: body.studentUserId,
      term_id: body.termId,
    })
  },

  // PUT /extracurricular/students/{id}
  update: (id: number, body: { predicate: 'SB' | 'B' | 'C' | 'K' }): Promise<AxiosResponse> => {
    return api.put(`/students/${id}`, body)
  },

  // DELETE /extracurricular/students/{id}
  remove: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/students/${id}`)
  },

  // GET /extracurricular/classes/{subClassroomId}/students
  studentsByClass: (subClassroomId: number): Promise<AxiosResponse> => {
    return api.get(`/classes/${subClassroomId}/students`)
  },
}

export default ExtracurricularServices
