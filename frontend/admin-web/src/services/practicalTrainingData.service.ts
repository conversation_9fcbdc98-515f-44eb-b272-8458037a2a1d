import type { AxiosResponse } from 'axios'

import type { PracticalTrainingDataRequestBody } from '@/dto/practicalTrainingDataRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'practicalTrainingData' })
const apiFile = API_FILE({ prefix: 'practicalTrainingData' })

const PracticalTrainingDataServices = {
  listByTraining: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  getPracticalTrainingDataById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  createPracticalTrainingData: (data: PracticalTrainingDataRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  updatePracticalTrainingData: (
    id: number,
    data: PracticalTrainingDataRequestBody,
  ): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  deletePracticalTrainingData: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  downloadImportTemplate: (practicalTrainingId: number): Promise<AxiosResponse<Blob>> => {
    return apiFile.get('/import/template', {
      params: { practicalTrainingId },
      responseType: 'blob',
    })
  },

  importData: (practicalTrainingId: number, file: File): Promise<AxiosResponse> => {
    const form = new FormData()
    form.append('file', file)
    form.append('practical_training_id', String(practicalTrainingId))
    return api.post('/import', form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },
}

export default PracticalTrainingDataServices
