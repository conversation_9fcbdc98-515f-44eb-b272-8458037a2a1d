import type { AxiosResponse } from 'axios'

import type { PositionRequestBody } from '@/dto/positionRequestBody'

import { API } from './api'

const api = API({ prefix: 'position' })

/**
 * A service object for managing positions.
 */
const PositionServices = {
  /**
   * Retrieves all positions with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to an AxiosResponse containing the positions.
   */
  getAllPositions: (page?: number): Promise<AxiosResponse> => {
    return api.get('', { params: { page } })
  },

  /**
   * Retrieves a position by its ID.
   *
   * @param id - The ID of the position to retrieve.
   * @returns A promise that resolves to an AxiosResponse containing the position details.
   */
  getPositionById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new position.
   *
   * @param data - The request body containing the details of the position to create.
   * @returns A promise that resolves to an AxiosResponse containing the created position.
   */
  createPosition: (data: PositionRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  /**
   * Updates an existing position by its ID.
   *
   * @param id - The ID of the position to update.
   * @param data - The request body containing the updated details of the position.
   * @returns A promise that resolves to an AxiosResponse containing the updated position.
   */
  updatePosition: (id: number, data: PositionRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  /**
   * Deletes a position by its ID.
   *
   * @param id - The ID of the position to delete.
   * @returns A promise that resolves to an AxiosResponse confirming the deletion.
   */
  deletePosition: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default PositionServices
