import type { AxiosResponse } from 'axios'

import type { PracticalTrainingRequestBody } from '@/dto/practicalTrainingRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'practicalTraining' })

const PracticalTrainingServices = {
  getAllPracticalTrainings: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  getPracticalTrainingById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  createPracticalTraining: (data: PracticalTrainingRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  updatePracticalTraining: (
    id: number,
    data: PracticalTrainingRequestBody,
  ): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  deletePracticalTraining: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default PracticalTrainingServices
