import type { AxiosResponse } from 'axios'

import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'subClassroom' })

/**
 * A service object for managing sub-classrooms, including retrieving data and assigning students, teachers, and subjects.
 */
const SubClassroomServices = {
  /**
   * Retrieves all sub-classrooms with optional query parameters.
   *
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise resolving to the Axios response containing the list of sub-classrooms.
   */
  getAllSubClassrooms: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Retrieves the count of sub-classrooms.
   *
   * @returns A promise resolving to the Axios response containing the count of sub-classrooms.
   */
  getCount: (): Promise<AxiosResponse> => {
    return api.get('/count')
  },

  /**
   * Updates the sequence of a specific sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param sequence - The new sequence value.
   * @returns A promise resolving to the Axios response indicating the result of the operation.
   */
  updateSequence: (subClassroomId: number, sequence: string): Promise<AxiosResponse> => {
    return api.put(`/${subClassroomId}`, { sequence })
  },

  /**
   * Retrieves the list of students associated with a specific sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise resolving to the Axios response containing the list of students.
   */
  getStudentsBySubClassroomId: (
    subClassroomId: number,
    params?: QueryParams,
  ): Promise<AxiosResponse> => {
    return api.get(`/${subClassroomId}/students`, { params: { ...params } })
  },

  /**
   * Retrieves the list of subjects associated with a specific sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise resolving to the Axios response containing the list of subjects.
   */
  getSubjectsBySubClassroomId: (
    subClassroomId: number,
    params?: QueryParams,
  ): Promise<AxiosResponse> => {
    return api.get(`/${subClassroomId}/subjects`, { params: { ...params } })
  },

  /**
   * Assigns one or more students to a specific sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param studentUserIds - An array of student IDs to be assigned.
   * @returns A promise resolving to the Axios response indicating the result of the operation.
   */
  assignStudentToSubClassroom: (
    subClassroomId: number,
    studentUserIds: number[],
  ): Promise<AxiosResponse> => {
    return api.post(`/${subClassroomId}/assignStudents`, { student_user_ids: studentUserIds })
  },

  /**
   * Assigns a teacher to a specific sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param teacherUserId - The ID of the teacher to be assigned.
   * @returns A promise resolving to the Axios response indicating the result of the operation.
   */
  assignTeacher: (subClassroomId: number, teacherUserId: number): Promise<AxiosResponse> => {
    return api.post(`/${subClassroomId}/assignTeacher`, { teacher_user_id: teacherUserId })
  },

  /**
   * Assigns a teacher to a specific subject within a sub-classroom.
   *
   * @param subClassroomId - The ID of the sub-classroom.
   * @param teacherUserId - The ID of the teacher to be assigned.
   * @param subjectId - The ID of the subject to be assigned.
   * @returns A promise resolving to the Axios response indicating the result of the operation.
   */
  assignSubjectTeacher: (
    subClassroomId: number,
    teacherUserId: number,
    subjectId: number,
  ): Promise<AxiosResponse> => {
    return api.post(`/${subClassroomId}/assignSubjectTeacher`, {
      teacher_user_id: teacherUserId,
      subject_id: subjectId,
    })
  },
}

export default SubClassroomServices
