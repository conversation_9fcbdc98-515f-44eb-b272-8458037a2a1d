import type { AxiosResponse } from 'axios'

import type { CanteenRequestBody } from '@/dto/canteenRequestBody'
import type { PurchaseRequestBody } from '@/dto/purchaseRequestBody'
import type { TransactionRequestBody } from '@/dto/transactionRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'canteens' })

const CanteenServices = {
  /**
   * Fetches all canteens with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to the Axios response containing the canteens.
   */
  getAllCanteens: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params })
  },
  /**
   *
   * @param id
   * @returns
   */
  getCanteenById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },
  /**
   *
   * @param data
   * @returns
   */
  createCanteen: (data: CanteenRequestBody): Promise<AxiosResponse> => {
    return api.post('', data)
  },
  /**
   *
   * @param id
   * @param data
   * @returns
   */
  updateCanteen: (id: number, data: CanteenRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, data)
  },
  /**
   *
   * @param id
   * @returns
   */
  deleteCanteen: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
  /**
   *
   * @param params
   * @returns
   */
  getAllProducts: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/products', { params })
  },
  /**
   *
   * @param id
   * @returns
   */
  getProductById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/products/${id}`)
  },
  /**
   *
   * @param data
   * @returns
   */
  createProduct: (data: FormData): Promise<AxiosResponse> => {
    return api.post('/products', data)
  },
  /**
   *
   * @param id
   * @param data
   * @returns
   */
  updateProduct: (id: number, data: FormData): Promise<AxiosResponse> => {
    data.append('_method', 'PUT')
    return api.post(`/products/${id}`, data)
  },
  /**
   *
   * @param id
   * @returns
   */
  deleteProduct: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/products/${id}`)
  },
  /**
   *
   * @param data
   * @returns
   */
  createTransaction: (data: TransactionRequestBody): Promise<AxiosResponse> => {
    return api.post('/transactions', data)
  },
  /**
   *
   * @param params
   * @returns
   */
  getAllTransantions: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/transactions', { params })
  },
  /**
   *
   * @param id
   * @param data
   * @returns
   */
  updateTransaction: (id: number, data: TransactionRequestBody): Promise<AxiosResponse> => {
    return api.put(`/transactions/${id}`, data)
  },
  /**
   *
   * @param id
   * @param params
   * @returns
   */
  getItems: (id: number, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/transactions/${id}/items`, { params: { ...params } })
  },
  /**
   *
   * @param params
   * @returns
   */
  getAllPurchases: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/purchases', { params })
  },
  /**
   *
   * @param data
   * @returns
   */
  createPurchase: (data: PurchaseRequestBody): Promise<AxiosResponse> => {
    return api.post('/purchases', data)
  },
  /**
   *
   * @param id
   * @returns
   */
  deletePurchase: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/purchases/${id}`)
  },
  /**
   *
   * @param params
   * @returns
   */
  getAllPurchaseLogs: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('/purchases/logs', { params })
  },
}

export default CanteenServices
