import type { AxiosResponse } from 'axios'

import type { TermRequestBody } from '@/dto/TermRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'terms' })

/**
 * A service object for managing Terms through API calls.
 */
const TermServices = {
  getAllTerms: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  getTermById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  createTerm: (data: TermRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  updateTerm: (id: number, data: TermRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  deleteTerm: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default TermServices
