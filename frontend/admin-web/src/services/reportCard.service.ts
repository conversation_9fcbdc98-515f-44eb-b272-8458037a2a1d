import type { AxiosResponse } from 'axios'

import type { ReportCardRequestBody } from '@/dto/reportCardRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'reportCards' })
const apiFile = API_FILE({ prefix: 'reportCards' })
export type ReportStage = 'mid' | 'final'

/**
 * A service object for managing ReportCards through API calls.
 */
const ReportCardServices = {
  /**
   * Fetches all ReportCards with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to the Axios response containing the list of ReportCards.
   */
  getAllReportCards: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Fetches a specific ReportCard by its unique identifier.
   *
   * @param id - The unique identifier of the ReportCard.
   * @returns A promise that resolves to the Axios response containing the ReportCard details.
   */
  getReportCardById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new ReportCard with the provided data.
   *
   * @param data - The data for the new ReportCard.
   * @returns A promise that resolves to the Axios response containing the created ReportCard.
   */
  createReportCard: (data: any): Promise<AxiosResponse> => {
    return api.post('', data)
  },

  /**
   * Updates an existing ReportCard by its unique identifier.
   *
   * @param id - The unique identifier of the ReportCard to be updated.
   * @param data - The updated data for the ReportCard.
   * @returns A promise that resolves to the Axios response containing the updated ReportCard.
   */
  updateReportCard: (id: number, data: any): Promise<AxiosResponse> => {
    return api.put(`/${id}`, data)
  },

  /**
   * Deletes a ReportCard by its unique identifier.
   *
   * @param id - The unique identifier of the ReportCard to be deleted.
   * @returns A promise that resolves to the Axios response confirming the deletion.
   */
  deleteReportCard: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },

  upsertAssignmentGrades: (
    reportCardId: number,
    payload: { assignment_grades: number[] },
  ): Promise<AxiosResponse> => {
    return api.put(`/${reportCardId}/grades/assignments`, payload)
  },

  upsertAssignmentGradesSmart: (payload: {
    term_id: number
    student_user_id: number
    sub_classroom_subject_id: number
    assignment_grades: number[] | Record<string, number>
  }): Promise<AxiosResponse> => {
    return api.put(`/grades/assignments`, payload)
  },

  upsertRevisionScore: (
    reportCardId: number,
    payload: { revision_score: number },
  ): Promise<AxiosResponse> => {
    return api.post(`/${reportCardId}/grades/revision-score`, payload)
  },

  upsertNoteByKeys: (payload: {
    term_id: number
    sub_classroom_subject_id: number
    student_user_id: number
    note_type: 'mid' | 'final'
    note: string
  }): Promise<AxiosResponse> => {
    return api.post(`/upsert-note`, payload)
  },

  importNote: (form: FormData): Promise<AxiosResponse> => {
    return apiFile.post(`/import-notes`, form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },

  export: (
    stage: 'mid' | 'final',
    payload: { term_id: number; sub_classroom_subject_id: number },
  ) => {
    return api.post(`/export-note/${stage}`, payload)
  },

  downloadNotesTemplate: (params: {
    term_id: number
    sub_classroom_subject_id: number
    format?: 'xlsx' | 'csv'
  }): Promise<AxiosResponse<Blob>> => {
    return apiFile.get('/notes-template', {
      params,
      responseType: 'blob',
    })
  },

  reportDownload: (
    termId: number,
    studentUserId: number,
    stage: ReportStage,
    extra?: { teacherNote?: string; graduationStatus?: 'pass' | 'fail' | null },
  ): Promise<AxiosResponse<Blob>> => {
    return apiFile.get(`/${termId}/students/${studentUserId}/report-download`, {
      params: { stage, ...extra },
      responseType: 'blob',
    })
  },

  reportCoverDownload: (studentUserId: number): Promise<AxiosResponse<Blob>> => {
    return apiFile.get(`/student/${studentUserId}/report-cover`, {
      responseType: 'blob',
    })
  },

  revisionScore: (reportCardId: number, data: ReportCardRequestBody): Promise<AxiosResponse> => {
    return api.post(`/${reportCardId}/grades/revision-score`, data)
  },

  updateClassTeacherNote: (
    termId: number,
    studentUserId: number,
    body: { classTeacherNote: string },
  ): Promise<AxiosResponse> => {
    return api.patch(`/terms/${termId}/students/${studentUserId}/report-card/class-teacher-note`, {
      class_teacher_note: body.classTeacherNote,
    })
  },

  getClassTeacherNote: (termId: number, studentUserId: number): Promise<AxiosResponse> => {
    return api.get(`/terms/${termId}/students/${studentUserId}/report-card/class-teacher-note`)
  },
}

export default ReportCardServices
