import type { AxiosResponse } from 'axios'

import type { ExportParams, QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'student' })
const apiFile = API_FILE({ prefix: 'student' })

/**
 * A service object for managing student-related API calls.
 */
const StudentServices = {
  /**
   * Fetches all students with optional query parameters and classroom ID.
   *
   * @param params - Optional query parameters for filtering the students.
   * @param classroomId - Optional ID of the classroom to filter students by.
   * @returns A promise that resolves to the Axios response containing the list of students.
   */
  getAllStudents: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Fetches the count of students.
   *
   * @returns A promise that resolves to the Axios response containing the count of students.
   */
  getCount: (): Promise<AxiosResponse> => {
    return api.get('/count')
  },

  /**
   * Creates a new student with the provided data.
   *
   * @param data - The request body containing student details.
   * @returns A promise that resolves to the Axios response of the create student request.
   */
  importStudent: (file: File, schoolId: any): Promise<AxiosResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    return apiFile.post('/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-Active-School-Id': String(schoolId),
      },
    })
  },

  /**
   * Creates a new student with the provided data.
   *
   * @param data - The request body containing student details.
   * @returns A promise that resolves to the Axios response of the create student request.
   */
  downloadTemplate: (): Promise<AxiosResponse> => {
    return apiFile.get('/template')
  },

  /**
   * Creates a new student with the provided data.
   *
   * @param data - The request body containing student details.
   * @returns A promise that resolves to the Axios response of the create student request.
   */
  exportStudent: (params?: ExportParams): Promise<AxiosResponse<Blob>> => {
    return apiFile.get('/export', {
      params: { ...params },
    })
  },
}

export default StudentServices
