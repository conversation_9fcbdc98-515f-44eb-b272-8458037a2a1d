import type { AxiosResponse } from 'axios'

import type {
  SetDefaultClassRequestDTO,
  ToggleAllColumnRequestDTO,
  ToggleAllRowRequestDTO,
} from '@/dto/subjectEnrollmentRequestBody'

import { API } from './api'

const api = API({ prefix: 'classEnrollment' })

const ClassEnrollmentServices = {
  summary: (subClassroomId: number, termId: number): Promise<AxiosResponse> => {
    return api.get(`/${subClassroomId}/enrollments/summary`, {
      params: { term_id: termId },
    })
  },

  syncMatrix: (subClassroomId: number, termId: number): Promise<AxiosResponse> => {
    return api.put(`/${subClassroomId}/enrollments/sync`, {
      term_id: termId,
    })
  },

  setDefault: (subClassroomId: number, body: SetDefaultClassRequestDTO): Promise<AxiosResponse> => {
    return api.post(`/${subClassroomId}/enrollments/defaults`, body)
  },

  selectAllColumn: (
    subClassroomId: number,
    subjectId: number,
    body: ToggleAllColumnRequestDTO,
  ): Promise<AxiosResponse> => {
    return api.put(`/${subClassroomId}/subjects/${subjectId}/enroll-all`, body)
  },

  selectAllRow: (
    subClassroomId: number,
    userId: number,
    body: ToggleAllRowRequestDTO,
  ): Promise<AxiosResponse> => {
    return api.put(`/${subClassroomId}/students/${userId}/enroll-all-subjects`, body)
  },
}

export default ClassEnrollmentServices
