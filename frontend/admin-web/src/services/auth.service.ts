import type { AxiosResponse } from 'axios'

import type { ChangePasswordRequestBody } from '@/dto/changePasswordRequestBody'
import type { LoginRequestBody } from '@/dto/loginRequestBody'
import type { ResetPasswordRequestBody } from '@/dto/resetPasswordRequestBody'
import type { UserRequestBody } from '@/dto/userRequestBody'
import { addOneDay } from '@/utils/date.util'

import { API } from './api'

const api = API({ prefix: 'auth' })

/**
 * A service object for handling authentication-related API calls.
 */
/**
 * AuthServices provides methods for handling authentication-related API requests.
 *
 * Methods:
 * - `login`: Sends a login request to the server with the provided credentials.
 * - `refreshToken`: Sends a request to refresh the authentication token.
 * - `getProfile`: Retrieves the authenticated user's profile information.
 */
const AuthServices = {
  /**
   * Sends a login request to the server with the provided credentials.
   *
   * @param data - The login request body containing user credentials.
   * @returns A promise that resolves to the Axios response of the login request.
   */
  login: (data: LoginRequestBody): Promise<AxiosResponse> => {
    return api.post('/login', data)
  },

  /**
   * Sends a registration request to the server with the provided user data.
   *
   * @param data - The user request body containing registration details.
   * @param role - Optional role to be assigned during registration.
   * @returns A promise that resolves to the Axios response of the registration request.
   */
  register: (data: UserRequestBody, role?: string): Promise<AxiosResponse> => {
    return api.post('/register', {
      ...data,
      // Remove password field - backend will generate random password
      role: role,
      birthDate: data.birthDate ? addOneDay(data.birthDate).toISOString() : undefined,
      parent_user_id: data.parentUserId,
    })
  },

  /**
   * Sends a request to refresh the authentication token.
   *
   * @returns A promise that resolves to the Axios response of the refresh token request.
   */
  refreshToken: (): Promise<AxiosResponse> => {
    return api.post('/refresh')
  },

  /**
   * Retrieves the authenticated user's profile information.
   *
   * @returns A promise that resolves to the Axios response containing the user's profile information.
   */
  getProfile: (): Promise<AxiosResponse> => {
    return api.get('/profile')
  },

  /**
   * Sends a request to reset the user's password.
   *
   * @param data - The request body containing the email and new password.
   * @returns A promise that resolves to the Axios response of the reset password request.
   */
  resetPassword: (data: ResetPasswordRequestBody): Promise<AxiosResponse> => {
    return api.post('/resetPassword', data)
  },

  changePassword: (data: ChangePasswordRequestBody) => {
    const payload = {
      current_password: data.currentPassword,
      new_password: data.newPassword,
      new_password_confirmation: data.confirmNewPassword,
    }
    return api.post('/changePassword', payload) // endpoint kamu
  },

  /**
   * Creates a new user with the provided data.
   * @param {UserRequestBody} data - The request body containing user details.
   * @returns {Promise<AxiosResponse>} - The Axios response of the create user request.
   */
  update: (data: UserRequestBody): Promise<AxiosResponse> => {
    return api.put(`/update`, {
      ...data,
      birthDate: data.birthDate ? addOneDay(data.birthDate).toISOString() : undefined,
    })
  },

  /**
   * Updates a user by their ID with the provided data.
   * @param {number} id - The ID of the user to update.
   * @param {UserRequestBody} data - The request body containing updated user details.
   * @returns {Promise<AxiosResponse>} - The Axios response of the update user request.
   */
  updateById: (id: number, data: UserRequestBody): Promise<AxiosResponse> => {
    return api.put(`/update/${id}`, {
      ...data,
      birthDate: data.birthDate ? addOneDay(data.birthDate).toISOString() : undefined,
    })
  },
}

export default AuthServices
