import type { AxiosResponse } from 'axios'

import type { BulkUpsertRequestDTO, ToggleCellRequestDTO } from '@/dto/subjectEnrollmentRequestBody'

import { API } from './api'

const api = API({ prefix: 'subjectEnrollment' })

const SubjectEnrollmentServices = {
  getMatrix: (termId: number, subClassroomId: number): Promise<AxiosResponse> => {
    return api.get('/enrollments/matrix', {
      params: { term_id: termId, sub_classroom_id: subClassroomId },
    })
  },

  toggleCell: (body: ToggleCellRequestDTO): Promise<AxiosResponse> => {
    return api.patch('/enrollments', body)
  },

  bulkUpsert: (body: BulkUpsertRequestDTO): Promise<AxiosResponse> => {
    return api.put('/enrollments/bulk', body)
  },
}

export default SubjectEnrollmentServices
