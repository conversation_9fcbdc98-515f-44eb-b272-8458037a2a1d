import type { AxiosResponse } from 'axios'

import type { FoundationRequestBody } from '@/dto/foundationRequestBody'
import type { QueryParams } from '@/types/queryParams.type'

import { API } from './api'

const api = API({ prefix: 'foundations' })

/**
 * A service object for managing Foundations.
 */
const FoundationServices = {
  /**
   * Retrieves all Foundations with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to an AxiosResponse containing the Foundations.
   */
  getAllFoundations: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Retrieves a Foundation by its ID.
   *
   * @param id - The ID of the Foundation to retrieve.
   * @returns A promise that resolves to an AxiosResponse containing the Foundation details.
   */
  getFoundationById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Retrieves all admins of a Foundation by its ID.
   *
   * @param id - The ID of the Foundation to retrieve admins for.
   * @returns A promise that resolves to an AxiosResponse containing the list of admins.
   */
  getFoundationAdmins: (id: number, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/${id}/admins`, { params: { ...params } })
  },

  /**
   * Retrieves all schools of a Foundation by its ID.
   *
   * @param id - The ID of the Foundation to retrieve schools for.
   * @returns A promise that resolves to an AxiosResponse containing the list of schools.
   */
  getFoundationSchools: (id: number, params?: QueryParams): Promise<AxiosResponse> => {
    return api.get(`/${id}/schools`, { params: { ...params } })
  },

  /**
   * Assigns an admin to a Foundation by their ID.
   *
   * @param id - The ID of the Foundation to assign the admin to.
   * @param userId - The ID of the user to be assigned as an admin.
   * @returns A promise that resolves to an AxiosResponse confirming the assignment.
   */
  assignAdmin: (id: number, userId: number): Promise<AxiosResponse> => {
    return api.post(`/${id}/assignAdmin`, { userId })
  },

  /**
   * Adds a school to a Foundation by its ID.
   *
   * @param id - The ID of the Foundation to add the school to.
   * @param schoolId - The ID of the school to be added.
   * @returns A promise that resolves to an AxiosResponse confirming the addition.
   */
  addSchool: (id: number, schoolId: number): Promise<AxiosResponse> => {
    return api.post(`/${id}/addSchool`, { schoolId })
  },

  /**
   * Creates a new Foundation.
   *
   * @param data - The request body containing the details of the Foundation to create.
   * @returns A promise that resolves to an AxiosResponse containing the created Foundation.
   */
  createFoundation: (data: FoundationRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  /**
   * Updates an existing Foundation by its ID.
   *
   * @param id - The ID of the Foundation to update.
   * @param data - The request body containing the updated details of the Foundation.
   * @returns A promise that resolves to an AxiosResponse containing the updated Foundation.
   */
  updateFoundation: (id: number, data: FoundationRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  /**
   * Deletes a Foundation by its ID.
   *
   * @param id - The ID of the Foundation to delete.
   * @returns A promise that resolves to an AxiosResponse confirming the deletion.
   */
  deleteFoundation: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default FoundationServices
