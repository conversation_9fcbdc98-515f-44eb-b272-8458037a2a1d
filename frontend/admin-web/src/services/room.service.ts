import type { AxiosResponse } from 'axios'

import type { RoomRequestBody } from '@/dto/roomRequestBody'

import { API } from './api'

const api = API({ prefix: 'rooms' })

/**
 * A service object for managing rooms.
 */
const RoomServices = {
  /**
   * Retrieves all rooms with optional pagination.
   *
   * @param page - The page number for paginated results (optional).
   * @returns A promise that resolves to an AxiosResponse containing the rooms.
   */
  getAllRooms: (page?: number): Promise<AxiosResponse> => {
    return api.get('', { params: { page } })
  },

  /**
   * Retrieves a room by its ID.
   *
   * @param id - The ID of the room to retrieve.
   * @returns A promise that resolves to an AxiosResponse containing the room details.
   */
  getRoomById: (id: number): Promise<AxiosResponse> => {
    return api.get(`/${id}`)
  },

  /**
   * Creates a new room.
   *
   * @param data - The request body containing the details of the room to create.
   * @returns A promise that resolves to an AxiosResponse containing the created room.
   */
  createRoom: (data: RoomRequestBody): Promise<AxiosResponse> => {
    return api.post('', { ...data })
  },

  /**
   * Updates an existing room by its ID.
   *
   * @param id - The ID of the room to update.
   * @param data - The request body containing the updated details of the room.
   * @returns A promise that resolves to an AxiosResponse containing the updated room.
   */
  updateRoom: (id: number, data: RoomRequestBody): Promise<AxiosResponse> => {
    return api.put(`/${id}`, { ...data })
  },

  /**
   * Deletes a room by its ID.
   *
   * @param id - The ID of the room to delete.
   * @returns A promise that resolves to an AxiosResponse confirming the deletion.
   */
  deleteRoom: (id: number): Promise<AxiosResponse> => {
    return api.delete(`/${id}`)
  },
}

export default RoomServices
