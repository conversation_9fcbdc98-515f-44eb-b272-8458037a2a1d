import type { AxiosResponse } from 'axios'

import type { QueryParams } from '@/types/queryParams.type'

import { API, API_FILE } from './api'

const api = API({ prefix: 'teacher' })
const apiFile = API_FILE({ prefix: 'teacher' })

/**
 * A service object for managing teacher-related API calls.
 */
const TeacherServices = {
  /**
   * Fetches a list of all teachers with optional query parameters.
   *
   * @param params - Optional query parameters for filtering or pagination.
   * @returns A promise that resolves to the Axios response containing the list of teachers.
   */
  getAllTeachers: (params?: QueryParams): Promise<AxiosResponse> => {
    return api.get('', { params: { ...params } })
  },

  /**
   * Fetches the count of teachers.
   *
   * @returns A promise that resolves to the Axios response containing the count of teachers.
   */
  getCount: (): Promise<AxiosResponse> => {
    return api.get('/count')
  },

  /**
   * Creates a new teacher with the provided data.
   *
   * @param data - The request body containing teacher details.
   * @returns A promise that resolves to the Axios response of the create teacher request.
   */
  importTeacher: (file: File, schoolId: any): Promise<AxiosResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    return apiFile.post('/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-Active-School-Id': String(schoolId),
      },
    })
  },

  /**
   * Downloads a template for importing teachers.
   *
   * @returns A promise that resolves to the Axios response containing the template file.
   */
  downloadTemplate: (): Promise<AxiosResponse> => {
    return apiFile.get('/template')
  },

  /**
   * Exports teacher data based on optional query parameters.
   *
   * @param params - Optional query parameters for filtering the exported data.
   * @returns A promise that resolves to the Axios response containing the exported file.
   */
  exportTeacher: (params?: QueryParams): Promise<AxiosResponse> => {
    return apiFile.get('/export', {
      responseType: 'blob',
      params: {
        ...params,
      },
    })
  },
}

export default TeacherServices
