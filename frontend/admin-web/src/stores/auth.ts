import { defineStore } from 'pinia'

import { RoleType } from '@/enums/roleType.enum.ts'
import type { School } from '@/types/school.type'
import type { Role, User } from '@/types/user.type'
/**
 * Auth store for managing user authentication state.
 *
 * This store handles user and token management, including
 * loading from and saving to localStorage. It also provides
 * utility methods for checking authentication status and
 * retrieving user details.
 */
export const useAuthStore = defineStore('auth', {
  state: () => ({
    /**
     * The currently authenticated user, loaded from localStorage.
     * If no user is found, defaults to `null`.
     */
    user: JSON.parse(localStorage.getItem('user') || 'null') as User | null,

    /**
     * The authentication token, loaded from localStorage.
     * If no token is found, defaults to `null`.
     */
    token: localStorage.getItem('token') || null,

    /**
     * The active school, loaded from localStorage.
     * If no school is found, defaults to `null`.
     */
    activeSchool: JSON.parse(localStorage.getItem('activeSchool') || 'null') as School | null,
    activeRole: JSON.parse(localStorage.getItem('activeRole') || 'null') as RoleType | null,
  }),

  getters: {
    /**
     * Determines if the user is authenticated based on the presence of a token.
     *
     * @param state - The current state of the store.
     * @returns `true` if a token exists, otherwise `false`.
     */
    isAuthenticated: (state) => !!state.token,

    /**
     * Retrieves the details of the currently authenticated user.
     *
     * @param state - The current state of the store.
     * @returns The user object or `null` if no user is authenticated.
     */
    getUser: (state) => state.user as User,

    /**
     * Retrieves the user's role if authenticated.
     */
    roles: (state) => state.user?.roles?.map((r: Role) => r.name) || [],

    /**
     * Retrieves the active school.
     */
    getActiveSchool: (state) => state.activeSchool as School | null,
    getActiveRole: (state) => state.activeRole as RoleType | null,
  },

  actions: {
    /**
     * Logs in the user by setting the user and token in the store
     * and saving them to localStorage.
     *
     * @param user - The user object to set in the store.
     * @param token - The authentication token to set in the store.
     */
    login(user: User, token: string) {
      this.user = user
      this.token = token

      if (user.schools.length > 0) {
        this.setActiveSchool(user.schools[0])
      }

      if (user.roles.length > 0) {
        this.setActiveRole(user.roles[0].name as RoleType)
      }

      // Save to localStorage
      localStorage.setItem('user', JSON.stringify(user))
      localStorage.setItem('token', token)
    },

    /**
     * Logs out the user by clearing the user and token from the store
     * and removing them from localStorage.
     */
    logout() {
      this.user = null
      this.token = null
      this.activeSchool = null
      this.activeRole = null
      // Remove from localStorage
      localStorage.removeItem('auth')
      localStorage.removeItem('activeRole')
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      localStorage.removeItem('schoolIdentityDraft')
      localStorage.removeItem('activeSchool')
    },

    /**
     * Updates the user state in the store and saves it to localStorage.
     *
     * @param user - The user object to set in the store.
     */
    refreshToken(token: string) {
      localStorage.setItem('token', token)
    },

    /**
     * Loads the authentication token from localStorage and updates
     * the store state. This method is typically called when the app
     * initializes to restore the user's session.
     */
    loadAuthFromStorage() {
      const storedToken = localStorage.getItem('token')

      if (storedToken) {
        this.token = storedToken
      }
    },

    /**
     * Sets the active school in the store and saves it to localStorage.
     *
     * @param school - The school object to set as the active school.
     */
    setActiveSchool(school: School) {
      this.activeSchool = school
      localStorage.setItem('activeSchool', JSON.stringify(school))
    },

    setActiveRole(role: RoleType) {
      this.activeRole = role
      localStorage.setItem('activeRole', JSON.stringify(role))
    },

    /**
     * Sets the user in the store and saves it to localStorage.
     *
     * @param user - The user object to set in the store.
     */
    setUser(user: User) {
      this.user = user
      localStorage.setItem('user', JSON.stringify(user))
    },
  },

  /**
   * Enables automatic persistence of the store state.
   */
  persist: true,
})
