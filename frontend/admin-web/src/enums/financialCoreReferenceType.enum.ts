export enum FinancialCoreReferenceType {
  TUITION = 'tuition',
  CANTEEN = 'canteen',
  CASHLESS = 'cashless',
  OTHERS = 'others',
}

export const getFinancialCoreReferenceType = (type: string) => {
  switch (type) {
    case FinancialCoreReferenceType.TUITION:
      return 'SPP'
    case FinancialCoreReferenceType.CANTEEN:
      return 'Kantin'
    case FinancialCoreReferenceType.CASHLESS:
      return 'Transaksi NFC'
    case FinancialCoreReferenceType.OTHERS:
      return 'Lainnya'
    default:
      return 'Unknown'
  }
}

export enum FinancialCoreStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export const getFinancialCoreStatus = (status: string) => {
  switch (status) {
    case FinancialCoreStatus.PENDING:
      return 'Pending'
    case FinancialCoreStatus.COMPLETED:
      return 'Selesai'
    case FinancialCoreStatus.FAILED:
      return 'Gagal'
    default:
      return 'Unknown'
  }
}