export enum GradeType {
  ASSIGNMENT = 'assignment',
  MID_EXAM = 'mid_exam',
  FINAL_EXAM = 'final_exam',
}

export const getGradeType = (method: string) => {
  switch (method) {
    case GradeType.ASSIGNMENT:
      return 'Tugas'
    case GradeType.MID_EXAM:
      return '<PERSON><PERSON><PERSON>'
    case GradeType.FINAL_EXAM:
      return '<PERSON><PERSON><PERSON>'
    default:
      return 'Unknown'
  }
}
