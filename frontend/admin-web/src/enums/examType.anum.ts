export enum ExamType {
  SUMATIF_TENGAH_SEMESTER = 'sts',
  SUMATIF_AKHIR_SEMESTER = 'sas',
  TEST_KEMAMPUAN_AKADEMIK = 'tka',
}

export const ExamTypeOptions = [
  { label: '<PERSON><PERSON><PERSON> (STS)', value: ExamType.SUMATIF_TENGAH_SEMESTER },
  { label: '<PERSON><PERSON><PERSON> (SAS)', value: ExamType.SUMATIF_AKHIR_SEMESTER },
  { label: 'Test Kemampuan Akademik (TKA)', value: ExamType.TEST_KEMAMPUAN_AKADEMIK },
]

export const getExamTypeLabel = (type: string): string => {
  const found = ExamTypeOptions.find((t) => t.value === type)
  return found?.label ?? ''
}
