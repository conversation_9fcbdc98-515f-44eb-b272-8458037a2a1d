import { useToast } from 'primevue/usetoast'

import {
  translateErrorList,
  flattenTranslatedErrors,
  translateSingleMessage,
} from '@/utils/errorTranslation'
import type { ErrorList, TranslatedErrorList } from '@/utils/errorTranslation'
import { showToast } from '@/utils/toast.util'

export const useFormRequestHandler = (
  onErrorTranslated?: (translatedErrors?: TranslatedErrorList | null) => void,
) => {
  const toast = useToast()

  const handleRequest = async (
    callback: () => Promise<void>,
    successMsg?: string,
    fallbackErrorMsg?: string,
  ) => {
    try {
      if (onErrorTranslated) onErrorTranslated(null)

      await callback()
      if (successMsg) {
        showToast(toast, 'success', successMsg)
      }
    } catch (error: any) {
      console.error('❌ Request Error:', error)

      const rawErrors: ErrorList | undefined = error?.response?.data?.errors
      const isLaravelValidation =
        rawErrors && typeof rawErrors === 'object' && !Array.isArray(rawErrors)

      if (isLaravelValidation) {
        const translated = translateErrorList(rawErrors)
        const messages = flattenTranslatedErrors(translated)

        // update form errors
        if (onErrorTranslated) {
          onErrorTranslated(translated)
        }

        if (messages.length > 0) {
          messages.forEach((msg?: string) => msg && showToast(toast, 'error', msg))
          return
        }
      }

      // fallback error
      const rawFallback =
        typeof error?.response?.data?.errors === 'string'
          ? error.response.data.errors
          : fallbackErrorMsg

      const translatedFallback = translateSingleMessage('', rawFallback)

      showToast(toast, 'error', translatedFallback)
    }
  }

  return {
    handleRequest,
  }
}
