import router from '@/router'
import { useAuthStore } from '@/stores/auth'

/**
 * <PERSON>k apakah user memiliki role admin atau superadmin.
 * Gunakan ini di dalam setup() agar selalu reactive jika perlu.
 */
export const isAdminOrSuperUser = () => {
  const auth = useAuthStore()
  const roles = (auth.roles || []).map((r: any) => String(r).toLowerCase())
  return (
    roles.includes('foundation_admin') ||
    roles.includes('superadmin') ||
    roles.includes('school_admin')
  )
}

export const goToCreateExam = () => {
  router.push({ name: isAdminOrSuperUser() ? 'admin.exams.create' : 'teacher.exams.create' })
}

export const goToEditExam = (id?: number) => {
  router.push({
    name: isAdminOrSuperUser() ? 'admin.exams.edit' : 'teacher.exams.edit',
    params: { id },
  })
}

export const goToExamResult = (id: number) => {
  router.push({
    name: isAdminOrSuperUser() ? 'admin.exams.results' : 'teacher.exams.results',
    params: { id },
  })
}

export const goToDashboard = () => {
  router.push({ name: isAdminOrSuperUser() ? 'admin.exams' : 'teacher.dashboard' })
}

export const goToExamList = () => {
  router.push({ name: isAdminOrSuperUser() ? 'admin.exams' : 'teacher.exams' })
}

export const backToExamList = () => {
  const routeName = isAdminOrSuperUser() ? 'admin.exams' : 'teacher.exams'
  router.push({ name: routeName })
}

/**
 * Navigasi ke halaman edit ujian
 * @param examId - ID ujian
 */
export const goToExamEdit = (examId: number) => {
  const routeName = isAdminOrSuperUser() ? 'admin.exams.edit' : 'teacher.exams.edit'
  router.push({
    name: routeName,
    params: { id: examId },
  })
}
