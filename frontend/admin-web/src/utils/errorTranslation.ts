export type ErrorList = Record<string, string[]>
export type TranslatedErrorList = Record<string, string[]>

const errorTranslations: Record<string, string> = {
  // === Exam Validations ===
  start_datetime: 'Waktu mulai ujian tidak boleh di masa lalu.',
  end_datetime: 'Waktu selesai ujian harus setelah waktu mulai.',
  'passing_score.regex': '<PERSON><PERSON> kelulusan maksimal memiliki 2 angka di belakang koma.',
  'passing_score.max': '<PERSON><PERSON> kelulusan tidak boleh lebih dari 100.',
  'passing_score.required': '<PERSON><PERSON> kelulusan wajib diisi.',
  'passing_score.numeric': '<PERSON><PERSON> kelulusan harus berupa angka.',
  'passing_score.min': '<PERSON><PERSON> kelulusan minimal 0.',
  'sub_classroom_subject_id.exists': 'Mata pelajaran tidak ditemukan atau sudah dihapus.',
  'sub_classroom_subject_id.required': 'Mata pelajaran wajib dipilih.',
  'The selected subject does not exist or has been removed.': 'Mata pelajaran wajib dipilih.',
  'Exam time range is not within the term.': '<PERSON><PERSON>h tahun ajaran saat ini',

  // === Common validations ===
  'title.required': 'Judul ujian wajib diisi.',
  'title.string': 'Judul ujian harus berupa teks.',
  'title.max': 'Judul ujian maksimal 255 karakter.',
  'description.string': 'Deskripsi harus berupa teks.',
  'duration.required': 'Durasi ujian wajib diisi.',
  'duration.integer': 'Durasi ujian harus berupa angka bulat.',
  'duration.min': 'Durasi ujian minimal 1 menit.',

  // === Exam Question Validations ===
  'content.required': 'Konten soal wajib diisi.',
  'content.string': 'Konten soal harus berupa teks.',
  'answer_options.required': 'Opsi jawaban wajib diisi.',
  'answer_options.array': 'Format opsi jawaban harus berupa array.',
  'answer_options.min': 'Setiap soal ujian harus memiliki minimal 2 opsi jawaban.',
  'answer_options.*.content.required': 'Setiap opsi jawaban harus memiliki konten.',
  'answer_options.*.content.string': 'Konten opsi jawaban harus berupa teks.',
  'answer_options.*.is_correct.required':
    'Setiap opsi jawaban harus menyatakan apakah benar atau salah.',
  'answer_options.*.is_correct.boolean': 'Status kebenaran jawaban harus true atau false.',

  // === Bulk Question Validations ===
  'exam_id.required': 'ID ujian wajib diisi.',
  'exam_id.exists': 'ID ujian tidak valid.',
  'exam_id.integer': 'ID ujian harus berupa angka.',
  'questions.required': 'Minimal harus ada satu soal ujian.',
  'questions.min': 'Minimal harus ada satu soal ujian.',
  'questions.*.content.required': 'Konten soal wajib diisi.',
  'questions.*.content.string': 'Konten soal harus berupa teks.',
  'questions.*.answer_options': 'Setiap soal harus memiliki minimal 2 opsi jawaban.',
  'questions.*.answer_options.*.content.required': 'Konten opsi jawaban wajib diisi.',
  'questions.*.answer_options.*.content.string': 'Konten opsi jawaban harus berupa teks.',
  'questions.*.answer_options.*.is_correct.required': 'Status kebenaran jawaban harus ditentukan.',
  'questions.*.answer_options.*.is_correct.boolean':
    'Status kebenaran jawaban harus true atau false.',
  'questions.*.points': 'Poin untuk setiap soal wajib diisi.',
  'questions.*.answer_options.*.content': 'Konten tiap opsi jawaban wajib diisi.',
  'Each answer option must have content.': 'Setiap opsi jawaban harus memiliki konten.',
  'This exam is already published.': 'Ujian ini sudah dipublikasikan dan tidak dapat diubah.',
  'questions.*.competency_indicator': 'Indikator pencapaian kompetensi wajib diisi',

  // === Laravel default messages mapping ===
  'The question content field is required.': 'Konten soal wajib diisi.',
  'The title field is required.': 'Judul ujian wajib diisi.',
  'The passing score field is required.': 'Nilai kelulusan wajib diisi.',

  // === Exam Error ===
  'The exam token field is required.': 'Wajib memasukan token ujian',
  'Your exam token has expired.': 'Token Ujian Anda Sudah Kadaluwarsa',
  'Invalid exam token.': 'Token Ujian tidak valid',
  'Cannot unpublish an exam that has already started.':
    'Tidak dapat unpublish Ujian yang telah mulai',

  // === Identitas Sekolah ===
  'The address field must be a string.': 'Alamat harus berupa teks.',
  'The registration number field must be a string.': 'Nomor registrasi harus berupa teks.',
  'The year founded field must be a string.': 'Tahun berdiri harus berupa teks.',
  'The phone number field must be a string.': 'Nomor telepon harus berupa teks.',
  'The email field must be a valid email address.': 'Email harus berupa alamat email yang valid.',
  'The website field must be a string.': 'Website harus berupa teks.',
  'The logo field must be an image.': 'Logo harus berupa gambar.',
  'The logo field must be a file of type: jpg, jpeg, png.':
    'Logo harus berupa file dengan tipe: jpg, jpeg, png.',
  'The selected foundation id is invalid.': 'ID yayasan yang dipilih tidak valid.',

  // === Practical Data ===
  learning_objective: 'Tujuan pembelajaran wajib diisi',
  description: 'Deskripsi wajin diisi',
  final_score: 'Nilai akhir wajib diisi',

  // === Change Password ===
  'Current password is incorrect': 'Password Anda Salah',
  new_password: 'Password konfirmasi tidak sama dengan password baru',
}

const attributeTranslations: Record<string, string> = {
  title: 'judul ujian',
  description: 'deskripsi',
  duration: 'durasi ujian',
  content: 'konten soal',
  questions: 'daftar soal',
  'questions.*.content': 'konten soal',
  'questions.*.answer_options': 'opsi jawaban',
  'questions.*.answer_options.*.content': 'konten opsi jawaban',
  'questions.*.answer_options.*.is_correct': 'indikator kebenaran jawaban',
  'questions.*.answer_options.*.order_index': 'urutan opsi jawaban',
  'questions.*.points': 'poin soal',
  answer_options: 'opsi jawaban',
  'answer_options.*.content': 'konten opsi jawaban',
  'answer_options.*.is_correct': 'indikator kebenaran jawaban',
  'answer_options.*.order_index': 'urutan opsi jawaban',
  exam_id: 'ID ujian',
  start_datetime: 'waktu mulai',
  end_datetime: 'waktu selesai',
  passing_score: 'nilai kelulusan',
  sub_classroom_subject_id: 'mata pelajaran',
  exam_token: 'token ujian',

  // === Identitas Sekolah ===
  address: 'alamat',
  registrationNumber: 'nomor registrasi',
  yearFounded: 'tahun berdiri',
  phoneNumber: 'nomor telepon',
  email: 'email',
  website: 'website',
  logo: 'logo',

  // === Practical Data ===
  learning_objective: 'Tujuan Pembelajaran',
}

const findWildcardMatch = (
  key: string,
  translations: Record<string, string>,
): string | undefined => {
  return Object.keys(translations).find((pattern) => {
    if (!pattern.includes('*')) return false
    const regex = new RegExp('^' + pattern.replace(/\*/g, '\\d+') + '$')
    return regex.test(key)
  })
}

export const translateSingleMessage = (key: string, originalMsg: string): string => {
  if (errorTranslations[originalMsg]) return errorTranslations[originalMsg]

  const prefixMatch = Object.entries(errorTranslations).find(([prefix]) =>
    originalMsg.startsWith(prefix),
  )
  if (prefixMatch) return prefixMatch[1]

  if (errorTranslations[key]) return errorTranslations[key]

  const wildcardKey = findWildcardMatch(key, errorTranslations)
  if (wildcardKey) return errorTranslations[wildcardKey]

  const attrLabel =
    attributeTranslations[key] ||
    attributeTranslations[findWildcardMatch(key, attributeTranslations) || '']
  if (attrLabel) return `${attrLabel}: ${originalMsg}`

  return originalMsg
}

export const translateErrorList = (errors: ErrorList): TranslatedErrorList => {
  const translated: TranslatedErrorList = {}
  Object.entries(errors).forEach(([key, messages]) => {
    translated[key] = messages.map((msg) => translateSingleMessage(key, msg))
  })
  return translated
}

export const flattenTranslatedErrors = (translatedErrors: TranslatedErrorList): string[] => {
  return Object.values(translatedErrors).flat()
}
