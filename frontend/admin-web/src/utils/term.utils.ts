import TermServices from '@/services/term.service'
import type { Term } from '@/types/term.type'

import { formatDateOnly } from './date.util'

export const getTerms = async () => {
  const { data } = await TermServices.getAllTerms()
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const terms: Term[] = data.data.map((t: any) => ({
    id: t.id,
    name: t.name,
    startDate: new Date(t.startDate),
    endDate: new Date(t.endDate),
    label: `${t.name} (${formatDateOnly(t.startDate)} s/d ${formatDateOnly(t.endDate)})`,
  }))

  const termOptions = terms.map((t: any) => ({
    label: t.label,
    id: t.id,
    value: t.id,
  }))

  const currentTerm = terms.find((t: any) => {
    const start = new Date(t.startDate)
    const end = new Date(t.endDate)
    start.setHours(0, 0, 0, 0)
    end.setHours(0, 0, 0, 0)
    return today >= start && today <= end
  })

  return {
    terms,
    termOptions,
    currentTerm,
  }
}
