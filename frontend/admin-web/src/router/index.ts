import { createRouter, createWebHistory } from 'vue-router'

import Admin from '@/layouts/Admin.vue'
import Main from '@/layouts/Main.vue'
import Student from '@/layouts/Student.vue'
import { useAuthStore } from '@/stores/auth'
import ChangePasswordPage from '@/views/auth/changePassword/ChangePasswordPage.vue'
import ResetPasswordPage from '@/views/auth/forgotPassword/ResetPasswordPage.vue'
import LoginPage from '@/views/auth/login/LoginPage.vue'
import DasboardCanteen from '@/views/canteen/adminCanteen/dashboard/DasboardCanteen.vue'
import Product from '@/views/canteen/adminCanteen/product/Product.vue'
import Purchase from '@/views/canteen/adminCanteen/purchase/Purchase.vue'
import PurchaseLog from '@/views/canteen/adminCanteen/purchase/PurchaseLog.vue'
import DasboardCashier from '@/views/canteen/cashier/dashboard/DasboardCashier.vue'
import CashierProduct from '@/views/canteen/cashier/product/Product.vue'
import Transaction from '@/views/canteen/transaction/Transaction.vue'
import TransactionPending from '@/views/canteen/transaction/TransactionPending.vue'
import ExamDashboard from '@/views/lms/dashboard/student/ExamDashboard.vue'
import DashboardTeacher from '@/views/lms/dashboard/teacher/DashboardTeacher.vue'
import Exam from '@/views/lms/exam/Exam.vue'
import ExamFormWrapper from '@/views/lms/exam/ExamFormWrapper.vue'
import ExamResult from '@/views/lms/exam/examResult/ExamResult.vue'
import ExamResultDetail from '@/views/lms/exam/examResult/examResultDetail/ExamResultDetail.vue'
import ExamRoom from '@/views/lms/exam/examRoom/ExamRoom.vue'
import AboutPage from '@/views/main/AboutPage.vue'
import HomePage from '@/views/main/HomePage.vue'
import PageNotFound from '@/views/PageNotFound.vue'
import PageUnauthorized from '@/views/PageUnauthorized.vue'
import ExtracurricularAttendance from '@/views/schoolAdmin/attendance/extracurricular/ExtracurricularAttendance.vue'
import StudentAttendance from '@/views/schoolAdmin/attendance/student/StudentAttendance.vue'
import StudentAttendanceTeacher from '@/views/schoolAdmin/attendance/student/StudentAttendanceTeacher.vue'
import TeacherAttendance from '@/views/schoolAdmin/attendance/teacher/TeacherAttendance.vue'
import CanteenAdmin from '@/views/schoolAdmin/canteen/admin/Admin.vue'
import Canteen from '@/views/schoolAdmin/canteen/Canteen.vue'
import Cashier from '@/views/schoolAdmin/canteen/cashier/Cashier.vue'
import Category from '@/views/schoolAdmin/category/Category.vue'
import ClassAttendance from '@/views/schoolAdmin/classAttendance/ClassAttendance.vue'
import ClassroomData from '@/views/schoolAdmin/classroom/classroomData/ClassroomData.vue'
import Dashboard from '@/views/schoolAdmin/dashboard/Dashboard.vue'
import Dormitory from '@/views/schoolAdmin/dormitory/Dormitory.vue'
import Event from '@/views/schoolAdmin/event/Event.vue'
import ExtracurricularEnroll from '@/views/schoolAdmin/extracurricularEnroll/ExtracurricularEnroll.vue'
import Facilities from '@/views/schoolAdmin/facilities/Facilities.vue'
import InformationData from '@/views/schoolAdmin/information/InformationData.vue'
import AcademicYear from '@/views/schoolAdmin/management/academicYear/AcademicYear.vue'
import Extracurricular from '@/views/schoolAdmin/management/extracurricular/Extracurricular.vue'
import Organization from '@/views/schoolAdmin/management/organization/Organization.vue'
import PositionData from '@/views/schoolAdmin/management/position/PositionData.vue'
import SchoolIdentity from '@/views/schoolAdmin/management/schoolIdentity/SchoolIdentity.vue'
import StudentDailyActivity from '@/views/schoolAdmin/management/studentDailyActivity/StudentDailyActivity.vue'
import PracticalTrainingPage from '@/views/schoolAdmin/practicalTraining/PracticalTraining.vue'
import FinalAssessment from '@/views/schoolAdmin/reportCard/finalAssessment/FinalAssessment.vue'
import ReportDescription from '@/views/schoolAdmin/reportCard/reportDescription/ReportDescription.vue'
import NfcTransactionData from '@/views/schoolAdmin/student/nfcTransaction/NfcTransactionData.vue'
import ParentData from '@/views/schoolAdmin/student/parentData/ParentData.vue'
import StudentData from '@/views/schoolAdmin/student/studentData/StudentData.vue'
import Schedule from '@/views/schoolAdmin/subject/schedule/Schedule.vue'
import SubjectData from '@/views/schoolAdmin/subject/subjectData/SubjectData.vue'
import SubjectEnrollmentMatrixView from '@/views/schoolAdmin/subjectEnrollment/SubjectEnrollmentMatrixView.vue'
import Tags from '@/views/schoolAdmin/tags/Tags.vue'
import HomeroomTeacher from '@/views/schoolAdmin/teacher/homeroomTeacher/HomeroomTeacher.vue'
import TeacherData from '@/views/schoolAdmin/teacher/teacherData/TeacherData.vue'
import TuitionFeeData from '@/views/schoolAdmin/tuition/tuitionFeeData/TuitionFeeData.vue'
import TuitionPaymentData from '@/views/schoolAdmin/tuition/tuitionPaymentData/TuitionPaymentData.vue'
import FinancialCore from '@/views/schoolAdmin/finance/financialCore/FinancialCore.vue'
import SuperadminDashboard from '@/views/superadmin/dashboard/SuperadminDashboard.vue'
import FoundationData from '@/views/superadmin/foundationData/FoundationData.vue'
import SchoolData from '@/views/superadmin/schoolData/SchoolData.vue'
import UserData from '@/views/superadmin/usersData/UserData.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginPage,
      meta: { title: 'Login' },
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: ResetPasswordPage,
      meta: { title: 'Reset Password' },
    },
    {
      path: '/account/changePassword',
      name: 'account.changePassword',
      component: ChangePasswordPage,
      meta: { requiresAuth: true, title: 'Ubah Sandi' },
    },
    {
      path: '/',
      component: Main,
      children: [
        {
          path: '',
          name: 'home',
          meta: { title: 'Home' },
          component: HomePage,
        },
        {
          path: 'about',
          name: 'about',
          meta: { title: 'About' },
          component: AboutPage,
        },
      ],
    },
    // Superadmin
    {
      path: '/superadmin',
      name: 'superadmin',
      component: Admin,
      children: [
        {
          path: '',
          name: 'superadmin.dashboard',
          meta: { requiresAuth: true, title: 'Dashboard' },
          component: SuperadminDashboard,
        },
        {
          path: 'foundations',
          name: 'superadmin.foundation',
          meta: { requiresAuth: true, title: 'Yayasan' },
          component: FoundationData,
        },
        {
          path: 'schools',
          name: 'superadmin.schools',
          meta: { requiresAuth: true, title: 'Sekolah' },
          component: SchoolData,
        },
        {
          path: 'users',
          name: 'superadmin.users',
          meta: { requiresAuth: true, title: 'Pengguna' },
          component: UserData,
        },
      ],
    },
    // School admin
    {
      path: '/admin',
      name: 'admin',
      meta: { requiresAuth: true, title: 'Dashboard', role: 'school_admin' },
      component: Admin,
      children: [
        {
          path: '',
          name: 'admin.dashboard',
          meta: { role: 'school_admin' },
          component: Dashboard,
        },

        // Manajemen Seklah
        {
          path: 'schoolIdentity',
          name: 'admin.schoolIdentity',
          meta: { title: 'Identitas Sekolah', role: 'school_admin' },
          component: SchoolIdentity,
        },
        {
          path: 'academicYears',
          name: 'admin.academicYears',
          meta: { title: 'Tahun Akademik', role: 'school_admin' },
          component: AcademicYear,
        },
        {
          path: 'organization',
          name: 'admin.organization',
          meta: { title: 'Organisasi Sekolah', role: 'school_admin' },
          component: Organization,
        },
        {
          path: 'positions',
          name: 'admin.positions',
          meta: { title: 'Data Jabatan', role: 'school_admin' },
          component: PositionData,
        },
        {
          path: 'extracurriculars',
          name: 'admin.extracurriculars',
          meta: { title: 'Ekstrakurikuler', role: 'school_admin' },
          component: Extracurricular,
        },
        {
          path: 'studentDailyActivities',
          name: 'admin.studentDailyActivities',
          meta: { title: 'Kegiatan Harian Siswa', role: 'school_admin' },
          component: StudentDailyActivity,
        },

        // Artikel
        {
          path: 'categories',
          name: 'admin.categories',
          meta: { title: 'Kategori', role: 'school_admin' },
          component: Category,
        },
        {
          path: 'tags',
          name: 'admin.tags',
          meta: { title: 'Tags', role: 'school_admin' },
          component: Tags,
        },

        // Data Guru
        {
          path: 'teachers',
          name: 'admin.teachers',
          meta: { title: 'Data Guru', role: 'school_admin' },
          component: TeacherData,
        },
        {
          path: 'homerooms',
          name: 'admin.homerooms',
          meta: { title: 'Wali Kelas', role: 'school_admin' },
          component: HomeroomTeacher,
        },

        // Data SPP
        {
          path: 'tuition',
          children: [
            {
              path: 'fees',
              name: 'admin.tuition.fees',
              meta: { title: 'Data SPP', role: 'school_admin' },
              component: TuitionFeeData,
            },
            {
              path: 'payments',
              name: 'admin.tuition.payments',
              meta: { title: 'Data Pembayaran SPP', role: 'school_admin' },
              component: TuitionPaymentData,
            },
          ],
        },

        // Keuangan Utama  
        {
          path: 'finance',
          children: [
            {
              path: 'financial-core',
              name: 'admin.finance.core',
              meta: { title: 'Keuangan Utama', role: 'school_admin' },
              component: FinancialCore,
            },
          ],
        },

        // Data Kelas
        {
          path: 'classrooms',
          name: 'admin.classrooms',
          meta: { title: 'Data Kelas', role: 'school_admin' },
          component: ClassroomData,
        },

        // Data Siswa
        {
          path: 'students',
          name: 'admin.students',
          meta: { title: 'Data Siswa', role: 'school_admin' },
          component: StudentData,
        },
        {
          path: 'subjectEnrollment',
          name: 'admin.subjectEnrollment',
          meta: { title: 'Matriks Pengayaan', role: 'school_admin' },
          component: SubjectEnrollmentMatrixView,
        },

        // Data Orang Tua
        {
          path: 'parents',
          name: 'admin.parents',
          meta: { title: 'Data Orang Tua', role: 'school_admin' },
          component: ParentData,
        },

        // Data Transaksi RFID
        {
          path: 'rfid',
          name: 'admin.rfid',
          meta: { title: 'Data Transaksi RFID', role: 'school_admin' },
          component: NfcTransactionData,
        },

        // Mata Pelajaran
        {
          path: 'subjects',
          name: 'admin.subjects',
          meta: { title: 'Mata Pelajaran', role: 'school_admin' },
          component: SubjectData,
        },
        {
          path: 'schedules',
          name: 'admin.schedules',
          meta: { title: 'Jadwal Pelajaran', role: 'school_admin' },
          component: Schedule,
        },

        // Presensi
        {
          path: 'attendances',
          children: [
            {
              path: 'student',
              name: 'admin.attendances.student',
              meta: { title: 'Presensi Siswa', role: 'school_admin' },
              component: StudentAttendance,
            },
            {
              path: 'teacher',
              name: 'admin.attendances.teacher',
              meta: { title: 'Presensi Guru', role: 'school_admin' },
              component: TeacherAttendance,
            },
            {
              path: 'extracurricular',
              name: 'admin.attendances.extracurricular',
              meta: { title: 'Presensi Ekstrakurikuler', role: 'school_admin' },
              component: ExtracurricularAttendance,
            },
          ],
        },

        {
          path: 'exams',
          children: [
            {
              path: '',
              name: 'admin.exams',
              meta: { title: 'Bank Soal', role: 'school_admin' },
              component: Exam,
            },
            {
              path: 'create',
              name: 'admin.exams.create',
              meta: { title: 'Buat Soal', role: 'school_admin' },
              component: ExamFormWrapper,
            },
            {
              path: ':id/edit',
              name: 'admin.exams.edit',
              meta: { title: 'Edit Soal', role: 'school_admin' },
              component: ExamFormWrapper,
            },
            {
              path: ':id/results',
              name: 'admin.exams.results',
              meta: { title: 'Hasil Ujian', role: 'school_admin' },
              component: ExamResult,
            },
            {
              path: ':examId/student/:studentId/attempt/:attemptId',
              name: 'admin.exams.student.attempt',
              meta: { title: 'Hasil Jawaban Ujian', role: 'school_admin' },
              component: ExamResultDetail,
            },
          ],
        },
        {
          path: 'report-card',
          children: [
            {
              path: 'final-assessment',
              name: 'admin.report-card.final-assessment',
              meta: { requiresAuth: true, title: 'E-Raport', role: 'school_admin' },
              component: FinalAssessment,
            },
            {
              path: 'description',
              name: 'admin.report-card.description',
              meta: { requiresAuth: true, title: 'Deskripsi Rapor', role: 'school_admin' },
              component: ReportDescription,
            },
          ],
        },

        // Informations

        {
          path: 'informations',
          children: [
            {
              path: 'announcements',
              name: 'admin.informations.announcements',
              meta: { title: 'Pengumuman', role: 'school_admin' },
              component: InformationData,
            },
            {
              path: 'events',
              name: 'admin.informations.events',
              meta: { title: 'Acara Sekolah', role: 'school_admin' },
              component: Event,
            },
          ],
        },
        // Fasilitas
        {
          path: 'facilities',
          name: 'admin.facilities',
          meta: { title: 'Fasilitas', role: 'school_admin' },
          component: Facilities,
        },

        // E-Raport
        {
          path: 'report-cards',
          name: 'admin.report-cards',
          meta: { title: 'E-Raport', role: 'school_admin' },
          component: FinalAssessment,
        },

        //Canteens
        {
          path: 'canteens',
          children: [
            {
              path: '',
              name: 'admin.canteens',
              meta: { title: 'Kantin', role: 'school_admin' },
              component: Canteen,
            },
            {
              path: 'canteenAdmins',
              name: 'admin.canteens.canteenAdmins',
              meta: { title: 'Admin Kantin', role: 'school_admin' },
              component: CanteenAdmin,
            },
            {
              path: 'cashiers',
              name: 'admin.canteens.cashiers',
              meta: { title: 'Kasir Kantin', role: 'school_admin' },
              component: Cashier,
            },
          ],
        },
        {
          path: 'dormitories',
          meta: { title: 'Asrama', role: 'school_admin' },
          component: Dormitory,
        },
      ],
    },
    // Headmaster
    {
      path: '/headmaster',
      name: 'headmaster',
      meta: { requiresAuth: true, title: 'Dashboard', role: 'headmaster' },
      component: Admin,
      children: [
        {
          path: '',
          name: 'headmaster.dashboard',
          meta: { role: 'headmaster' },
          component: Dashboard,
        },

        // Manajemen Sekolah
        {
          path: 'schoolIdentity',
          name: 'headmaster.schoolIdentity',
          meta: { title: 'Identitas Sekolah', role: 'headmaster' },
          component: SchoolIdentity,
        },
        {
          path: 'academicYears',
          name: 'headmaster.academicYears',
          meta: { title: 'Tahun Akademik', role: 'headmaster' },
          component: AcademicYear,
        },
        {
          path: 'organization',
          name: 'headmaster.organization',
          meta: { title: 'Organisasi Sekolah', role: 'headmaster' },
          component: Organization,
        },
        {
          path: 'positions',
          name: 'headmaster.positions',
          meta: { title: 'Data Jabatan', role: 'headmaster' },
          component: PositionData,
        },
        {
          path: 'extracurriculars',
          name: 'headmaster.extracurriculars',
          meta: { title: 'Ekstrakurikuler', role: 'headmaster' },
          component: Extracurricular,
        },
        {
          path: 'studentDailyActivities',
          name: 'headmaster.studentDailyActivities',
          meta: { title: 'Kegiatan Harian Siswa', role: 'headmaster' },
          component: StudentDailyActivity,
        },

        // Artikel
        {
          path: 'categories',
          name: 'headmaster.categories',
          meta: { title: 'Kategori', role: 'headmaster' },
          component: Category,
        },
        {
          path: 'tags',
          name: 'headmaster.tags',
          meta: { title: 'Tags', role: 'headmaster' },
          component: Tags,
        },

        // Data Guru
        {
          path: 'teachers',
          name: 'headmaster.teachers',
          meta: { title: 'Data Guru', role: 'headmaster' },
          component: TeacherData,
        },
        {
          path: 'homerooms',
          name: 'headmaster.homerooms',
          meta: { title: 'Wali Kelas', role: 'headmaster' },
          component: HomeroomTeacher,
        },

        // Data SPP
        {
          path: 'tuition',
          children: [
            {
              path: 'fees',
              name: 'headmaster.tuition.fees',
              meta: { title: 'Data SPP', role: 'headmaster' },
              component: TuitionFeeData,
            },
            {
              path: 'payments',
              name: 'headmaster.tuition.payments',
              meta: { title: 'Data Pembayaran SPP', role: 'headmaster' },
              component: TuitionPaymentData,
            },
          ],
        },

        // Keuangan Utama
        {
          path: 'finance',
          children: [
            {
              path: 'financial-core',
              name: 'headmaster.finance.core',
              meta: { title: 'Keuangan Utama', role: 'headmaster' },
              component: FinancialCore,
            },
          ],
        },

        // Data Kelas
        {
          path: 'classrooms',
          name: 'headmaster.classrooms',
          meta: { title: 'Data Kelas', role: 'headmaster' },
          component: ClassroomData,
        },

        // Data Siswa
        {
          path: 'students',
          name: 'headmaster.students',
          meta: { title: 'Data Siswa', role: 'headmaster' },
          component: StudentData,
        },

        // Data Orang Tua
        {
          path: 'parents',
          name: 'headmaster.parents',
          meta: { title: 'Data Orang Tua', role: 'headmaster' },
          component: ParentData,
        },

        // Data Transaksi RFID
        {
          path: 'rfid',
          name: 'headmaster.rfid',
          meta: { title: 'Data Transaksi RFID', role: 'headmaster' },
          component: NfcTransactionData,
        },

        // Mata Pelajaran
        {
          path: 'subjects',
          name: 'headmaster.subjects',
          meta: { title: 'Mata Pelajaran', role: 'headmaster' },
          component: SubjectData,
        },
        {
          path: 'schedules',
          name: 'headmaster.schedules',
          meta: { title: 'Jadwal Pelajaran', role: 'headmaster' },
          component: Schedule,
        },

        // Presensi
        {
          path: 'attendances',
          children: [
            {
              path: 'student',
              name: 'headmaster.attendances.student',
              meta: { title: 'Presensi Siswa', role: 'headmaster' },
              component: StudentAttendance,
            },
            {
              path: 'teacher',
              name: 'headmaster.attendances.teacher',
              meta: { title: 'Presensi Guru', role: 'headmaster' },
              component: TeacherAttendance,
            },
            {
              path: 'extracurricular',
              name: 'headmaster.attendances.extracurricular',
              meta: { title: 'Presensi Ekstrakurikuler', role: 'headmaster' },
              component: ExtracurricularAttendance,
            },
          ],
        },

        {
          path: 'exams',
          children: [
            {
              path: '',
              name: 'headmaster.exams',
              component: Exam,
            },
            {
              path: 'create',
              name: 'headmaster.exams.create',
              component: ExamFormWrapper,
            },
            {
              path: ':id/edit',
              name: 'headmaster.exams.edit',
              component: ExamFormWrapper,
            },
            {
              path: ':id/results',
              name: 'headmaster.exams.results',
              component: ExamResult,
            },
            {
              path: ':examId/student/:studentId/attempt/:attemptId',
              name: 'headmaster.exams.student.attempt',
              component: ExamResultDetail,
            },
          ],
        },

        // Informations
        {
          path: 'informations',
          children: [
            {
              path: 'announcements',
              name: 'headmaster.informations.announcements',
              meta: { title: 'Pengumuman', role: 'headmaster' },
              component: InformationData,
            },
            {
              path: 'events',
              name: 'headmaster.informations.events',
              meta: { title: 'Acara Sekolah', role: 'headmaster' },
              component: Event,
            },
          ],
        },

        // Fasilitas
        {
          path: 'facilities',
          name: 'headmaster.facilities',
          meta: { title: 'Fasilitas', role: 'headmaster' },
          component: Facilities,
        },

        // E-Raport
        {
          path: 'report-cards',
          name: 'headmaster.report-cards',
          meta: { title: 'E-Raport', role: 'headmaster' },
          component: FinalAssessment,
        },

        //Canteens
        {
          path: 'canteens',
          children: [
            {
              path: '',
              name: 'headmaster.canteens',
              meta: { title: 'Kantin', role: 'headmaster' },
              component: Canteen,
            },
            {
              path: 'canteenAdmins',
              name: 'headmaster.canteens.canteenAdmins',
              meta: { title: 'Admin Kantin', role: 'headmaster' },
              component: CanteenAdmin,
            },
            {
              path: 'cashiers',
              name: 'headmaster.canteens.cashiers',
              meta: { title: 'Kasir Kantin', role: 'headmaster' },
              component: Cashier,
            },
          ],
        },
      ],
    },

    // Teacher
    {
      path: '/teacher',
      name: 'teacher',
      meta: { requiresAuth: true, title: 'Guru', role: 'teacher' },
      component: Admin,
      children: [
        {
          path: '',
          name: 'teacher.dashboard',
          meta: { requiresAuth: true, title: 'Dashboard', role: 'teacher' },
          component: DashboardTeacher,
        },
        {
          path: 'exams',
          children: [
            {
              path: '',
              name: 'teacher.exams',
              component: Exam,
            },
            {
              path: 'create',
              name: 'teacher.exams.create',
              component: ExamFormWrapper,
            },
            {
              path: ':id/edit',
              name: 'teacher.exams.edit',
              component: ExamFormWrapper,
            },
            {
              path: ':id/results',
              name: 'teacher.exams.results',
              component: ExamResult,
            },
            {
              path: ':examId/student/:studentId/attempt/:attemptId',
              name: 'teacher.exams.student.attempt',
              component: ExamResultDetail,
            },
          ],
        },
        {
          path: 'report-card',
          children: [
            {
              path: 'final-assessment',
              name: 'teacher.report-card.final-assessment',
              meta: { requiresAuth: true, title: 'E-Raport', role: 'teacher' },
              component: FinalAssessment,
            },
            {
              path: 'description',
              name: 'teacher.report-card.description',
              meta: { requiresAuth: true, title: 'Deskripsi Rapor', role: 'teacher' },
              component: ReportDescription,
            },
          ],
        },
        {
          path: 'classAttendance',
          name: 'teacher.classAttendance',
          meta: { requiresAuth: true, title: 'Kehadiran Kelas', role: 'teacher' },
          component: ClassAttendance,
        },
        {
          path: 'extracurricularEnrollment',
          name: 'teacher.extracurricularEnrollment',
          meta: {
            requiresAuth: true,
            title: 'Pendaftaran kegiatan ekstrakurikuler',
            role: 'teacher',
          },
          component: ExtracurricularEnroll,
        },
        {
          path: 'praticalRapor',
          name: 'teacher.praticalRapor',
          meta: {
            requiresAuth: true,
            title: 'PKL',
            role: 'teacher',
          },
          component: PracticalTrainingPage,
        },
        {
          path: 'attendance',
          children: [
            {
              path: 'student',
              name: 'teacher.attendance.student',
              meta: { requiresAuth: true, title: 'Attendance', role: 'teacher' },
              component: StudentAttendanceTeacher,
            },
          ],
        },
      ],
    },
    // Student
    {
      path: '/student',
      component: Student,
      children: [
        {
          path: '',
          name: 'student.dashboard',
          meta: { title: 'Dashboard Siswa' },
          component: ExamDashboard,
        },
        {
          path: 'exam/:id',
          name: 'student.exams.examRoom',
          meta: { title: 'Ruang Ujian' },
          component: ExamRoom,
        },
      ],
    },

    // Admin Kantin
    {
      path: '/canteenAdmin',
      name: 'canteenAdmin',
      meta: { requiresAuth: true, title: 'Dashboard', role: 'canteen_admin' },
      component: Admin,
      children: [
        // Dashboard
        {
          path: '',
          name: 'canteenAdmin.dashboard',
          meta: { title: 'Dashbaord Kantin Admin', role: 'canteen_admin' },
          component: DasboardCanteen,
        },
        // Product
        {
          path: 'products',
          name: 'canteenAdmin.products',
          meta: { title: 'Produk', role: 'canteen_admin' },
          component: Product,
        },
        {
          path: 'purchases',
          children: [
            {
              path: '',
              name: 'canteenAdmin.purchases',
              meta: { title: 'Stok Produk', role: 'canteen_admin' },
              component: Purchase,
            },
            {
              path: 'logs',
              name: 'canteenAdmin.purchases.logs',
              meta: { title: 'Log Stok Produk', role: 'canteen_admin' },
              component: PurchaseLog,
            },
          ],
        },
        {
          path: 'transactions',
          children: [
            {
              path: 'history',
              name: 'canteenAdmin.transactions.history',
              meta: { title: 'Transaksi', role: 'canteen_admin' },
              component: Transaction,
            },
            {
              path: 'pending',
              name: 'canteenAdmin.transactions.pending',
              meta: { title: 'Transaksi Belum bayar', role: 'canteen_admin' },
              component: TransactionPending,
            },
          ],
        },
      ],
    },
    // Kasir
    {
      path: '/cashier',
      name: 'cashier',
      meta: { requiresAuth: true, title: 'Dashboard', role: 'cashier' },
      component: Admin,
      children: [
        {
          path: '',
          name: 'cashier.dashboard',
          meta: { role: 'cashier' },
          component: DasboardCashier,
        },
        {
          path: 'products',
          name: 'cashier.products',
          meta: { title: 'Produk', role: 'cashier' },
          component: CashierProduct,
        },
        {
          path: 'transactions',
          children: [
            {
              path: 'history',
              name: 'cashier.transactions.history',
              meta: { title: 'Transaksi', role: 'cashier' },
              component: Transaction,
            },
            {
              path: 'pending',
              name: 'cashier.transactions.pending',
              meta: { title: 'Transaksi Belum Bayar', role: 'cashier' },
              component: TransactionPending,
            },
          ],
        },
      ],
    },
    // If route not found
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      meta: { title: 'Page Not Found' },
      component: PageNotFound,
    },
    {
      path: '/unauthorized',
      name: 'unauthorized',
      meta: { title: 'Unauthorized' },
      component: PageUnauthorized,
    },
  ],
})

router.beforeEach((to, _, next) => {
  const authStore = useAuthStore()
  const isAuthenticated = authStore.isAuthenticated
  const userRole = authStore.roles[0]
  const isSuperadmin = authStore.user?.isSuperadmin

  type RouteMeta = {
    role?: string | string[]
    requiresAuth?: boolean
    [key: string]: any
  }

  const mergedMeta: RouteMeta = to.matched.reduce((acc, r) => Object.assign(acc, r.meta), {})

  if (mergedMeta.requiresAuth && !isAuthenticated) {
    return next('/login')
  }

  if (mergedMeta.role === 'superadmin' && !isSuperadmin) {
    return next('/unauthorized')
  }

  if (mergedMeta.role) {
    const allowedRoles = Array.isArray(mergedMeta.role) ? mergedMeta.role : [mergedMeta.role]

    if (!authStore.roles.some((role: any) => allowedRoles.includes(role))) {
      return next('/unauthorized')
    }
  }

  if (to.path === '/login' && isAuthenticated) {
    if (isSuperadmin) {
      return next('/superadmin')
    }
    switch (userRole) {
      case 'school_admin':
        return next('/admin')
      case 'headmaster':
        return next('/headmaster')
      case 'canteen_admin':
        return next('/canteenAdmin')
      case 'cashier':
        return next('/cashier')
      case 'teacher':
        return next('/teacher')
      case 'student':
        return next('/student')
      default:
        return next('/unauthorized')
    }
  }
  next()
})

// Set the document title and scroll to top after each route change
router.afterEach((to) => {
  const title = `${to.meta.title} | TIAS`
  document.title = title as string // Set the document title
  window.scrollTo(0, 0) // Scroll to top
})

export default router
