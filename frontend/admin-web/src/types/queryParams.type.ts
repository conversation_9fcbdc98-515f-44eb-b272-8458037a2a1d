export interface QueryParams {
  page?: number
  limit?: number
  [key: string]: string | number | undefined
}

export type ExportPreset = 'students_all' | 'exam_token'
export type ExportParams = {
  preset?: ExportPreset
}
export interface ExamListParams {
  page?: number
  limit?: number
  q?: string
  title?: string
  teacherId?: number | string | null
  subjectId?: number | string | null
  classroomId?: number | string | null
  sort?: 'start_datetime' | 'end_datetime' | 'title' | 'created_at'
  order?: 'asc' | 'desc'
}
