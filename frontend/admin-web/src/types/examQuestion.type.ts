import type { QuestionType } from '@/enums/questionType.enum'

import type { ExamQuestionHasAnswerOption } from './examQuestionHasAnswerOption.type'

export interface ExamQuestion {
  id?: number
  examId?: number
  content: string
  answerKeyEssay?: string
  points?: number

  orderIndex?: number
  questionType?: QuestionType.ESSAY | QuestionType.MULTIPLE_CHOICE | string
  mediaQuestions?: File | null
  mediaQuestionsUrl?: string | null

  kdNumber?: string
  learningOutcome?: string
  competencyIndicator?: string
  levelKognitif?: string

  essayAnswer?: string
  answerOptions: ExamQuestionHasAnswerOption[]
  options?: ExamQuestionHasAnswerOption[]

  mediaAnswer?: File | null
  mediaAnswerUrl?: string | null
  isAnswered?: boolean
  selectedOptionId?: number
  answerId?: number

  createdAt?: string
  updatedAt?: string
}

export function mapExamQuestionsResponse(apiData: any[]): ExamQuestion[] {
  return (apiData ?? []).map((q) => ({
    id: q.id,
    examId: q?.examId,
    content: q?.content,
    answerKeyEssay: q?.answerKeyEssay ?? null,
    points: Number(q?.points ?? 0),

    orderIndex: q.orderIndex ?? 0,
    questionType: q?.questionType,
    mediaQuestionsUrl: q?.mediaQuestionsUrl ?? null,
    mediaQuestions: q?.mediaQuestions ?? null,

    kdNumber: q?.kdNumber ?? null,
    learningOutcome: q?.learningOutcome ?? null,
    competencyIndicator: q?.competencyIndicator ?? null,
    levelKognitif: q?.levelKognitif ?? null,

    answerOptions: (q?.answerOptions ?? []).map((opt: any) => ({
      id: opt?.id,
      examQuestionId: opt?.examQuestionId,
      content: opt?.content ?? '',
      isCorrect: !!opt?.isCorrect,
      orderIndex: opt?.orderIndex ?? 0,
      mediaAnswerOptionsUrl: opt?.mediaAnswerOptionsUrl ?? null,
      mediaAnswerOptions: null,
      createdAt: opt?.createdAt,
      updatedAt: opt?.updatedAt,
    })),
    createdAt: q?.createdAt,
    updatedAt: q?.updatedAt,
  }))
}
