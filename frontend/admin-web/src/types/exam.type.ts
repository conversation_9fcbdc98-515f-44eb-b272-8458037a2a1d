import type { ExamType } from '@/enums/examType.anum'

import type { SubClassroom } from './classroom.type'
import type { ExamAttempt } from './examAttempt.type'
import type { Subject } from './subject.type'
import type { User } from './user.type'

export interface Exam {
  id: number
  title: string
  examsType?: ExamType
  description?: string
  subClassroomSubjectId?: number
  questionsCount?: number
  startDatetime?: string
  endDatetime?: string
  passingScore?: number
  isPublished?: boolean
  isShuffled?: boolean
  subClassroom?: SubClassroom
  subject?: Subject
  teacher?: User
  status?: string
  attempt?: ExamAttempt
  totalQuestions?: number
  termId?: number

  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}
