import type { Grade } from './grade.type'
import type { SubClassroomSubject } from './subClassroomSubject.type'
import type { User } from './user.type'

export interface ReportCard {
  id: number
  termId: number
  studentUserId: number
  subClassroomSubjectId?: number
  subClassroomSubject?: SubClassroomSubject
  student: User
  finalScore?: number
  revisionScore?: number
  grade: string
  grades: Grade[]

  midTermNote?: string
  midExportedAt?: string
  midLockedScore?: number

  finalTermNote?: string
  finalExportedAt?: string
  finalLockedScore?: number

  classTeacherNote?: string
  displayScore?: number

  isEnrolled: boolean

  createdAt: string
  updatedAt?: string
}
