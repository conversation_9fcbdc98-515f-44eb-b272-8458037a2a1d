import type { Subject } from './subject.type'
import type { User } from './user.type'

export interface SubjectHeader {
  id: number
  name: string
}

export interface EnrollmentCell {
  subClassroomSubjectId: number
  subjectName?: string | null
  isEnrolled: boolean
  note?: string | null
}

export interface EnrollmentRow {
  userId: number
  studentName: string
  subjects: EnrollmentCell[]
}

export interface SubjectEnrollmentMatrix {
  termId: number
  subClassroomId: number
  students: User[]
  subjects: Subject[]
  matrix: EnrollmentRow[]
}

export interface ClassEnrollmentSummary {
  termId?: number
  subClassroomId: number
  combinations: number
  existingRows: number
  enrolledChecked: number
  percentChecked?: number
  updatedAt?: string
}
