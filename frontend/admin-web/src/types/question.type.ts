import type { QuestionType } from '@/enums/questionType.enum'

export type CompetencyPayload = {
  kdNumber?: string
  learningOutcome?: string
  competencyIndicator?: string
  levelKognitif?: string
}

export function makeBlankQuestion(examId: number, orderIndex: number, payload: CompetencyPayload) {
  return {
    id: Date.now(),
    examId,
    content: '',
    answerKeyEssay: '',
    points: 0,
    questionType: 'multiple_choice' as QuestionType,
    orderIndex,
    mediaQuestions: null as File | null,
    mediaQuestionsUrl: null as string | null,
    kdNumber: payload.kdNumber,
    learningOutcome: payload.learningOutcome,
    levelKognitif: payload.levelKognitif,
    competencyIndicator: payload.competencyIndicator,
    answerOptions: [] as any[],
    createdAt: '',
    updatedAt: '',
  }
}

export function toPendingCompetency(q: any): CompetencyPayload {
  return {
    kdNumber: q.kdNumber || '',
    learningOutcome: q.learningOutcome || '',
    competencyIndicator: q.competencyIndicator || '',
    levelKognitif: q.levelKognitif || '',
  }
}

export function patchCompetency(target: any, payload: CompetencyPayload) {
  target.kdNumber = payload.kdNumber
  target.learningOutcome = payload.learningOutcome
  target.competencyIndicator = payload.competencyIndicator
  target.levelKognitif = payload.levelKognitif
}
