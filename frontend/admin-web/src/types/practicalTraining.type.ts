import type { Term } from './term.type'
import type { User } from './user.type'

export interface PracticalTraining {
  id: number
  termId: number
  term?: Term
  studentUserId: number
  studentUser?: User
  programOfStudy?: string
  specialization?: string
  place: string
  periodStart: Date
  periodEnd: Date
  instructorName: string
  mentorName: string
  sick: number
  leave: number
  present: number
  alpha: number
  classTeacherNote: string

  createdAt: string
  updatedAt: string
}
