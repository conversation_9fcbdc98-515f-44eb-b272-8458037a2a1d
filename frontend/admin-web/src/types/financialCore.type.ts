export interface FinancialCore {
  id: number
  schoolId?: number
  studentUserId?: number
  parentUserId?: number
  transactionNumber: string
  referenceType: string
  referenceTypeLabel: string
  referenceId?: string
  debitAmount: number
  creditAmount: number
  balance: number
  formattedDebit: number
  formattedCredit: number
  formattedBalance: number
  netAmount: number
  notes?: string
  status: string
  transactionDate: string
  transactionTime?: string
  transactionTimezone?: string
  isDebit: boolean
  isCredit: boolean
  createdAt: string
  updatedAt: string
  student?: {
    id: number
    name: string
    email: string
  }
  parent?: {
    id: number
    name: string
    email: string
  }
  school?: {
    id: number
    name: string
  }
}