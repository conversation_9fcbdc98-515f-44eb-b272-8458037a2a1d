import type { AcademicYear } from './academicYear.type'
import type { SubClassroom } from './classroom.type'
import type { School } from './school.type'

export interface User {
  id: number
  name: string
  email: string
  roles: Role[]
  schools: School[]
  isActive: boolean
  isSuperadmin: boolean
  phone?: string
  address?: string
  photo?: string
  birthDate?: Date
  birthPlace?: string
  registrationNumber?: string
  religion?: string
  subClassroomId: number
  subClassroom?: SubClassroom
  schoolOrigin?: string
  academicYearId?: number
  academicYear?: AcademicYear
  lastEducation?: string
  examToken?: string
  examTokenExpiration?: string
  parent?: User[]
  children?: User[]
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: number
  name: string
  guardName: string
  createdAt: string
  updatedAt: string
}
