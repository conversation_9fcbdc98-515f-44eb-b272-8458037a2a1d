export interface School {
  id: number
  name: string
  address: string
  subdistrict: string
  district: string
  city: string
  province: string
  registrationNumber: string
  yearFounded: string
  phoneNumber: string
  email: string
  website: string
  logo: string
  foundationId: number
  schoolLevelId: number
  schoolLevel: SchoolLevel
  createdAt: string
  updatedAt: string
}

export interface SchoolLevel {
  id: number
  name: string
  slug: string
  createdAt: string
  updatedAt: string
}
