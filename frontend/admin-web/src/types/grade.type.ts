import type { GradeType } from '@/enums/gradeType.enum'

import type { Assignment } from './assignment.type'
import type { Exam } from './exam.type'
import type { ReportCard } from './reportCard.type'
import type { User } from './user.type'

export interface Grade {
  id: number
  studentUserId: number
  student: User
  assignmentId?: number
  assignment?: Assignment
  examId?: number
  exam?: Exam
  termId: number
  reportCardId: number
  reportCard?: ReportCard
  type: GradeType
  score: number
  notes?: string
  createdAt: string
  updatedAt: string
}
