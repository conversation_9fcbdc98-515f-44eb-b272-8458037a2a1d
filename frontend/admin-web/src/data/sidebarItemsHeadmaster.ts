import { PrimeIcons } from '@primevue/core/api'
import type { MenuItem } from 'primevue/menuitem'

import { useAuthStore } from '@/stores/auth'

export const menuItemsHeadmaster: MenuItem[] = [
  {
    label: 'Beranda',
    icon: PrimeIcons.HOME, // Represents the main dashboard
    route: '/headmaster',
  },
  {
    label: 'Manajemen',
    icon: PrimeIcons.COG, // General settings or management
    items: [
      {
        label: 'Identitas Sekolah',
        icon: PrimeIcons.ID_CARD,
        route: '/headmaster/schoolIdentity',
      },
      {
        label: '<PERSON><PERSON> Akademik',
        icon: PrimeIcons.CALENDAR,
        route: '/headmaster/academicYears',
      },
      {
        label: 'Organisasi Sekolah',
        icon: PrimeIcons.SITEMAP,
        route: '/headmaster/organization',
      },
      {
        label: 'Data Jabatan',
        icon: PrimeIcons.BRIEFCASE,
        route: '/headmaster/positions',
      },
      {
        label: 'Ekstrakurikuler',
        icon: PrimeIcons.USERS,
        route: '/headmaster/extracurriculars',
      },
      {
        label: '<PERSON><PERSON><PERSON>',
        icon: PrimeIcons.LIST,
        route: '/headmaster/studentDailyActivities',
      },
    ],
  },
  {
    label: 'Data SPP',
    icon: PrimeIcons.MONEY_BILL, // Represents school/class structures
    items: [
      {
        label: 'Data SPP',
        icon: PrimeIcons.MONEY_BILL,
        route: '/headmaster/tuition/fees',
      },
      {
        label: 'Data Pembayaran SPP',
        icon: PrimeIcons.MONEY_BILL,
        route: '/headmaster/tuition/payments',
      },
    ],
  },
  {
    label: 'Keuangan Utama',
    icon: PrimeIcons.CHART_LINE,
    items: [
      {
        label: 'Transaksi Keuangan',
        icon: PrimeIcons.WALLET,
        route: '/headmaster/finance/financial-core',
      },
    ],
  },
  // Informations
  {
    label: 'Informasi',
    icon: PrimeIcons.INFO_CIRCLE,
    items: [
      {
        label: 'Pengumuman',
        icon: PrimeIcons.MEGAPHONE,
        route: '/headmaster/informations/announcements',
      },
      {
        label: 'Acara',
        icon: PrimeIcons.CALENDAR,
        route: '/headmaster/informations/events',
      },
    ],
  },
  {
    label: 'Data Kelas',
    icon: PrimeIcons.BUILDING, // Represents school/class structures
    items: [
      {
        label: 'Data Kelas',
        icon: PrimeIcons.LIST,
        route: '/headmaster/classrooms',
      },
    ],
  },
  {
    label: 'Data Guru',
    icon: PrimeIcons.USERS, // Represents teachers
    items: [
      { label: 'Data Guru', icon: PrimeIcons.USER, route: '/headmaster/teachers' },
      {
        label: 'Wali Kelas',
        icon: PrimeIcons.USER_EDIT,
        route: '/headmaster/homerooms',
      },
    ],
  },
  {
    label: 'Data Siswa',
    icon: PrimeIcons.USER, // Represents students
    items: [
      {
        label: 'Data Siswa',
        icon: PrimeIcons.USERS,
        route: '/headmaster/students',
      },
      {
        label: 'Data Orang Tua',
        icon: PrimeIcons.USERS,
        route: '/headmaster/parents',
      },
      {
        label: 'Transaksi Kartu RFID',
        icon: PrimeIcons.CART_PLUS,
        route: '/headmaster/rfid',
      },
    ],
  },
  {
    label: 'Data Mata Pelajaran',
    icon: PrimeIcons.BOOK, // Represents subjects and learning materials
    items: [
      {
        label: 'Mata Pelajaran',
        icon: PrimeIcons.BOOKMARK,
        route: '/headmaster/subjects',
      },
      {
        label: 'Jadwal Pelajaran',
        icon: PrimeIcons.CALENDAR_PLUS,
        route: '/headmaster/schedules',
      },
    ],
  },
  {
    label: 'Presensi',
    icon: PrimeIcons.CALENDAR, // Represents attendance tracking
    items: [
      {
        label: 'Presensi Siswa',
        icon: PrimeIcons.CLOCK,
        route: '/headmaster/attendances/student',
      },
      {
        label: 'Presensi Guru',
        icon: PrimeIcons.CLOCK,
        route: '/headmaster/attendances/teacher',
      },
      {
        label: 'Presensi Ekstrakurikuler',
        icon: PrimeIcons.CLOCK,
        route: '/headmaster/attendances/extracurricular',
      },
    ],
  },
  {
    label: 'Fasilitas',
    icon: PrimeIcons.BUILDING,
    items: [
      {
        label: 'Asset Lancar',
        icon: PrimeIcons.LIST,
        route: '/headmaster/facilities',
      },
    ],
  },
  {
    label: 'LMS',
    icon: PrimeIcons.BOOK,
    items: [
      {
        label: 'Bank Soal',
        icon: PrimeIcons.BOOK,
        route: '/headmaster/exams',
      },
    ],
  },
  {
    label: 'Data Kantin',
    icon: PrimeIcons.BUILDING,
    items: [
      {
        label: 'Kantin',
        icon: PrimeIcons.LIST,
        route: '/headmaster/canteens',
      },
      {
        label: 'Admin',
        icon: PrimeIcons.USER,
        route: '/headmaster/canteens/canteenAdmins',
      },
      {
        label: 'Kasir',
        icon: PrimeIcons.USER,
        route: '/headmaster/canteens/cashiers',
      },
    ],
  },
  {
    label: 'Lihat Website',
    icon: PrimeIcons.GLOBE, // Represents external website access
    url: '/',
    target: '_blank', // Opens in a new tab
  },
  {
    label: 'Logout',
    icon: PrimeIcons.SIGN_OUT, // Represents user logout
    command: () => {
      const auth = useAuthStore()
      auth.logout() // Calls the logout action from the auth store
    },
    url: '/login', // Redirects to the login page
  },
]
