import { PrimeIcons } from '@primevue/core/api'
import type { MenuItem } from 'primevue/menuitem'

import { useAuthStore } from '@/stores/auth'

export const menuItemsSuperadmin: MenuItem[] = [
  {
    label: 'Dashboard',
    icon: PrimeIcons.BOX,
    route: '/superadmin',
  },
  // {
  //   label: 'Data Yayasan',
  //   icon: PrimeIcons.BOX,
  //   route: '/superadmin/foundations',
  // },
  {
    label: 'Data Yayasan',
    icon: PrimeIcons.HOME,
    route: '/superadmin/foundations',
  },
  {
    label: 'Data Sekolah',
    icon: PrimeIcons.HOME,
    route: '/superadmin/schools',
  },
  {
    label: 'Data Pengguna',
    icon: PrimeIcons.HOME,
    route: '/superadmin/users',
  },
  {
    label: 'Logout',
    icon: PrimeIcons.SIGN_OUT,
    command: () => {
      const auth = useAuthStore()
      auth.logout() // Calls the logout action from the auth store
    },
    url: '/login',
  },
]
