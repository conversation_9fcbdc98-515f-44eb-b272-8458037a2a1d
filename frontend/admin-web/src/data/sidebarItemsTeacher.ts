import { PrimeIcons } from '@primevue/core/api'
import type { MenuItem } from 'primevue/menuitem'

import { useAuthStore } from '@/stores/auth'

export const menuItemsTeachers: MenuItem[] = [
  {
    label: 'Beranda',
    icon: PrimeIcons.HOME,
    route: '/teacher',
  },
  {
    label: 'E-<PERSON>or',
    icon: PrimeIcons.BOOK,
    items: [
      {
        label: 'Deskripsi Rapor',
        icon: PrimeIcons.INFO_CIRCLE,
        route: '/teacher/report-card/description',
      },
      {
        label: '<PERSON><PERSON><PERSON>',
        icon: PrimeIcons.BOOK,
        route: '/teacher/report-card/final-assessment',
      },
    ],
  },
  {
    label: 'Kehadiran',
    icon: PrimeIcons.BOX,
    items: [
      {
        label: 'Kehadiran Siswa',
        icon: PrimeIcons.LIST,
        route: '/teacher/classAttendance',
      },
    ],
  },
  {
    label: 'Ekstrakurikular',
    icon: PrimeIcons.BOX,
    items: [
      {
        label: 'Pendaftaran ekstrakurikuler',
        icon: PrimeIcons.LIST,
        route: '/teacher/extracurricularEnrollment',
      },
    ],
  },
  {
    label: 'Rapor PKL',
    icon: PrimeIcons.BOX,
    items: [
      {
        label: 'Data PKL',
        icon: PrimeIcons.LIST,
        route: '/teacher/praticalRapor',
      },
    ],
  },
  {
    label: 'Logout',
    icon: PrimeIcons.SIGN_OUT, // Represents user logout
    command: () => {
      const auth = useAuthStore()
      auth.logout() // Calls the logout action from the auth store
    },
    url: '/login', // Redirects to the login page
  },
]
